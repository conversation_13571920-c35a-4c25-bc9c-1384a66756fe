# Flask配置
SECRET_KEY=your-secret-key-here
PORT=8080  # 应用端口号，默认8080避免与macOS AirPlay冲突

# OCR配置
OCR_ENGINE=tesseract
TESSERACT_CMD=tesseract
#OCR_LANGUAGE=chi_sim+eng  # 简体中文+英文
OCR_LANGUAGE=chi_sim  # 简体中文+英文

# PaddleOCR配置
PADDLE_USE_GPU=false
PADDLE_LANG=ch
PADDLE_USE_ANGLE_CLS=true
# 如果需要使用自定义模型目录，取消下面的注释并设置路径
#PADDLE_MODELS_BASE_DIR=/path/to/ocr/models
#PADDLE_DET_MODEL_DIR=/path/to/ocr/models/ch_PP-OCRv4_det_server_infer
#PADDLE_REC_MODEL_DIR=/path/to/ocr/models/ch_PP-OCRv4_rec_server_infer
#PADDLE_CLS_MODEL_DIR=/path/to/ocr/models/ch_ppocr_mobile_v2.0_cls_infer

# 百度云OCR配置
# 要使用百度云OCR，请设置OCR_ENGINE=baidu并配置以下参数
#BAIDU_OCR_APP_ID=your-app-id-here
#BAIDU_OCR_API_KEY=your-api-key-here
#BAIDU_OCR_SECRET_KEY=your-secret-key-here
#BAIDU_OCR_LANGUAGE_TYPE=CHN_ENG  # 可选：CHN_ENG(中英文)、ENG(英文)、JAP(日语)等

# 大模型API配置
LLM_API_URL=https://api.openai.com/v1/chat/completions
LLM_API_KEY=your-api-key-here
LLM_MODEL=gpt-3.5-turbo

# 分类模型配置
# 如果不设置，则使用上面的通用配置
CLASSIFICATION_MODEL=gpt-3.5-turbo
# 如果需要为分类使用不同的API供应商，设置以下参数
CLASSIFICATION_API_URL=https://api-alternative-provider.com/v1/chat/completions
CLASSIFICATION_API_KEY=your-classification-api-key-here

# LLM请求参数配置
# 主要处理参数
LLM_TEMPERATURE=0.3
LLM_MAX_TOKENS=4000
LLM_TOP_P=0.9

# 分类处理参数
CLASSIFICATION_TEMPERATURE=0.3
CLASSIFICATION_MAX_TOKENS=500
CLASSIFICATION_TOP_P=0.9

# LLM响应处理配置
# 是否保留thinking/think标签，默认为false
INCLUDE_THINKING_TAG=false

# 复选框合并处理配置
# 是否合并多个栏目的复选框到单个LLM请求中，减少API调用次数，默认为false
COMBINE_CHECKBOXES=false

# 复选框批量处理配置
# 是否启用批量并发处理模式，默认为false（顺序处理）
PROCESS_CHECKBOX_BATCH=false
# 并发处理的最大线程数，默认为10
PROCESS_THREAD=10

# 二维码URL配置
# 是否使用外部URL (true/false)，启用后二维码将使用EXTERNAL_URL而不是localhost
USE_EXTERNAL_URL=False
# 外部访问URL，例如 https://example.com 或 http://*************:8080
EXTERNAL_URL=http://your-public-ip:8080
