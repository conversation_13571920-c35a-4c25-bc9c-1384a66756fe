import os
import uuid
import threading
import time
import json
import logging
from flask import Flask, render_template, request, redirect, url_for, jsonify, send_from_directory, abort, session
from werkzeug.utils import secure_filename
from werkzeug.security import safe_join
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
import base64
import qrcode
from io import BytesIO

from config import Config
from ocr import OCRFactory
from llm import LLMProcessor
from document import DocumentGenerator
from services.printer import printer_service

# import geoip2.database  # 已禁用GeoIP功能
import ipaddress

# 初始化Flask应用
app = Flask(__name__)
app.config.from_object(Config)
Config.init_app(app)
app.secret_key = os.environ.get('SECRET_KEY', 'default_secret_key')

# 配置全局日志级别
log_level = getattr(logging, Config.LOG_LEVEL, logging.INFO)
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 设置Flask应用的日志级别
app.logger.setLevel(log_level)

print(f"日志级别设置为: {Config.LOG_LEVEL}")

# 初始化 Socket.IO，增加最大文件大小限制
socketio = SocketIO(
    app,
    cors_allowed_origins="*",
    max_http_buffer_size=50 * 1024 * 1024  # 设置为50MB
)

# 设置默认OCR引擎为百度OCR
app.config['OCR_ENGINE'] = 'baidu'

# 允许访问的 IP 地址列表
ALLOWED_IPS = [
    # 区域网地址
    '127.0.0.1',
    '***************',
    '************',
    '**************'
    # 上海地址
]

ALLOWED_IP_RANGES = [
    # ***********/24
]

ALLOWED_PROVINCES = ['Shanghai', 'Anhui', 'Fuyang']
ALLOWED_ISP = 'China Unicom'

# 可能的 GeoLite2 数据库路径列表 - 已禁用
# POSSIBLE_CITY_DB_PATHS = [
#     '/opt/homebrew/var/GeoIP/GeoLite2-City.mmdb',  # macOS
#     '/var/lib/GeoIP/GeoLite2-City.mmdb'            # Linux
# ]
# POSSIBLE_ASN_DB_PATHS = [
#     '/opt/homebrew/var/GeoIP/GeoLite2-ASN.mmdb',  # macOS
#     '/var/lib/GeoIP/GeoLite2-ASN.mmdb'            # Linux
# ]

# def load_geoip_reader(db_path):
#     """尝试加载 GeoLite2 数据库"""
#     for path in db_path:
#         try:
#             # 尝试加载数据库
#             return geoip2.database.Reader(path)
#         except FileNotFoundError:
#             continue
#     raise FileNotFoundError("GeoLite2 数据库未找到，请检查路径设置。")

# 已禁用GeoIP功能
# city_reader = load_geoip_reader(POSSIBLE_CITY_DB_PATHS)
# asn_reader = load_geoip_reader(POSSIBLE_ASN_DB_PATHS)

def is_ip_allowed(ip):
    """检查 IP 是否在允许列表中"""
    # 检查精确匹配
    if ip in ALLOWED_IPS:
        return True

    # 检查 CIDR 范围
    try:
        ip_obj = ipaddress.ip_address(ip)
        for allowed_range in ALLOWED_IP_RANGES:
            if ip_obj in ipaddress.ip_network(allowed_range):
                return True
    except:
        pass

    return False

def get_ip_info(ip):
    """使用 GeoLite2 获取 IP 地址的地理信息和 ISP 信息 - 已禁用"""
    # GeoIP功能已禁用，直接返回None
    return None, None
    # 原始代码已注释
    # try:
    #     city_response = city_reader.city(ip)
    #     asn_response = asn_reader.asn(ip)
    #     province = city_response.subdivisions.most_specific.name
    #     isp = asn_response.autonomous_system_organization
    #     return province, isp
    # except geoip2.errors.AddressNotFoundError:
    #     return None, None

@app.before_request
def check_access():
    """在每个请求前检查 IP 地址和地理信息"""
    client_ip = request.remote_addr
    if request.headers.get('X-Forwarded-For'):
        client_ip = request.headers.get('X-Forwarded-For').split(',')[0].strip()

    # 检查 IP 是否在允许列表中
    if is_ip_allowed(client_ip):
        return

    # GeoIP功能已禁用，允许所有IP访问
    return

    # 以下代码已禁用
    # # 获取 IP 的地理信息和 ISP 信息
    # province, isp = get_ip_info(client_ip)
    # if province or isp:
    #     # 检查省份和 ISP
    #     if province in ALLOWED_PROVINCES or "China Unicom" in isp:
    #         return
    #
    # # 如果不符合条件，拒绝访问
    # return jsonify({
    #     'error': 'Access Denied',
    #     'message': f'{client_ip} is not allowed to access this service.'
    # }), 403


# 在非请求上下文中生成URL的配置
# 注意：在运行时不设置SERVER_NAME，因为它会影响请求路由
# 代替的，我们使用直接构造URL的方式

# 添加CORS支持
@app.after_request
def add_cors_headers(response):
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

# 定义全局处理状态
app.config['PROCESSING_STATUS'] = {
    'status': '就绪',
    'message': '等待处理'
}

# 当前正在处理的任务
app.config['CURRENT_TASK'] = None

# 处理任务的线程
processing_thread = None

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 确保下载目录存在
download_dir = os.path.join(app.static_folder, 'downloads')
os.makedirs(download_dir, exist_ok=True)

# 确保模板目录存在
os.makedirs(app.config['TEMPLATES_DIR'], exist_ok=True)

# 将配置中的模板映射和案件类型列表加入到app.config
app.config['TEMPLATE_MAPPING'] = Config.TEMPLATE_MAPPING
app.config['CASE_TYPES'] = Config.CASE_TYPES

# 批量处理相关的全局变量
app.config['BATCH_TASKS'] = {}  # 存储批量处理任务的状态
app.config['BATCH_RESULTS'] = {}  # 存储批量处理的结果

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    # 添加详细日志
    print(f"\n=== 检查文件是否允许上传 ===\n文件名: '{filename}'")

    # 检查文件名是否为空
    if not filename:
        print("错误: 文件名为空")
        return False

    # 检查文件名是否包含扩展名
    if '.' not in filename:
        print(f"错误: 文件名中没有扩展名: '{filename}'")
        return False

    # 提取扩展名
    try:
        ext = filename.rsplit('.', 1)[1].lower()
        print(f"文件扩展名: '{ext}'")
    except IndexError:
        print(f"错误: 无法提取扩展名从: '{filename}'")
        return False

    # 检查扩展名是否为空
    if not ext:
        print("错误: 文件扩展名为空")
        return False

    # 检查扩展名是否在允许的列表中
    allowed = ext in app.config['ALLOWED_EXTENSIONS']
    print(f"允许的扩展名: {app.config['ALLOWED_EXTENSIONS']}")
    print(f"扩展名 '{ext}' 是否允许: {allowed}")

    return allowed

def process_filename(original_filename):
    """处理文件名，确保安全并保留扩展名"""
    # 确保文件名安全
    safe_filename = secure_filename(original_filename)
    print(f"安全处理后的文件名: {safe_filename}")

    # 确保文件名中包含扩展名
    if '.' not in safe_filename and '.' in original_filename:
        # 如果安全处理后丢失了扩展名，尝试恢复
        ext = original_filename.rsplit('.', 1)[1].lower()
        print(f"从原始文件名提取的扩展名: {ext}")
        safe_filename = f"{safe_filename}.{ext}"

    # 生成唯一的文件名
    unique_id = uuid.uuid4().hex
    final_filename = f"{unique_id}_{safe_filename}"

    return final_filename

def select_template(formatted_text, classification_mode, template_type):
    """选择适当的模板路径"""
    # 初始化变量
    template_path = app.config['DEFAULT_TEMPLATE_PATH']
    template_selected = False
    selected_template_type = None

    # 如果是手动模式且指定了模板文件名
    if classification_mode == "manual" and template_type:
        template_filename = template_type
        custom_template_path = os.path.join(app.config['TEMPLATES_DIR'], template_filename)
        print(f"手动选择的模板文件: {template_filename}")

        if os.path.exists(custom_template_path):
            template_path = custom_template_path
            print(f"使用模板: {template_filename}")
            template_selected = True

            # 将选择的模板文件名添加到格式化文本中
            if isinstance(formatted_text, dict):
                formatted_text['template_filename'] = template_filename
                # 确保模板类型也被设置，这可能是后续处理需要的
                formatted_text['template_type'] = template_filename.replace('.docx', '')

            # 手动模式下，直接返回选择的模板，不再尝试自动检测
            return template_path, formatted_text
        else:
            print(f"模板文件不存在: {custom_template_path}, 尝试使用自动检测的模板")

    # 如果手动模式未成功选择模板或是自动模式，且LLM返回了模板类型
    if (not template_selected) and isinstance(formatted_text, dict) and 'template_type' in formatted_text:
        selected_template_type = formatted_text['template_type']
        print(f"LLM检测到的模板类型: {selected_template_type}")

        # 强制将LLM检测到的模板类型保存到会话中，以便于调试
        app.config['DETECTED_TEMPLATE_TYPE'] = selected_template_type

        # 尝试从映射中获取模板文件名
        if selected_template_type in app.config['TEMPLATE_MAPPING']:
            template_filename = app.config['TEMPLATE_MAPPING'][selected_template_type]
            custom_template_path = os.path.join(app.config['TEMPLATES_DIR'], template_filename)

            # 保存模板文件名到会话中
            app.config['SELECTED_TEMPLATE_FILENAME'] = template_filename

            if os.path.exists(custom_template_path):
                template_path = custom_template_path
                template_selected = True
                formatted_text['template_filename'] = template_filename
            else:
                # 尝试模糊匹配
                available_templates = [f for f in os.listdir(app.config['TEMPLATES_DIR']) if f.endswith('.docx')]
                for template_file in available_templates:
                    if template_filename.lower() in template_file.lower():
                        template_path = os.path.join(app.config['TEMPLATES_DIR'], template_file)
                        template_selected = True
                        formatted_text['template_filename'] = template_file
                        break
        else:
            # 尝试直接使用模板类型名称查找匹配的模板文件
            available_templates = [f for f in os.listdir(app.config['TEMPLATES_DIR']) if f.endswith('.docx')]
            for template_file in available_templates:
                if selected_template_type.lower() in template_file.lower():
                    template_path = os.path.join(app.config['TEMPLATES_DIR'], template_file)
                    template_selected = True
                    formatted_text['template_filename'] = template_file
                    break

    # 确保模板存在，如果不存在则返回None，提示用户手动选择
    if not os.path.exists(template_path) or not template_selected:
        print(f"未找到合适的模板，请用户手动选择")
        # 获取可用模板列表
        available_templates = [f for f in os.listdir(app.config['TEMPLATES_DIR']) if f.endswith('.docx')]

        if not available_templates:
            raise FileNotFoundError(f"templates目录中没有可用的模板文件")

        # 返回None表示需要手动选择
        return None, formatted_text

    return template_path, formatted_text

@app.route('/')
def index():
    """网站首页 - 单诉状处理界面"""
    # 生成用户ID
    session['user_id'] = str(uuid.uuid4())

    # 获取默认OCR引擎
    default_ocr_engine = app.config.get('OCR_ENGINE', 'tesseract').lower()

    return render_template('index.html',
                          session_id=session.get('user_id', ''),
                          default_ocr_engine=default_ocr_engine)

@app.route('/touchscreen')
def touchscreen():
    """触摸屏首页"""
    # 生成用户ID
    session['user_id'] = str(uuid.uuid4())

    # 获取默认OCR引擎
    default_ocr_engine = app.config.get('OCR_ENGINE', 'tesseract').lower()
    return render_template('touchscreen.html',
                          session_id=session.get('user_id', ''),
                          default_ocr_engine=default_ocr_engine)

@app.route('/calculator')
def calculator():
    """司法计算器"""
    return render_template('calculator.html')

@app.route('/calculator/<path:filename>')
def calculator_static(filename):
    """司法计算器静态文件"""
    return send_from_directory('calculator', filename)

@app.route('/mediator')
def mediator():
    """多元调解平台"""
    return render_template('mediator.html')

@app.route('/status')
def get_status():
    """获取当前处理状态"""
    # 添加日志输出以便调试
    print(f"Status API called, current status: {app.config['PROCESSING_STATUS']}")

    # 创建状态响应数据
    status_data = app.config['PROCESSING_STATUS'].copy()

    # 如果状态为"完成"，添加下载链接
    if status_data.get('status') == '完成' and 'LAST_RESULT' in app.config and app.config['LAST_RESULT']:
        last_result = app.config['LAST_RESULT']
        status_data['download_url'] = last_result.get('download_url')
        status_data['filename'] = os.path.basename(last_result.get('download_url', '')) if last_result.get('download_url') else None
        print(f"添加下载链接到状态响应: {status_data['download_url']}")

    # 如果状态为"文档编辑"，确保edit_url被包含在响应中
    if status_data.get('status') == '文档编辑' and 'edit_url' in status_data:
        print(f"添加编辑链接到状态响应: {status_data['edit_url']}")

    # 添加时间戳防止缓存
    response = jsonify(status_data)
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    response.headers['Last-Modified'] = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime())
    return response

@app.route('/api/confirm-text', methods=['POST'])
def api_confirm_text():
    """API端点：处理用户确认的文字内容"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        action = data.get('action', 'confirm')
        confirmed_text = data.get('confirmed_text', '').strip()

        # 获取确认数据
        confirmation_key = f'CONFIRMATION_DATA_{task_id}'
        if confirmation_key not in app.config:
            return jsonify({'error': '任务不存在或已过期'}), 404

        confirmation_data = app.config[confirmation_key]

        if action == 'back':
            # 用户选择返回首页
            # 清理确认数据
            del app.config[confirmation_key]
            # 重置处理状态
            app.config['PROCESSING_STATUS'] = {
                'status': '就绪',
                'message': '等待处理'
            }
            return jsonify({'success': True, 'message': '已取消处理'})

        if action == 'confirm':
            if not confirmed_text:
                return jsonify({'error': '文字内容不能为空'}), 400

            # 更新确认数据中的文字内容
            confirmation_data['ocr_text'] = confirmed_text

            # 启动LLM处理线程
            global processing_thread
            processing_thread = threading.Thread(
                target=continue_processing_after_confirmation,
                args=(confirmation_data,)
            )
            processing_thread.daemon = True
            processing_thread.start()

            return jsonify({
                'success': True,
                'message': '文字内容已确认，正在继续处理'
            })

        return jsonify({'error': '无效的操作'}), 400

    except Exception as e:
        print(f"API confirm text error: {str(e)}")
        return jsonify({'error': f'处理错误: {str(e)}'}), 500

# 删除测试状态路由

def generate_complete_preview_data(formatted_text, template_path, party_info):
    """
    生成完整的预览数据，包括当事人模板处理后的内容

    Args:
        formatted_text (dict): LLM处理的格式化文本
        template_path (str): 模板文件路径
        party_info (dict): 当事人信息

    Returns:
        dict: 包含完整内容的预览数据
    """
    try:
        print("开始生成完整的预览数据...")
        print(f"输入参数 - 格式化文本字段数: {len(formatted_text)}, 当事人信息字段数: {len(party_info)}")

        # 创建中间文档处理器
        from document.intermediate_processor import IntermediateDocumentProcessor
        intermediate_processor = IntermediateDocumentProcessor(
            template_path=template_path,
            debug=True
        )

        # 生成包含当事人信息的中间文档
        print("正在生成中间文档...")
        intermediate_doc, remaining_placeholders = intermediate_processor.create_intermediate_document(party_info)
        print(f"中间文档生成完成，剩余占位符数量: {len(remaining_placeholders)}")

        # 保存中间文档到临时文件
        import tempfile
        import os
        temp_dir = tempfile.mkdtemp()
        intermediate_doc_path = os.path.join(temp_dir, 'intermediate_doc.docx')
        intermediate_doc.save(intermediate_doc_path)
        print(f"中间文档已保存到: {intermediate_doc_path}")

        # 从中间文档提取完整的文本内容
        print("正在提取中间文档文本内容...")
        complete_text_content = intermediate_processor.get_document_text(intermediate_doc)
        print(f"中间文档文本内容长度: {len(complete_text_content)} 字符")

        # 获取文档结构信息
        doc_structure = intermediate_processor.get_document_structure_info(intermediate_doc)
        print(f"文档结构信息: {doc_structure}")

        # 在生成表单之前处理CHECKBOX
        print("开始在表单生成前处理CHECKBOX...")

        # 更新状态为CHECKBOX处理
        app.config['PROCESSING_STATUS'] = {
            'status': '处理复选框',
            'message': '正在分析文档中的复选框'
        }
        print(f"CHECKBOX processing, status set to: {app.config['PROCESSING_STATUS']}")

        # 获取OCR原始文本 - 从app.config中获取最新的OCR文本
        ocr_text_for_checkbox = app.config.get('LAST_OCR_TEXT', '')
        if not ocr_text_for_checkbox:
            # 尝试从formatted_text中获取
            ocr_text_for_checkbox = formatted_text.get('ocr_original_text', '')

        print(f"传递给CHECKBOX处理的OCR文本长度: {len(ocr_text_for_checkbox)}")

        checkbox_processed_data = process_checkbox_before_form(intermediate_doc, formatted_text, template_path, ocr_text_for_checkbox)
        print(f"CHECKBOX处理完成，处理后数据字段数: {len(checkbox_processed_data)}")

        # 恢复状态为生成文档
        app.config['PROCESSING_STATUS'] = {
            'status': '生成文档',
            'message': '正在生成Word文档'
        }

        # 合并数据：当事人信息 + LLM处理结果 + CHECKBOX处理结果
        complete_preview_data = {}

        # 1. 添加当事人信息
        complete_preview_data.update(party_info)
        print(f"已添加当事人信息，字段数: {len(party_info)}")

        # 2. 添加LLM处理的其他字段
        complete_preview_data.update(formatted_text)
        print(f"已添加LLM处理结果，总字段数: {len(complete_preview_data)}")

        # 3. 添加CHECKBOX处理结果
        complete_preview_data.update(checkbox_processed_data)
        print(f"已添加CHECKBOX处理结果，总字段数: {len(complete_preview_data)}")

        # 4. 添加完整的文档内容用于预览
        complete_preview_data['_complete_document_text'] = complete_text_content
        complete_preview_data['_remaining_placeholders'] = remaining_placeholders
        complete_preview_data['_intermediate_doc_structure'] = doc_structure
        complete_preview_data['_intermediate_doc_path'] = intermediate_doc_path

        # 4. 确保模板类型信息正确
        if 'template_type' in formatted_text:
            complete_preview_data['template_type'] = formatted_text['template_type']

        print(f"完整预览数据生成成功，包含 {len(complete_preview_data)} 个字段")
        print(f"当事人信息字段: {[k for k in complete_preview_data.keys() if '当事人' in k or '原告' in k or '被告' in k or '第三人' in k]}")
        print(f"剩余占位符数量: {len(remaining_placeholders)}")
        print(f"是否包含完整文档内容: {'_complete_document_text' in complete_preview_data}")

        return complete_preview_data

    except Exception as e:
        print(f"生成完整预览数据时出错: {str(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        # 如果出错，返回原始的formatted_text
        return formatted_text

def _replace_placeholders_in_doc(doc, data):
    """
    在文档中替换占位符

    Args:
        doc: Word文档对象
        data: 包含占位符替换数据的字典
    """
    import re

    print(f"📍 [占位符替换] 开始替换，数据字段数: {len(data)}")

    # 处理段落中的占位符
    for paragraph in doc.paragraphs:
        if '{{' in paragraph.text and '}}' in paragraph.text:
            original_text = paragraph.text
            new_text = original_text

            # 查找所有占位符
            placeholders = re.findall(r'\{\{([^}]+)\}\}', original_text)
            for placeholder in placeholders:
                placeholder_key = placeholder.strip()
                if placeholder_key in data:
                    replacement_value = str(data[placeholder_key])
                    new_text = new_text.replace(f'{{{{{placeholder_key}}}}}', replacement_value)
                    print(f"📍 [占位符替换] 段落: {placeholder_key} -> {replacement_value[:30]}...")

            # 更新段落文本
            if new_text != original_text:
                paragraph.clear()
                paragraph.add_run(new_text)

    # 处理表格中的占位符
    for table in doc.tables:
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    if '{{' in paragraph.text and '}}' in paragraph.text:
                        original_text = paragraph.text
                        new_text = original_text

                        # 查找所有占位符
                        placeholders = re.findall(r'\{\{([^}]+)\}\}', original_text)
                        for placeholder in placeholders:
                            placeholder_key = placeholder.strip()
                            if placeholder_key in data:
                                replacement_value = str(data[placeholder_key])
                                new_text = new_text.replace(f'{{{{{placeholder_key}}}}}', replacement_value)
                                print(f"📍 [占位符替换] 表格: {placeholder_key} -> {replacement_value[:30]}...")

                        # 更新段落文本
                        if new_text != original_text:
                            paragraph.clear()
                            paragraph.add_run(new_text)

def process_checkbox_before_form(intermediate_doc, formatted_text, template_path, ocr_original_text=''):
    """
    在表单生成前处理CHECKBOX，获取LLM解析的原始数据

    Args:
        intermediate_doc: 中间文档对象
        formatted_text: LLM处理的格式化文本
        template_path: 模板路径
        ocr_original_text: OCR原始文本

    Returns:
        dict: 包含CHECKBOX处理结果的数据字典
    """
    try:
        print("开始在表单生成前处理CHECKBOX...")

        # 使用传入的OCR原始文本，如果没有则尝试从其他来源获取
        if not ocr_original_text:
            # 1. 从formatted_text中获取
            if 'ocr_original_text' in formatted_text and formatted_text['ocr_original_text']:
                ocr_original_text = formatted_text['ocr_original_text']
                print(f"从formatted_text获取OCR原始文本，长度: {len(ocr_original_text)}")

            # 2. 从app.config中获取
            elif 'OCR_ORIGINAL_TEXT' in app.config and app.config['OCR_ORIGINAL_TEXT']:
                ocr_original_text = app.config['OCR_ORIGINAL_TEXT']
                print(f"从app.config获取OCR原始文本，长度: {len(ocr_original_text)}")

            # 3. 尝试从其他字段获取
            elif 'original_text' in formatted_text and formatted_text['original_text']:
                ocr_original_text = formatted_text['original_text']
                print(f"从original_text字段获取OCR原始文本，长度: {len(ocr_original_text)}")
        else:
            print(f"使用传入的OCR原始文本，长度: {len(ocr_original_text)}")

        print(f"最终OCR原始文本长度: {len(ocr_original_text)}, 前100字符: {ocr_original_text[:100] if ocr_original_text else '(空)'}")

        # 创建CHECKBOX处理配置
        checkbox_config = {
            'use_llm_for_checkbox': app.config.get('USE_LLM_FOR_CHECKBOX', True),
            'process_checkboxes': app.config.get('PROCESS_CHECKBOXES', True),
            'combine_checkboxes': app.config.get('COMBINE_CHECKBOXES', False),
            'combine_checkboxes_by': app.config.get('COMBINE_CHECKBOXES_BY', 'fulltext'),
            'process_checkbox_batch': app.config.get('PROCESS_CHECKBOX_BATCH', False),
            'llm_api_url': app.config.get('CHECKBOX_LLM_API_URL') or app.config.get('LLM_API_URL', ''),
            'llm_api_key': app.config.get('CHECKBOX_LLM_API_KEY') or app.config.get('LLM_API_KEY', ''),
            'llm_model': app.config.get('CHECKBOX_LLM_MODEL') or app.config.get('LLM_MODEL', ''),
            'llm_temperature': app.config.get('CHECKBOX_LLM_TEMPERATURE') or app.config.get('LLM_TEMPERATURE', 0.3),
            'llm_max_tokens': app.config.get('CHECKBOX_LLM_MAX_TOKENS') or app.config.get('LLM_MAX_TOKENS', 1000),
            'llm_top_p': app.config.get('CHECKBOX_LLM_TOP_P') or app.config.get('LLM_TOP_P', 0.9),
            'case_type_keywords': app.config.get('CASE_TYPE_KEYWORDS', {}),
            'template_mapping': app.config.get('TEMPLATE_MAPPING', {}),
            'ocr_original_text': ocr_original_text,  # 使用获取到的OCR原始文本
            'debug_mode': app.config.get('DEBUG', True)
        }

        print(f"CHECKBOX处理配置: {checkbox_config}")

        # 检查是否启用CHECKBOX处理
        if not checkbox_config.get('process_checkboxes', False):
            print("CHECKBOX处理未启用，跳过")
            return {}

        # 导入CHECKBOX处理器
        try:
            from document.checkbox import CheckboxProcessor
        except ImportError:
            print("警告: CHECKBOX处理模块未找到，跳过CHECKBOX处理")
            return {}

        # 创建CHECKBOX处理器
        checkbox_processor = CheckboxProcessor(formatted_text, checkbox_config)

        # 创建中间文档的副本用于CHECKBOX处理
        import tempfile
        import os
        from docx import Document

        temp_dir = tempfile.mkdtemp()
        temp_doc_path = os.path.join(temp_dir, 'checkbox_processing.docx')
        intermediate_doc.save(temp_doc_path)

        # 加载副本进行处理
        checkbox_doc = Document(temp_doc_path)

        # 在CHECKBOX处理前，先替换文档中的占位符
        print("📍 [CHECKBOX前处理] 开始替换文档中的占位符...")
        _replace_placeholders_in_doc(checkbox_doc, formatted_text)
        print("📍 [CHECKBOX前处理] 占位符替换完成")

        # 处理CHECKBOX
        print("开始处理文档中的CHECKBOX...")

        if checkbox_config.get('combine_checkboxes', False):
            print("使用合并模式处理CHECKBOX")
            checkbox_processor.process_document_checkboxes(checkbox_doc)
        else:
            # 检查是否启用批量处理
            process_checkbox_batch = checkbox_config.get('process_checkbox_batch', False)

            if process_checkbox_batch:
                print("使用传统模式 + 批量处理CHECKBOX")
                _process_checkboxes_batch_mode(checkbox_processor, checkbox_doc)
            else:
                print("使用传统模式 + 顺序处理CHECKBOX")
                _process_checkboxes_sequential_mode(checkbox_processor, checkbox_doc)

        # 提取CHECKBOX处理结果
        checkbox_results = extract_checkbox_results(checkbox_processor, checkbox_doc)
        print(f"CHECKBOX处理完成，提取到 {len(checkbox_results)} 个结果")

        return checkbox_results

    except Exception as e:
        print(f"CHECKBOX处理时出错: {str(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return {}

def _process_checkboxes_batch_mode(checkbox_processor, doc):
    """批量模式处理CHECKBOX"""
    print("📍 [表单前CHECKBOX] 使用批量模式处理")

    # 收集所有包含复选框的段落和表格行
    checkbox_items = []

    # 处理所有段落中的复选框
    for paragraph in doc.paragraphs:
        if _has_checkboxes(paragraph):
            checkbox_items.append({
                'type': 'paragraph',
                'paragraph': paragraph,
                'table_context': None
            })

    # 处理表格中的复选框
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                table_context = {
                    'table_idx': table_idx,
                    'row_idx': row_idx,
                    'cell_idx': cell_idx
                }
                for para in cell.paragraphs:
                    if _has_checkboxes(para):
                        checkbox_items.append({
                            'type': 'table_cell',
                            'paragraph': para,
                            'table_context': table_context
                        })

    print(f"📍 [表单前CHECKBOX] 找到 {len(checkbox_items)} 个包含复选框的段落")

    if checkbox_items:
        # 使用批量处理
        _handle_checkboxes_batch_for_form(checkbox_processor, checkbox_items, doc)
    else:
        print("📍 [表单前CHECKBOX] 没有找到需要处理的复选框")

def _process_checkboxes_sequential_mode(checkbox_processor, doc):
    """顺序模式处理CHECKBOX"""
    print("📍 [表单前CHECKBOX] 使用顺序模式处理")

    # 处理所有段落中的复选框
    for paragraph in doc.paragraphs:
        if _has_checkboxes(paragraph):
            checkbox_processor.process_checkboxes(paragraph, None)

    # 处理表格中的复选框
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                table_context = {
                    'table_idx': table_idx,
                    'row_idx': row_idx,
                    'cell_idx': cell_idx
                }
                for para in cell.paragraphs:
                    if _has_checkboxes(para):
                        checkbox_processor.process_checkboxes(para, table_context)

def _handle_checkboxes_batch_for_form(checkbox_processor, checkbox_items, doc):
    """为表单生成处理批量复选框"""
    print(f"📍 [表单前CHECKBOX-BATCH] 开始批量处理 {len(checkbox_items)} 个复选框项目")

    try:
        # 构建表格行数据
        table_rows_with_checkboxes = {}

        for i, item in enumerate(checkbox_items):
            paragraph = item['paragraph']
            table_context = item['table_context']

            # 为每个项目创建一个唯一的行ID
            if table_context:
                row_id = f"table_{table_context['table_idx']}_row_{table_context['row_idx']}_cell_{table_context['cell_idx']}"
                first_column = ""  # 可以根据需要获取第一列内容
            else:
                row_id = f"paragraph_{i}"
                first_column = ""

            row_text = paragraph.text
            full_text = row_text

            # 计算复选框数量
            checkbox_chars = ['☐', '□', '■', '☑', '☒', '✓', '✔']
            checkbox_count = sum(paragraph.text.count(char) for char in checkbox_chars)

            if checkbox_count > 0:
                table_rows_with_checkboxes[row_id] = {
                    'table_idx': table_context['table_idx'] if table_context else 0,
                    'row_idx': table_context['row_idx'] if table_context else i,
                    'first_column': first_column,
                    'row_text': row_text,
                    'full_text': full_text,
                    'checkbox_count': checkbox_count,
                    'paragraph': paragraph,
                    'table_context': table_context
                }

        print(f"📍 [表单前CHECKBOX-BATCH] 构建了 {len(table_rows_with_checkboxes)} 个表格行数据")

        if table_rows_with_checkboxes:
            # 设置处理器的table_row_runs
            checkbox_processor.table_row_runs = {}
            for row_id, row_info in table_rows_with_checkboxes.items():
                checkbox_processor.table_row_runs[row_id] = {
                    'table_idx': row_info['table_idx'],
                    'row_idx': row_info['row_idx'],
                    'first_column': row_info['first_column'],
                    'row_text': row_info['row_text'],
                    'full_text': row_info['full_text'],
                    'runs': []
                }

            # 调用批量处理
            print("📍 [表单前CHECKBOX-BATCH] 调用 process_document_checkboxes 处理文档")
            checkbox_processor.process_document_checkboxes(doc)
        else:
            print("📍 [表单前CHECKBOX-BATCH] 没有有效的复选框数据需要处理")

    except Exception as e:
        print(f"📍 [表单前CHECKBOX-BATCH] 批量处理失败: {str(e)}")
        # 回退到顺序处理
        print("📍 [表单前CHECKBOX-BATCH] 回退到顺序处理")
        for item in checkbox_items:
            checkbox_processor.process_checkboxes(item['paragraph'], item['table_context'])

def _has_checkboxes(paragraph):
    """检查段落是否包含复选框字符"""
    checkbox_chars = ['☐', '□', '■', '☑', '☒', '✓', '✔']
    return any(char in paragraph.text for char in checkbox_chars)

def extract_checkbox_results(checkbox_processor, doc):
    """
    从CHECKBOX处理器中提取处理结果

    Args:
        checkbox_processor: CHECKBOX处理器实例
        doc: 处理后的文档

    Returns:
        dict: CHECKBOX处理结果
    """
    try:
        results = {}

        print("📍 [CHECKBOX结果提取] 开始提取CHECKBOX处理结果...")

        # 1. 尝试从处理器的结果属性中获取
        if hasattr(checkbox_processor, 'checkbox_results') and checkbox_processor.checkbox_results:
            results.update(checkbox_processor.checkbox_results)
            print(f"📍 [CHECKBOX结果提取] 从checkbox_results获取到 {len(checkbox_processor.checkbox_results)} 个结果")

        # 2. 尝试从处理器的其他结果属性中获取
        if hasattr(checkbox_processor, 'processed_results') and checkbox_processor.processed_results:
            results.update(checkbox_processor.processed_results)
            print(f"📍 [CHECKBOX结果提取] 从processed_results获取到 {len(checkbox_processor.processed_results)} 个结果")

        # 3. 尝试从LLM处理器中获取结果
        if hasattr(checkbox_processor, 'llm_handler') and checkbox_processor.llm_handler:
            llm_handler = checkbox_processor.llm_handler
            if hasattr(llm_handler, 'checkbox_results') and llm_handler.checkbox_results:
                results.update(llm_handler.checkbox_results)
                print(f"📍 [CHECKBOX结果提取] 从LLM处理器获取到 {len(llm_handler.checkbox_results)} 个结果")

        # 4. 扫描文档中的复选框状态作为备用方案
        if not results:
            print("📍 [CHECKBOX结果提取] 处理器中没有结果，扫描文档中的复选框状态...")
            checkbox_count = 0

            # 更完整的复选框字符集合
            checkbox_chars = [
                '☐', '□', '■', '☑', '☒', '✓', '✔', '✗', '✘', '×', '☓',  # 原有字符
                '▢', '▣', '⬜', '⬛', '◻', '◼', '◯', '●', '○', '◉',      # 方形和圆形
                '❏', '❐', '❑', '❒', '⊞', '⊟', '⊠', '⊡',              # 其他复选框字符
                '🔲', '🔳', '☐', '☑', '☒'                              # Unicode复选框
            ]

            # 勾选状态的字符
            checked_chars = ['☑', '■', '✓', '✔', '▣', '⬛', '◼', '●', '◉', '❑', '❒', '⊠', '⊡', '🔳']

            print(f"📍 [CHECKBOX结果提取] 使用 {len(checkbox_chars)} 种复选框字符进行扫描")

            # 扫描段落
            for para_idx, paragraph in enumerate(doc.paragraphs):
                para_text = paragraph.text
                for char_idx, char in enumerate(para_text):
                    if char in checkbox_chars:
                        checkbox_count += 1
                        checkbox_key = f"checkbox_{checkbox_count}"
                        is_checked = char in checked_chars
                        results[checkbox_key] = is_checked
                        print(f"📍 [CHECKBOX结果提取] 段落复选框 {checkbox_key}: {is_checked} (段落{para_idx}, 字符位置{char_idx}, 字符: {char})")

            # 扫描表格
            for table_idx, table in enumerate(doc.tables):
                for row_idx, row in enumerate(table.rows):
                    for cell_idx, cell in enumerate(row.cells):
                        for para_idx, para in enumerate(cell.paragraphs):
                            para_text = para.text
                            for char_idx, char in enumerate(para_text):
                                if char in checkbox_chars:
                                    checkbox_count += 1
                                    checkbox_key = f"checkbox_{checkbox_count}"
                                    is_checked = char in checked_chars
                                    results[checkbox_key] = is_checked
                                    print(f"📍 [CHECKBOX结果提取] 表格复选框 {checkbox_key}: {is_checked} (位置: 表{table_idx}行{row_idx}列{cell_idx}段{para_idx}字符{char_idx}, 字符: {char})")

        print(f"📍 [CHECKBOX结果提取] 最终提取到 {len(results)} 个CHECKBOX结果: {results}")
        return results

    except Exception as e:
        print(f"📍 [CHECKBOX结果提取] 提取CHECKBOX结果时出错: {str(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return {}

# 异步处理文件的函数
def process_file_async(file_path, ocr_engine_type, download_dir, classification_mode="auto", template_type=None):
    # 在线程中创建应用上下文
    with app.app_context():
        try:
            # 输出文件信息
            print(f"\n=== 开始处理文件 ===\n文件路径: {file_path}")
            print(f"文件存在: {os.path.exists(file_path)}")

            # 确保手动模式下有模板类型
            if classification_mode == "manual" and not template_type:
                print("警告: 手动模式下未提供模板类型，将使用默认模板")
                # 尝试使用第一个可用的模板
                available_templates = [f for f in os.listdir(app.config['TEMPLATES_DIR']) if f.endswith('.docx')]
                if available_templates:
                    template_type = available_templates[0]
                    print(f"使用第一个可用模板: {template_type}")
                else:
                    print("错误: 没有可用的模板")
                    app.config['PROCESSING_STATUS'] = {
                        'status': '错误',
                        'message': '没有可用的模板'
                    }
                    return

            if os.path.exists(file_path):
                print(f"文件大小: {os.path.getsize(file_path)} 字节")
                print(f"文件扩展名: {os.path.splitext(file_path)[1]}")
            else:
                print("错误: 文件不存在!")
                app.config['PROCESSING_STATUS'] = {
                    'status': '错误',
                    'message': '文件不存在'
                }
                return

            # 创建OCR引擎
            app.config['PROCESSING_STATUS'] = {
                'status': 'OCR',
                'message': '正在进行OCR文字识别'
            }
            print(f"OCR processing, status set to: {app.config['PROCESSING_STATUS']}")

            # 检查并规范化OCR引擎类型
            if not ocr_engine_type or ocr_engine_type.strip() == '':
                print(f"警告: OCR引擎类型为空，使用默认值: {app.config['OCR_ENGINE']}")
                ocr_engine_type = app.config['OCR_ENGINE']

            # 强制使用小写并去除空格
            ocr_engine_type = ocr_engine_type.lower().strip()

            # 验证OCR引擎类型
            valid_engines = ['tesseract', 'olm', 'paddleocr', 'baidu']
            if ocr_engine_type not in valid_engines:
                print(f"警告: 无效的OCR引擎类型: '{ocr_engine_type}'，使用默认值: {app.config['OCR_ENGINE']}")
                ocr_engine_type = app.config['OCR_ENGINE']

            print(f"\n=== 创建OCR引擎 ===\n引擎类型: '{ocr_engine_type}'")

            # 创建OCR引擎配置
            # 根据引擎类型设置配置
            if ocr_engine_type.lower() == 'paddleocr':
                ocr_config = {
                    'use_gpu': app.config['PADDLE_USE_GPU'],
                    'lang': app.config['PADDLE_LANG'],
                    'use_angle_cls': app.config['PADDLE_USE_ANGLE_CLS'],
                    'show_log': False
                }

                # 添加模型目录配置（如果已配置）
                if 'PADDLE_DET_MODEL_DIR' in app.config and os.path.exists(app.config['PADDLE_DET_MODEL_DIR']):
                    ocr_config['det_model_dir'] = app.config['PADDLE_DET_MODEL_DIR']
                    print(f"使用检测模型目录: {app.config['PADDLE_DET_MODEL_DIR']}")

                if 'PADDLE_REC_MODEL_DIR' in app.config and os.path.exists(app.config['PADDLE_REC_MODEL_DIR']):
                    ocr_config['rec_model_dir'] = app.config['PADDLE_REC_MODEL_DIR']
                    print(f"使用识别模型目录: {app.config['PADDLE_REC_MODEL_DIR']}")

                if 'PADDLE_CLS_MODEL_DIR' in app.config and os.path.exists(app.config['PADDLE_CLS_MODEL_DIR']) and app.config['PADDLE_USE_ANGLE_CLS']:
                    ocr_config['cls_model_dir'] = app.config['PADDLE_CLS_MODEL_DIR']
                    print(f"使用方向分类模型目录: {app.config['PADDLE_CLS_MODEL_DIR']}")
            elif ocr_engine_type.lower() == 'tesseract':
                ocr_config = {
                    'tesseract_cmd': app.config['TESSERACT_CMD'],
                    'lang': app.config['OCR_LANGUAGE']
                }
            elif ocr_engine_type.lower() == 'baidu':
                ocr_config = {
                    'app_id': app.config.get('BAIDU_OCR_APP_ID'),
                    'api_key': app.config.get('BAIDU_OCR_API_KEY'),
                    'secret_key': app.config.get('BAIDU_OCR_SECRET_KEY'),
                    'language_type': app.config.get('BAIDU_OCR_LANGUAGE_TYPE', 'CHN_ENG')
                }
            else:
                # 默认配置
                ocr_config = {
                    'lang': app.config['OCR_LANGUAGE']
                }
            print(f"引擎配置: {ocr_config}")

            # 创建OCR引擎实例
            ocr_engine = OCRFactory.get_ocr_engine(ocr_engine_type, ocr_config)

            # 处理文件
            print(f"开始处理文件: {file_path}")
            text = ocr_engine.process_file(file_path)
            print(f"文件处理完成, 提取的文本长度: {len(text)}")

            # 保存OCR原始文本到app.config中，供CHECKBOX处理使用
            app.config['LAST_OCR_TEXT'] = text
            print(f"已保存OCR原始文本到app.config，长度: {len(text)}")

            # 创建大模型处理器
            if classification_mode == "auto":
                app.config['PROCESSING_STATUS'] = {
                    'status': 'LLM分类',
                    'message': '正在调用大模型分类文本'
                }
                print(f"LLM classification, status set to: {app.config['PROCESSING_STATUS']}")

            app.config['PROCESSING_STATUS'] = {
                'status': 'LLM处理',
                'message': '正在调用大模型分析文本'
            }
            print(f"LLM processing, status set to: {app.config['PROCESSING_STATUS']}")

            llm_processor = LLMProcessor(
                app.config['LLM_API_URL'],
                app.config['LLM_API_KEY'],
                app.config['LLM_MODEL'],
                app.config['CLASSIFICATION_MODEL'],  # 分类模型参数
                app.config['CLASSIFICATION_API_URL'],  # 分类API URL
                app.config['CLASSIFICATION_API_KEY']   # 分类API密钥
            )

            # 第一步：文档分类和模板选择
            app.config['PROCESSING_STATUS'] = {
                'status': 'LLM文档分类',
                'message': '正在分析文档'
            }
            print(f"Document classification, status set to: {app.config['PROCESSING_STATUS']}")

            # 进行文档分类以选择正确的模板
            if classification_mode == "manual" and template_type:
                print(f"手动模式：使用指定的模板类型: {template_type}")
                selected_template_type = template_type
            else:
                print("自动模式：调用LLM进行文档分类...")
                selected_template_type = llm_processor.classify_case_type(text)
                print(f"分类结果: {selected_template_type}")

            # 选择适当的模板
            template_path, _ = select_template({'template_type': selected_template_type}, classification_mode, selected_template_type)
            if template_path is None:
                template_path = app.config['DEFAULT_TEMPLATE_PATH']

            print(f"选定的模板路径: {template_path}")

            # 第二步：当事人信息提取
            app.config['PROCESSING_STATUS'] = {
                'status': '并行处理',
                'message': '正在并行分析当事人信息和占位符内容'
            }
            print(f"Parallel processing, status set to: {app.config['PROCESSING_STATUS']}")

            # 引入并行处理器
            from document.parallel_processor import ParallelProcessor

            # 创建并行处理器实例
            parallel_processor = ParallelProcessor(debug=True)

            # 准备当事人处理器配置
            party_processor_config = {
                'api_url': app.config['LLM_API_URL'],
                'api_key': app.config['LLM_API_KEY'],
                'model': app.config['LLM_MODEL'],
                'debug': app.config.get('DEBUG', False)
            }

            # 准备LLM处理器配置
            llm_processor_config = {
                'api_url': app.config['LLM_API_URL'],
                'api_key': app.config['LLM_API_KEY'],
                'model': app.config['LLM_MODEL'],
                'classification_model': app.config['CLASSIFICATION_MODEL'],
                'classification_api_url': app.config['CLASSIFICATION_API_URL'],
                'classification_api_key': app.config['CLASSIFICATION_API_KEY']
            }

            # 并行执行当事人提取和占位符内容提取
            party_info, formatted_text = parallel_processor.extract_party_and_placeholders_parallel(
                text, template_path, party_processor_config, llm_processor_config,
                selected_template_type, classification_mode, flask_app=app
            )

            print(f"并行处理完成 - 当事人信息: {len(party_info)} 个列表, 格式化文本: {len(formatted_text)} 个字段")

            # 检查并行处理结果
            if "error" in formatted_text:
                print(f"警告: 占位符内容提取失败: {formatted_text['error']}")
                # 如果并行处理失败，回退到原始流程
                app.config['PROCESSING_STATUS'] = {
                    'status': 'LLM占位符处理',
                    'message': '正在分析占位符信息（回退模式）'
                }
                print(f"Fallback placeholder processing, status set to: {app.config['PROCESSING_STATUS']}")

                # 生成中间文档
                from document.intermediate_processor import IntermediateDocumentProcessor
                intermediate_processor = IntermediateDocumentProcessor(template_path=template_path, debug=True)
                intermediate_doc, remaining_placeholders = intermediate_processor.create_intermediate_document(party_info)

                # 使用原始方法处理占位符
                formatted_text = llm_processor.process_text_with_placeholders(
                    text, selected_template_type, classification_mode,
                    remaining_placeholders, intermediate_processor.get_document_text(intermediate_doc)
                )

                # 合并当事人信息
                if isinstance(formatted_text, dict):
                    formatted_text.update(party_info)
                    formatted_text['template_type'] = selected_template_type
                    print("当事人信息已合并到格式化文本中（回退模式）")
            else:
                print("并行处理成功，跳过中间文档生成步骤")

            # 创建文档生成器
            app.config['PROCESSING_STATUS'] = {
                'status': '生成文档',
                'message': '正在生成Word文档'
            }
            print(f"Document generation, status set to: {app.config['PROCESSING_STATUS']}")

            # 选择适当的模板
            template_path, formatted_text = select_template(formatted_text, classification_mode, template_type)

            # 如果没有找到合适的模板，返回提示让用户手动选择
            if template_path is None:
                # 获取可用模板列表供用户选择
                available_templates = [f for f in os.listdir(app.config['TEMPLATES_DIR']) if f.endswith('.docx')]

                # 返回数据给前端，包含格式化的文本和可用模板列表
                response_data = {
                    'status': 'template_selection_required',
                    'message': '未找到合适的模板，请手动选择',
                    'available_templates': available_templates,
                    'formatted_text': formatted_text
                }
                return jsonify(response_data)

            print(f"最终选择的模板路径: {template_path}")

            # 创建文档生成器
            print(f"最终使用的模板路径用于文档生成器: {template_path}")

            # 确保模板路径存在
            if not os.path.exists(template_path):
                print(f"错误: 模板路径不存在: {template_path}")
                app.config['PROCESSING_STATUS'] = {
                    'status': '错误',
                    'message': f'模板文件不存在: {os.path.basename(template_path)}'
                }
                return

            # 创建完整的配置字典，包含LLM配置和案件类型关键词
            doc_generator_config = {
                'use_llm_for_checkbox': True,
                'process_checkboxes': True,  # 启用复选框处理
                'llm_api_url': app.config.get('CHECKBOX_LLM_API_URL') or app.config['LLM_API_URL'],
                'llm_api_key': app.config.get('CHECKBOX_LLM_API_KEY') or app.config['LLM_API_KEY'],
                'llm_model': app.config.get('CHECKBOX_LLM_MODEL') or app.config['LLM_MODEL'],
                'llm_temperature': app.config.get('CHECKBOX_LLM_TEMPERATURE') or app.config.get('LLM_TEMPERATURE', 0.3),
                'llm_max_tokens': app.config.get('CHECKBOX_LLM_MAX_TOKENS') or app.config.get('LLM_MAX_TOKENS', 1000),
                'llm_top_p': app.config.get('CHECKBOX_LLM_TOP_P') or app.config.get('LLM_TOP_P', 0.9),
                'case_type_keywords': app.config.get('CASE_TYPE_KEYWORDS', {}),
                'template_mapping': app.config.get('TEMPLATE_MAPPING', {}),
                'ocr_original_text': text,  # 保证OCR原始文本传递给复选框处理
                'debug_mode': app.config.get('DEBUG', True)  # 启用调试模式，记录更多日志
            }

            # 确保检测到的模板类型也传递给文档生成器
            if 'DETECTED_TEMPLATE_TYPE' in app.config:
                doc_generator_config['detected_template_type'] = app.config['DETECTED_TEMPLATE_TYPE']

            # 保存当前使用的模板路径到配置中，以便调试
            app.config['CURRENT_TEMPLATE_PATH'] = template_path

            # 检查是否启用预览编辑
            if os.environ.get('PREVIEW_EDIT', 'false').lower() == 'true':
                print("预览编辑已启用，开始生成完整预览数据...")

                # 生成完整的预览数据，包括当事人模板处理后的内容
                try:
                    complete_preview_data = generate_complete_preview_data(
                        formatted_text, template_path, party_info
                    )
                    print(f"完整预览数据生成成功，包含字段: {list(complete_preview_data.keys())}")

                    # 检查是否包含中间文档内容
                    if '_complete_document_text' in complete_preview_data:
                        print("✅ 完整文档内容已包含在预览数据中")
                    else:
                        print("⚠️ 警告：完整文档内容未包含在预览数据中")

                except Exception as e:
                    print(f"生成完整预览数据时出错: {str(e)}")
                    # 如果出错，使用原始数据
                    complete_preview_data = formatted_text

                # 保存完整的预览数据到临时存储
                from utils.preview_storage import get_preview_storage
                storage = get_preview_storage()
                # 使用文件路径作为file_id
                file_id = os.path.basename(file_path) if 'file_path' in locals() else 'single_file'
                preview_id = storage.save_preview_data(complete_preview_data, template_path, file_id)

                # 返回所见即所得编辑URL
                app.config['PROCESSING_STATUS'] = {
                    'status': '文档编辑',
                    'message': '请在表单中编辑内容后生成文档',
                    'edit_url': f'/edit-document/{preview_id}'
                }
                print(f"WYSIWYG edit enabled, redirecting to: /edit-document/{preview_id}")
                return

            # 创建文档生成器实例并传递完整配置
            try:
                doc_generator = DocumentGenerator(
                    template_path=template_path,
                    llm_api_url=app.config['LLM_API_URL'],
                    llm_api_key=app.config['LLM_API_KEY'],
                    config=doc_generator_config
                )

                # 生成Word文档 - 确保正确使用当事人信息
                print("开始生成Word文档，使用当事人信息模板")
                output_path = doc_generator.generate_document(formatted_text, download_dir)
            except Exception as e:
                error_msg = f"创建文档生成器或生成文档时出错: {str(e)}"
                print(error_msg)

                # 检查是否是LLM API相关错误
                if "LLM API调用失败" in str(e) or "timeout" in str(e).lower() or "connection" in str(e).lower():
                    app.config['PROCESSING_STATUS'] = {
                        'status': '错误',
                        'message': f'LLM API调用失败，请检查网络连接和API配置: {str(e)}'
                    }
                else:
                    app.config['PROCESSING_STATUS'] = {
                        'status': '错误',
                        'message': f'生成文档失败: {str(e)}'
                    }
                return

            # 获取下载链接
            if output_path:
                download_filename = os.path.basename(output_path)
                # 单个处理页面，直接从下载目录获取文件
                download_url = f"/download/{download_filename}"

                # 将结果存储在会话中
                app.config['LAST_RESULT'] = {
                    'result': formatted_text,
                    'download_url': download_url
                }

                # 更新状态为完成
                app.config['PROCESSING_STATUS'] = {
                    'status': '完成',
                    'message': '处理完成'
                }
                print(f"Processing completed, status set to: {app.config['PROCESSING_STATUS']}")
            else:
                app.config['PROCESSING_STATUS'] = {
                    'status': '错误',
                    'message': '生成文档失败'
                }
                print(f"Document generation failed, status set to: {app.config['PROCESSING_STATUS']}")
        except Exception as e:
            app.config['PROCESSING_STATUS'] = {
                'status': '错误',
                'message': f'处理错误: {str(e)}'
            }
            print(f"Processing error: {str(e)}, status set to: {app.config['PROCESSING_STATUS']}")

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理单诉状的多文件上传（将按序合并OCR结果）"""
    global processing_thread

    # 重置状态
    app.config['PROCESSING_STATUS'] = {
        'status': '开始',
        'message': '开始处理文件'
    }
    print(f"Upload started, status set to: {app.config['PROCESSING_STATUS']}")

    # 检查是否有文件
    if 'files[]' not in request.files:
        app.config['PROCESSING_STATUS'] = {
            'status': '错误',
            'message': '没有文件'
        }
        return jsonify({'error': '没有文件'}), 400

    files = request.files.getlist('files[]')

    # 检查是否选择了文件
    if not files or files[0].filename == '':
        app.config['PROCESSING_STATUS'] = {
            'status': '错误',
            'message': '没有选择文件'
        }
        return jsonify({'error': '没有选择文件'}), 400

    try:
        # 保存文件
        app.config['PROCESSING_STATUS'] = {
            'status': '上传',
            'message': '正在保存文件'
        }
        print(f"Files saving, status set to: {app.config['PROCESSING_STATUS']}")

        # 创建任务ID和目录
        task_id = uuid.uuid4().hex
        task_dir = os.path.join(app.config['UPLOAD_FOLDER'], task_id)
        os.makedirs(task_dir, exist_ok=True)

        # 保存所有文件
        saved_files = []
        for file in files:
            if file and allowed_file(file.filename):
                original_filename = file.filename
                print(f"原始文件名: {original_filename}")

                # 使用辅助函数处理文件名
                final_filename = process_filename(original_filename)
                file_path = os.path.join(task_dir, final_filename)
                print(f"保存文件到: {file_path}")

                # 保存文件
                file.save(file_path)

                # 添加到已保存文件列表
                saved_files.append({
                    'original_name': original_filename,
                    'saved_path': file_path
                })

        # 获取OCR引擎类型
        ocr_engine_type = request.form.get('ocr_engine', app.config['OCR_ENGINE'])

        # 获取分类模式和模板类型
        classification_mode = request.form.get('classification_mode', 'auto')
        template_type = request.form.get('template_type', None)

        print(f"分类模式: {classification_mode}, 模板类型: {template_type}")
        print(f"需要处理的文件数量: {len(saved_files)}")

        # 开始异步处理
        if processing_thread and processing_thread.is_alive():
            # 如果有正在运行的处理线程，则等待它完成
            app.config['PROCESSING_STATUS'] = {
                'status': '等待',
                'message': '等待前一个任务完成'
            }
            print(f"Waiting for previous task, status set to: {app.config['PROCESSING_STATUS']}")

        # 创建新线程处理OCR，但不直接进行LLM处理
        processing_thread = threading.Thread(
            target=process_files_with_confirmation,
            args=(saved_files, ocr_engine_type, download_dir, classification_mode, template_type, task_id)
        )
        processing_thread.daemon = True  # 设置为守护线程，不阻塞主线程退出
        processing_thread.start()

        # 立即返回响应，不等待处理完成
        return jsonify({
            'success': True,
            'message': '文件已接收，正在处理中',
            'redirect': '/result'  # 添加重定向URL
        })

    except Exception as e:
        app.config['PROCESSING_STATUS'] = {
            'status': '错误',
            'message': f'处理错误: {str(e)}'
        }
        print(f"Processing error: {str(e)}, status set to: {app.config['PROCESSING_STATUS']}")
        return jsonify({'error': str(e)}), 500

@app.route('/continue_with_template', methods=['POST'])
def continue_with_template():
    """处理用户手动选择模板后的后续处理"""
    try:
        # 获取用户选择的模板
        template_type = request.form.get('template_type')
        classification_mode = request.form.get('classification_mode', 'manual')

        print(f"用户选择的模板：{template_type}")

        if not template_type:
            return jsonify({'error': '未选择模板'}), 400

        # 获取格式化的文本
        formatted_text_json = request.form.get('formatted_text_json')
        if formatted_text_json:
            formatted_text = json.loads(formatted_text_json)
        else:
            # 如果前端没有传递格式化文本，尝试使用应用程序配置中的数据
            formatted_text = app.config.get('FORMATTED_TEXT', {})

        # 创建下载目录
        if not os.path.exists(download_dir):
            os.makedirs(download_dir, exist_ok=True)

        # 更新处理状态
        app.config['PROCESSING_STATUS'] = {
            'status': '生成文档',
            'message': '正在生成文档...'
        }

        # 选择模板路径
        template_path = os.path.join(app.config['TEMPLATES_DIR'], template_type)

        if not os.path.exists(template_path):
            error_msg = f"模板文件不存在: {template_path}"
            print(f"错误: {error_msg}")
            raise FileNotFoundError(error_msg)

        # 添加模板文件名到格式化文本中
        if isinstance(formatted_text, dict):
            formatted_text['template_filename'] = template_type

            # 从文件名中提取案件类型，例如从"2-离婚纠纷起诉状.docx"提取"离婚纠纷起诉状"
            import re
            match = re.match(r'^\d+-(.+)\.docx$', template_type)
            if match:
                case_type = match.group(1)
                formatted_text['template_type'] = case_type
            else:
                # 如果无法提取，使用原始模板类型（去除扩展名）
                formatted_text['template_type'] = os.path.splitext(template_type)[0]

        # 创建文档生成器的配置
        doc_generator_config = {
            'use_llm_for_checkbox': app.config.get('USE_LLM_FOR_CHECKBOX', True),
            'process_checkboxes': app.config.get('PROCESS_CHECKBOXES', True),
            'use_rule_engine_for_checkbox': app.config.get('USE_RULE_ENGINE_FOR_CHECKBOX', False),
            'combine_checkboxes': app.config.get('COMBINE_CHECKBOXES', False),  # 添加复选框合并配置
            'combine_checkboxes_by': app.config.get('COMBINE_CHECKBOXES_BY', 'fulltext'),  # 添加复选框合并策略配置
            'llm_api_url': app.config.get('CHECKBOX_LLM_API_URL') or app.config.get('LLM_API_URL', ''),
            'llm_api_key': app.config.get('CHECKBOX_LLM_API_KEY') or app.config.get('LLM_API_KEY', ''),
            'llm_model': app.config.get('CHECKBOX_LLM_MODEL') or app.config.get('LLM_MODEL', ''),
            'llm_temperature': app.config.get('CHECKBOX_LLM_TEMPERATURE') or app.config.get('LLM_TEMPERATURE', 0.3),
            'llm_max_tokens': app.config.get('CHECKBOX_LLM_MAX_TOKENS') or app.config.get('LLM_MAX_TOKENS', 1000),
            'llm_top_p': app.config.get('CHECKBOX_LLM_TOP_P') or app.config.get('LLM_TOP_P', 0.9),
        }

        # 保存当前使用的模板路径到配置中，以便调试
        app.config['CURRENT_TEMPLATE_PATH'] = template_path

        # 创建文档生成器
        try:
            from document.generator import DocumentGenerator
            doc_generator = DocumentGenerator(
                template_path=template_path,
                llm_api_url=app.config.get('LLM_API_URL', ''),
                llm_api_key=app.config.get('LLM_API_KEY', ''),
                config=doc_generator_config
            )

            # 生成文档
            output_path = doc_generator.generate_document(formatted_text, download_dir)
            output_filename = os.path.basename(output_path)
        except Exception as e:
            error_msg = f"生成文档失败: {str(e)}"
            print(f"错误: {error_msg}")

            # 检查是否是LLM API相关错误并更新状态
            if "LLM API调用失败" in str(e) or "timeout" in str(e).lower() or "connection" in str(e).lower():
                app.config['PROCESSING_STATUS'] = {
                    'status': '错误',
                    'message': f'LLM API调用失败，请检查网络连接和API配置: {str(e)}'
                }
                return jsonify({'error': f'LLM API调用失败，请检查网络连接和API配置: {str(e)}'}), 500
            else:
                app.config['PROCESSING_STATUS'] = {
                    'status': '错误',
                    'message': error_msg
                }
                return jsonify({'error': error_msg}), 500

        # 更新处理状态
        app.config['PROCESSING_STATUS'] = {
            'status': '完成',
            'message': '文档生成完成'
        }

        # 更新结果信息
        app.config['LAST_RESULT'] = {
            'result': {
                'success': True,
                'message': '文档生成成功',
                'filename': output_filename
            },
            'download_url': f"/download/{output_filename}"
        }

        return jsonify({
            'success': True,
            'message': '文档生成成功',
            'redirect': '/result'
        })

    except Exception as e:
        import traceback
        traceback.print_exc()

        # 更新处理状态
        app.config['PROCESSING_STATUS'] = {
            'status': '错误',
            'message': f'处理错误: {str(e)}'
        }

        return jsonify({
            'error': str(e)
        }), 500

@app.route('/result')
def result():
    """显示LLM处理结果"""
    try:
        # 首先检查LAST_RESULT是否存在
        if 'LAST_RESULT' in app.config and app.config['LAST_RESULT']:
            result_data = app.config['LAST_RESULT']
            download_url = result_data.get('download_url')
            result_text = result_data.get('result')
            return render_template('result.html', result=result_text, download_url=download_url)
        # 检查PROCESSING_STATUS是否表明处理中
        elif 'PROCESSING_STATUS' in app.config:
            status = app.config['PROCESSING_STATUS']
            if status.get('status') in ['开始', '上传', 'OCR中', 'LLM处理', 'LLM分类', '生成文档']:
                # 处理中，显示处理状态
                return render_template('result.html', processing=True, status=status)
            elif status.get('status') == '错误':
                # 处理出错，显示错误信息
                return render_template('result.html', error=status.get('message', '未知错误'))

        # 如果没有结果数据，显示错误信息
        return render_template('result.html', error='没有可用的处理结果')
    except Exception as e:
        print(f"显示结果页面时出错: {str(e)}")
        return render_template('result.html', error=f'显示结果时出错: {str(e)}')

@app.route('/download/<path:filepath>')
def download_file(filepath):
    """下载文件

    支持两种路径格式：
    1. /download/filename - 直接从下载目录获取文件
    2. /download/task_id/filename - 从任务特定的子目录获取文件
    """
    # 分割路径
    parts = filepath.split('/')

    if len(parts) == 1:
        # 单个文件名，直接从下载目录获取
        return send_from_directory(directory=download_dir, path=parts[0], as_attachment=True)
    elif len(parts) == 2:
        # 任务ID和文件名，从任务子目录获取
        task_id = parts[0]
        filename = parts[1]
        task_dir = os.path.join(download_dir, task_id)

        if os.path.exists(os.path.join(task_dir, filename)):
            return send_from_directory(directory=task_dir, path=filename, as_attachment=True)

    # 如果文件不存在，返回404
    return "文件不存在", 404

@app.errorhandler(404)
def page_not_found(e):
    """404页面"""
    return render_template('index.html'), 404

@app.errorhandler(500)
def internal_server_error(e):
    """500页面"""
    return render_template('index.html', error='服务器内部错误'), 500

@app.route('/api/case-types')
def get_case_types():
    """返回可用的案件类型列表"""
    return jsonify({
        'case_types': app.config['CASE_TYPES'],
        'default_type': app.config['CASE_TYPES'][0] if app.config['CASE_TYPES'] else "合同纠纷起诉状"
    })

@app.route('/api/templates')
def get_templates():
    """返回分目录的模板文件"""
    templates_dir = app.config['TEMPLATES_DIR']
    template_files = []

    try:
        # 遍历所有子目录
        for batch_name in ['第一批', '第二批']:
            batch_dir = os.path.join(templates_dir, batch_name)
            if os.path.exists(batch_dir):
                for category_name in os.listdir(batch_dir):
                    category_dir = os.path.join(batch_dir, category_name)
                    if os.path.isdir(category_dir):
                        for filename in os.listdir(category_dir):
                            if filename.endswith('.docx') and not filename.startswith('~'):
                                # 构建相对路径
                                relative_path = f"{batch_name}/{category_name}/{filename}"

                                # 提取显示名称（去掉数字前缀）
                                display_name = filename
                                import re
                                match = re.match(r'^\d+-(.+)\.docx$', filename)
                                if match:
                                    display_name = match.group(1)
                                else:
                                    display_name = os.path.splitext(filename)[0]

                                template_files.append({
                                    'filename': relative_path,
                                    'name': display_name,
                                    'batch': batch_name,
                                    'category': category_name
                                })

        # 添加根目录的通用模板
        for filename in os.listdir(templates_dir):
            if filename.endswith('.docx') and not filename.startswith('~') and not os.path.isdir(os.path.join(templates_dir, filename)):
                # 跳过子目录，只处理文件
                display_name = os.path.splitext(filename)[0]
                template_files.append({
                    'filename': filename,
                    'name': display_name,
                    'batch': '通用',
                    'category': '通用'
                })

    except Exception as e:
        print(f"获取模板列表时出错: {str(e)}")

    # 按批次和编号排序
    def get_sort_key(item):
        try:
            # 提取文件名中的数字部分
            filename = os.path.basename(item['filename'])
            import re
            match = re.match(r'^(\d+)', filename)
            if match:
                batch_order = 0 if item['batch'] == '第一批' else 1
                return (batch_order, int(match.group(1)))
            return (2, filename)  # 没有数字前缀的放在最后
        except:
            return (3, item['filename'])

    try:
        template_files.sort(key=get_sort_key)
    except Exception as e:
        print(f"模板文件排序错误: {str(e)}")

    return jsonify({
        'templates': template_files
    })

@app.route('/batch')
def batch_page():
    """批量处理页面"""
    # 获取默认OCR引擎
    default_ocr_engine = app.config.get('OCR_ENGINE', 'tesseract').lower()
    return render_template('batch.html', default_ocr_engine=default_ocr_engine)

@app.route('/upload-batch', methods=['POST'])
def upload_batch():
    """处理批量文件上传"""
    if 'files[]' not in request.files:
        return jsonify({'success': False, 'error': '没有文件'}), 400

    files = request.files.getlist('files[]')
    if not files or files[0].filename == '':
        return jsonify({'success': False, 'error': '没有选择文件'}), 400

    # 创建任务ID
    task_id = uuid.uuid4().hex

    # 获取处理选项
    ocr_engine_type = request.form.get('ocr_engine', app.config['OCR_ENGINE'])

    # 创建任务状态
    app.config['BATCH_TASKS'][task_id] = {
        'status': 'pending',
        'total_files': len(files),
        'processed_files': 0,
        'current_file': None,
        'current_progress': 0,
        'results': [],
        'pending_files': [file.filename for file in files],  # 添加待处理的文件列表
        'options': {
            'ocr_engine': ocr_engine_type
        }
    }

    # 创建上传目录
    batch_upload_dir = os.path.join(app.config['UPLOAD_FOLDER'], task_id)
    os.makedirs(batch_upload_dir, exist_ok=True)

    # 创建结果目录
    batch_download_dir = os.path.join(download_dir, task_id)
    os.makedirs(batch_download_dir, exist_ok=True)

    # 保存文件
    saved_files = []
    for file in files:
        if file and allowed_file(file.filename):
            filename = process_filename(file.filename)
            file_path = os.path.join(batch_upload_dir, filename)
            file.save(file_path)
            saved_files.append({
                'original_name': file.filename,
                'saved_path': file_path
            })

    # 启动异步处理线程
    batch_thread = threading.Thread(
        target=process_batch_async,
        args=(task_id, saved_files, batch_download_dir, ocr_engine_type)
    )
    batch_thread.daemon = True
    batch_thread.start()

    return jsonify({
        'success': True,
        'task_id': task_id,
        'message': f'已开始处理 {len(saved_files)} 个文件'
    })

def process_batch_async(task_id, files, download_dir, ocr_engine_type):
    """异步处理批量文件"""
    with app.app_context():
        try:
            task = app.config['BATCH_TASKS'][task_id]
            task['status'] = 'processing'

            for i, file_info in enumerate(files):
                # 更新当前处理的文件
                task['current_file'] = os.path.basename(file_info['original_name'])
                task['current_progress'] = 0

                # 从待处理列表中移除当前文件
                if 'pending_files' in task and task['current_file'] in task['pending_files']:
                    task['pending_files'].remove(task['current_file'])

                try:
                    # 处理文件
                    file_path = file_info['saved_path']

                    # 确定OCR引擎类型
                    if not ocr_engine_type or ocr_engine_type.strip() == '':
                        ocr_engine_type = app.config['OCR_ENGINE']

                    # 强制使用小写并去除空格
                    ocr_engine_type = ocr_engine_type.lower().strip()

                    # 验证OCR引擎类型
                    valid_engines = ['tesseract', 'olm', 'paddleocr', 'baidu']
                    if ocr_engine_type not in valid_engines:
                        print(f"警告: 无效的OCR引擎类型: '{ocr_engine_type}'，使用默认值: {app.config['OCR_ENGINE']}")
                        ocr_engine_type = app.config['OCR_ENGINE']

                    # 创建OCR引擎配置
                    if ocr_engine_type.lower() == 'paddleocr':
                        ocr_config = {
                            'use_gpu': app.config['PADDLE_USE_GPU'],
                            'lang': app.config['PADDLE_LANG'],
                            'use_angle_cls': app.config['PADDLE_USE_ANGLE_CLS'],
                            'show_log': False
                        }

                        # 添加模型目录配置（如果已配置）
                        if 'PADDLE_DET_MODEL_DIR' in app.config and os.path.exists(app.config['PADDLE_DET_MODEL_DIR']):
                            ocr_config['det_model_dir'] = app.config['PADDLE_DET_MODEL_DIR']
                            print(f"使用检测模型目录: {app.config['PADDLE_DET_MODEL_DIR']}")

                        if 'PADDLE_REC_MODEL_DIR' in app.config and os.path.exists(app.config['PADDLE_REC_MODEL_DIR']):
                            ocr_config['rec_model_dir'] = app.config['PADDLE_REC_MODEL_DIR']
                            print(f"使用识别模型目录: {app.config['PADDLE_REC_MODEL_DIR']}")

                        if 'PADDLE_CLS_MODEL_DIR' in app.config and os.path.exists(app.config['PADDLE_CLS_MODEL_DIR']) and app.config['PADDLE_USE_ANGLE_CLS']:
                            ocr_config['cls_model_dir'] = app.config['PADDLE_CLS_MODEL_DIR']
                            print(f"使用方向分类模型目录: {app.config['PADDLE_CLS_MODEL_DIR']}")
                    elif ocr_engine_type.lower() == 'tesseract':
                        ocr_config = {
                            'tesseract_cmd': app.config['TESSERACT_CMD'],
                            'lang': app.config['OCR_LANGUAGE']
                        }
                    elif ocr_engine_type.lower() == 'baidu':
                        ocr_config = {
                            'app_id': app.config.get('BAIDU_OCR_APP_ID'),
                            'api_key': app.config.get('BAIDU_OCR_API_KEY'),
                            'secret_key': app.config.get('BAIDU_OCR_SECRET_KEY'),
                            'language_type': app.config.get('BAIDU_OCR_LANGUAGE_TYPE', 'CHN_ENG')
                        }
                    else:
                        # 默认配置
                        ocr_config = {
                            'lang': app.config['OCR_LANGUAGE']
                        }

                    # 创建OCR引擎实例
                    ocr_engine = OCRFactory.get_ocr_engine(ocr_engine_type, ocr_config)

                    # 更新进度
                    task['current_progress'] = 20

                    # 处理文件
                    text = ocr_engine.process_file(file_path)

                    # 更新进度
                    task['current_progress'] = 40

                    # 创建大模型处理器
                    llm_processor = LLMProcessor(
                        app.config['LLM_API_URL'],
                        app.config['LLM_API_KEY'],
                        app.config['LLM_MODEL'],
                        app.config['CLASSIFICATION_MODEL'],  # 分类模型参数
                        app.config['CLASSIFICATION_API_URL'],  # 分类API URL
                        app.config['CLASSIFICATION_API_KEY']   # 分类API密钥
                    )

                    # 处理文本
                    formatted_text = llm_processor.process_text(text, None, "auto")

                    # 更新进度
                    task['current_progress'] = 50

                    # 添加当事人信息专项处理环节
                    print(f"开始处理当事人信息...")

                    # 引入当事人信息处理器
                    from llm.party_processor import PartyInfoProcessor

                    # 创建当事人信息处理器实例
                    party_processor = PartyInfoProcessor(
                        api_url=app.config['LLM_API_URL'],
                        api_key=app.config['LLM_API_KEY'],
                        model=app.config['LLM_MODEL'],
                        debug=app.config.get('DEBUG', True)
                    )

                    # 提取当事人信息
                    party_info = party_processor.extract_party_info(text)

                    # 验证当事人信息
                    if party_processor.validate_party_info(party_info):
                        print("当事人信息提取成功")

                        # 将当事人信息合并到格式化文本中
                        if isinstance(formatted_text, dict):
                            # 移除可能存在的旧格式当事人信息
                            keys_to_remove = []
                            for key in formatted_text.keys():
                                if key.endswith('列表'):
                                    keys_to_remove.append(key)

                            for key in keys_to_remove:
                                formatted_text.pop(key, None)

                            # 合并新的当事人信息
                            formatted_text.update(party_info)
                            print("当事人信息已合并到格式化文本中")
                        else:
                            print("警告: 格式化文本不是字典类型，无法合并当事人信息")
                    else:
                        print("警告: 当事人信息验证失败，将使用原始格式化文本")

                    # 更新进度
                    task['current_progress'] = 60

                    # 选择适当的模板
                    template_path, formatted_text = select_template(formatted_text, "auto", None)

                    # 如果没有找到合适的模板，使用默认模板
                    if template_path is None:
                        template_path = app.config['DEFAULT_TEMPLATE_PATH']

                    # 更新进度
                    task['current_progress'] = 80

                    # 创建文档生成器配置
                    doc_generator_config = {
                        'use_llm_for_checkbox': app.config.get('USE_LLM_FOR_CHECKBOX', True),
                        'process_checkboxes': app.config.get('PROCESS_CHECKBOXES', True),
                        'use_rule_engine_for_checkbox': app.config.get('USE_RULE_ENGINE_FOR_CHECKBOX', False),
                        'combine_checkboxes': app.config.get('COMBINE_CHECKBOXES', False),  # 添加复选框合并配置
                        'combine_checkboxes_by': app.config.get('COMBINE_CHECKBOXES_BY', 'fulltext'),  # 添加复选框合并策略配置
                        'llm_api_url': app.config.get('CHECKBOX_LLM_API_URL') or app.config['LLM_API_URL'],
                        'llm_api_key': app.config.get('CHECKBOX_LLM_API_KEY') or app.config['LLM_API_KEY'],
                        'llm_model': app.config.get('CHECKBOX_LLM_MODEL') or app.config['LLM_MODEL'],
                        'llm_temperature': app.config.get('CHECKBOX_LLM_TEMPERATURE') or app.config.get('LLM_TEMPERATURE', 0.3),
                        'llm_max_tokens': app.config.get('CHECKBOX_LLM_MAX_TOKENS') or app.config.get('LLM_MAX_TOKENS', 1000),
                        'llm_top_p': app.config.get('CHECKBOX_LLM_TOP_P') or app.config.get('LLM_TOP_P', 0.9),
                        'case_type_keywords': app.config.get('CASE_TYPE_KEYWORDS', {}),
                        'template_mapping': app.config.get('TEMPLATE_MAPPING', {}),
                        'ocr_original_text': text,
                        'debug_mode': app.config.get('DEBUG', True)  # 启用调试模式，记录更多日志
                    }

                    # 添加检测到的模板类型
                    if 'template_type' in formatted_text:
                        doc_generator_config['detected_template_type'] = formatted_text['template_type']

                    # 创建文档生成器实例
                    doc_generator = DocumentGenerator(
                        template_path=template_path,
                        llm_api_url=app.config['LLM_API_URL'],
                        llm_api_key=app.config['LLM_API_KEY'],
                        config=doc_generator_config
                    )

                    # 生成文档 - 与单文件处理保持一致，传递目录
                    print("开始生成Word文档，使用当事人信息模板")
                    output_path = doc_generator.generate_document(formatted_text, download_dir)

                    # 更新进度
                    task['current_progress'] = 100

                    # 获取下载链接
                    if output_path:
                        download_filename = os.path.basename(output_path)
                        # 批量处理页面，包含任务ID目录
                        download_url = f"/download/{task_id}/{download_filename}"

                        # 添加结果
                        task['results'].append({
                            'file': file_info['original_name'],
                            'success': True,
                            'download_url': download_url
                        })
                    else:
                        # 处理失败
                        task['results'].append({
                            'file': file_info['original_name'],
                            'success': False,
                            'error': '生成文档失败'
                        })

                except Exception as e:
                    # 处理单个文件的错误
                    print(f"处理文件 {file_info['original_name']} 时出错: {str(e)}")
                    task['results'].append({
                        'file': file_info['original_name'],
                        'success': False,
                        'error': str(e)
                    })

                # 更新已处理文件数
                task['processed_files'] += 1

            # 更新任务状态为完成
            task['status'] = 'completed'
            task['current_file'] = None
            task['current_progress'] = 0

        except Exception as e:
            # 处理整个批处理任务的错误
            print(f"批处理任务 {task_id} 出错: {str(e)}")
            if task_id in app.config['BATCH_TASKS']:
                app.config['BATCH_TASKS'][task_id]['status'] = 'failed'
                app.config['BATCH_TASKS'][task_id]['error'] = str(e)

@app.route('/task-status/<task_id>')
def get_task_status(task_id):
    """获取批量处理任务的状态"""
    if task_id not in app.config['BATCH_TASKS']:
        return jsonify({'error': '任务不存在'}), 404

    return jsonify(app.config['BATCH_TASKS'][task_id])

@app.route('/download-batch', methods=['POST'])
def download_batch():
    """批量下载处理结果"""
    try:
        data = request.json
        if not data or 'urls' not in data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        download_urls = data['urls']
        if not download_urls:
            return jsonify({'success': False, 'error': '没有可下载的文件'}), 400

        # 创建一个临时目录来存放所有文件
        import tempfile
        import zipfile
        import shutil

        temp_dir = tempfile.mkdtemp()

        try:
            # 复制所有文件到临时目录
            for url in download_urls:
                # 从URL中提取路径部分
                if url.startswith('/download/'):
                    path_part = url[len('/download/'):]
                else:
                    path_part = url

                # 分割路径
                parts = path_part.split('/')

                if len(parts) == 1:
                    # 单个文件名，直接从下载目录获取
                    filename = parts[0]
                    source_path = os.path.join(download_dir, filename)
                elif len(parts) == 2:
                    # 任务ID和文件名，从任务子目录获取
                    task_id = parts[0]
                    filename = parts[1]
                    source_path = os.path.join(download_dir, task_id, filename)
                else:
                    # 无效的路径
                    print(f"无效的下载路径: {url}")
                    continue

                print(f"尝试复制文件: {source_path}")
                if os.path.exists(source_path):
                    print(f"文件存在，复制到: {os.path.join(temp_dir, filename)}")
                    shutil.copy2(source_path, os.path.join(temp_dir, filename))
                else:
                    print(f"文件不存在: {source_path}")

            # 检查临时目录中是否有文件
            files_in_temp = os.listdir(temp_dir)
            if not files_in_temp:
                print("临时目录中没有文件")
                return jsonify({'success': False, 'error': '无法找到要下载的文件'}), 404

            print(f"临时目录中的文件: {files_in_temp}")

            # 创建ZIP文件
            zip_filename = f"batch_download_{uuid.uuid4().hex}.zip"
            zip_path = os.path.join(download_dir, zip_filename)

            with zipfile.ZipFile(zip_path, 'w') as zipf:
                for file in files_in_temp:
                    file_path = os.path.join(temp_dir, file)
                    print(f"添加文件到ZIP: {file_path}")
                    zipf.write(file_path, file)

            # 返回ZIP文件的下载链接
            download_url = f"/download/{zip_filename}"
            print(f"生成的下载链接: {download_url}")
            return jsonify({
                'success': True,
                'download_url': download_url
            })

        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir)

    except Exception as e:
        import traceback
        print(f"批量下载出错: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'success': False, 'error': str(e)}), 500

def create_ocr_config(ocr_engine_type):
    """创建OCR引擎配置"""
    if ocr_engine_type.lower() == 'paddleocr':
        ocr_config = {
            'use_gpu': app.config['PADDLE_USE_GPU'],
            'lang': app.config['PADDLE_LANG'],
            'use_angle_cls': app.config['PADDLE_USE_ANGLE_CLS'],
            'show_log': False
        }

        # 添加模型目录配置（如果已配置）
        if 'PADDLE_DET_MODEL_DIR' in app.config and os.path.exists(app.config['PADDLE_DET_MODEL_DIR']):
            ocr_config['det_model_dir'] = app.config['PADDLE_DET_MODEL_DIR']
            print(f"使用检测模型目录: {app.config['PADDLE_DET_MODEL_DIR']}")

        if 'PADDLE_REC_MODEL_DIR' in app.config and os.path.exists(app.config['PADDLE_REC_MODEL_DIR']):
            ocr_config['rec_model_dir'] = app.config['PADDLE_REC_MODEL_DIR']
            print(f"使用识别模型目录: {app.config['PADDLE_REC_MODEL_DIR']}")

        if 'PADDLE_CLS_MODEL_DIR' in app.config and os.path.exists(app.config['PADDLE_CLS_MODEL_DIR']) and app.config['PADDLE_USE_ANGLE_CLS']:
            ocr_config['cls_model_dir'] = app.config['PADDLE_CLS_MODEL_DIR']
            print(f"使用方向分类模型目录: {app.config['PADDLE_CLS_MODEL_DIR']}")
    elif ocr_engine_type.lower() == 'tesseract':
        ocr_config = {
            'tesseract_cmd': app.config['TESSERACT_CMD'],
            'lang': app.config['OCR_LANGUAGE']
        }
    elif ocr_engine_type.lower() == 'baidu':
        ocr_config = {
            'app_id': app.config.get('BAIDU_OCR_APP_ID'),
            'api_key': app.config.get('BAIDU_OCR_API_KEY'),
            'secret_key': app.config.get('BAIDU_OCR_SECRET_KEY'),
            'language_type': app.config.get('BAIDU_OCR_LANGUAGE_TYPE', 'CHN_ENG')
        }
    else:
        # 默认配置
        ocr_config = {
            'lang': app.config['OCR_LANGUAGE']
        }

    return ocr_config

def process_files_with_confirmation(files, ocr_engine_type, download_dir, classification_mode, template_type, task_id):
    """处理文件并在OCR后等待用户确认"""
    with app.app_context():
        try:
            # 执行OCR处理
            combined_text = perform_ocr_processing(files, ocr_engine_type, task_id)

            if combined_text:
                # 保存OCR结果和处理参数到会话中
                confirmation_data = {
                    'task_id': task_id,
                    'ocr_text': combined_text,
                    'files': files,
                    'ocr_engine_type': ocr_engine_type,
                    'download_dir': download_dir,
                    'classification_mode': classification_mode,
                    'template_type': template_type
                }

                # 将确认数据存储到app.config中
                app.config[f'CONFIRMATION_DATA_{task_id}'] = confirmation_data

                # 更新状态为等待确认
                app.config['PROCESSING_STATUS'] = {
                    'status': '等待确认',
                    'message': '请确认OCR识别的文字内容',
                    'ocr_text': combined_text,
                    'task_id': task_id
                }
                print(f"OCR completed, waiting for confirmation: {app.config['PROCESSING_STATUS']}")
            else:
                app.config['PROCESSING_STATUS'] = {
                    'status': '错误',
                    'message': 'OCR处理失败'
                }

        except Exception as e:
            app.config['PROCESSING_STATUS'] = {
                'status': '错误',
                'message': f'处理错误: {str(e)}'
            }
            print(f"Processing error: {str(e)}")

def perform_ocr_processing(files, ocr_engine_type, task_id):
    """执行OCR处理并返回合并的文本"""
    try:
        combined_text = ""
        total_files = len(files)

        # 如果OCR引擎类型为空，使用默认配置
        if not ocr_engine_type or ocr_engine_type.strip() == '':
            ocr_engine_type = app.config['OCR_ENGINE']

        # 创建OCR引擎配置
        ocr_config = create_ocr_config(ocr_engine_type)

        # 创建OCR引擎实例 - 使用同一个实例处理所有文件
        ocr_engine = OCRFactory.get_ocr_engine(ocr_engine_type, ocr_config)

        # 逐个处理文件并合并OCR结果
        for idx, file_info in enumerate(files):
            file_path = file_info['saved_path']
            file_name = os.path.basename(file_info['original_name'])

            # 更新处理状态
            progress_percentage = (idx / total_files) * 80  # 总进度的80%用于OCR
            app.config['PROCESSING_STATUS'] = {
                'status': 'OCR中',
                'message': f'正在识别文件 {idx+1}/{total_files}: {file_name}',
                'progress': progress_percentage
            }

            # 处理文件
            file_text = ocr_engine.process_file(file_path)

            # 添加文件分隔标记并合并文本
            if combined_text:
                combined_text += f"\n\n--- 下一文件: {file_name} ---\n\n"
            else:
                combined_text += f"--- 文件: {file_name} ---\n\n"

            combined_text += file_text

        # 保存OCR原始文本到app.config中，供CHECKBOX处理使用
        app.config['LAST_OCR_TEXT'] = combined_text
        print(f"OCR处理完成，合并文本长度: {len(combined_text)}")

        return combined_text

    except Exception as e:
        print(f"OCR处理出错: {str(e)}")
        return None

def continue_processing_after_confirmation(confirmation_data):
    """在用户确认文字内容后继续处理"""
    with app.app_context():
        try:
            # 从确认数据中获取参数
            task_id = confirmation_data['task_id']
            confirmed_text = confirmation_data['ocr_text']
            files = confirmation_data['files']
            ocr_engine_type = confirmation_data['ocr_engine_type']
            download_dir = confirmation_data['download_dir']
            classification_mode = confirmation_data['classification_mode']
            template_type = confirmation_data['template_type']

            # 清理确认数据
            confirmation_key = f'CONFIRMATION_DATA_{task_id}'
            if confirmation_key in app.config:
                del app.config[confirmation_key]

            # 继续原有的处理流程，使用确认的文字内容
            process_confirmed_text_with_llm(
                confirmed_text, files, ocr_engine_type, download_dir,
                classification_mode, template_type, task_id
            )

        except Exception as e:
            app.config['PROCESSING_STATUS'] = {
                'status': '错误',
                'message': f'处理错误: {str(e)}'
            }
            print(f"Continue processing error: {str(e)}")

def process_confirmed_text_with_llm(text, files, ocr_engine_type, download_dir, classification_mode, template_type, task_id):
    """使用确认的文字内容进行LLM处理"""
    try:
        # 创建下载目录
        result_dir = os.path.join(download_dir, task_id)
        os.makedirs(result_dir, exist_ok=True)

        # 更新状态为LLM处理
        app.config['PROCESSING_STATUS'] = {
            'status': 'LLM处理',
            'message': '正在调用大模型分析文本',
            'progress': 85
        }
        print(f"LLM processing confirmed text, status set to: {app.config['PROCESSING_STATUS']}")

        # 创建大模型处理器
        llm_processor = LLMProcessor(
            app.config['LLM_API_URL'],
            app.config['LLM_API_KEY'],
            app.config['LLM_MODEL'],
            app.config['CLASSIFICATION_MODEL'],
            app.config['CLASSIFICATION_API_URL'],
            app.config['CLASSIFICATION_API_KEY']
        )

        # 文档分类和模板选择
        if classification_mode == "manual" and template_type:
            print(f"手动模式：使用指定的模板类型: {template_type}")
            selected_template_type = template_type
        else:
            print("自动模式：调用LLM进行文档分类...")
            selected_template_type = llm_processor.classify_case_type(text)
            print(f"分类结果: {selected_template_type}")

        # 选择适当的模板
        template_path, _ = select_template({'template_type': selected_template_type}, classification_mode, selected_template_type)
        if template_path is None:
            template_path = app.config['DEFAULT_TEMPLATE_PATH']

        print(f"选定的模板路径: {template_path}")

        # 使用确认的文字内容继续处理，调用原有的LLM处理逻辑
        # 这里直接复制process_multiple_files_async中LLM处理后的部分

        # 引入并行处理器
        from document.parallel_processor import ParallelProcessor

        # 创建并行处理器实例
        parallel_processor = ParallelProcessor(debug=True)

        # 准备当事人处理器配置
        party_processor_config = {
            'api_url': app.config['LLM_API_URL'],
            'api_key': app.config['LLM_API_KEY'],
            'model': app.config['LLM_MODEL'],
            'debug': app.config.get('DEBUG', False)
        }

        # 准备LLM处理器配置
        llm_processor_config = {
            'api_url': app.config['LLM_API_URL'],
            'api_key': app.config['LLM_API_KEY'],
            'model': app.config['LLM_MODEL'],
            'classification_model': app.config['CLASSIFICATION_MODEL'],
            'classification_api_url': app.config['CLASSIFICATION_API_URL'],
            'classification_api_key': app.config['CLASSIFICATION_API_KEY']
        }

        # 并行执行当事人提取和占位符内容提取
        party_info, formatted_text = parallel_processor.extract_party_and_placeholders_parallel(
            text, template_path, party_processor_config, llm_processor_config,
            selected_template_type, classification_mode, flask_app=app
        )

        print(f"并行处理完成 - 当事人信息: {len(party_info)} 个列表, 格式化文本: {len(formatted_text)} 个字段")

        # 继续文档生成流程
        generate_final_document(formatted_text, party_info, template_path, download_dir, task_id)

    except Exception as e:
        app.config['PROCESSING_STATUS'] = {
            'status': '错误',
            'message': f'LLM处理错误: {str(e)}'
        }
        print(f"LLM processing error: {str(e)}")

def generate_final_document(formatted_text, party_info, template_path, download_dir, task_id):
    """生成最终的Word文档"""
    try:
        # 更新状态为生成文档
        app.config['PROCESSING_STATUS'] = {
            'status': '生成文档',
            'message': '正在生成Word文档',
            'progress': 90
        }
        print(f"Document generation, status set to: {app.config['PROCESSING_STATUS']}")

        # 检查是否启用预览编辑
        if os.environ.get('PREVIEW_EDIT', 'false').lower() == 'true':
            print("预览编辑已启用，开始生成完整预览数据...")

            # 生成完整的预览数据，包括当事人模板处理后的内容
            try:
                complete_preview_data = generate_complete_preview_data(
                    formatted_text, template_path, party_info
                )
                print(f"完整预览数据生成成功，包含字段: {list(complete_preview_data.keys())}")

                # 保存完整的预览数据到临时存储
                from utils.preview_storage import get_preview_storage
                storage = get_preview_storage()
                file_id = f'task_{task_id}'
                preview_id = storage.save_preview_data(complete_preview_data, template_path, file_id)

                # 返回所见即所得编辑URL
                app.config['PROCESSING_STATUS'] = {
                    'status': '文档编辑',
                    'message': '请在表单中编辑内容后生成文档',
                    'edit_url': f'/edit-document/{preview_id}'
                }
                print(f"WYSIWYG edit enabled, redirecting to: /edit-document/{preview_id}")
                return

            except Exception as e:
                print(f"生成完整预览数据时出错: {str(e)}")
                # 如果出错，继续使用原始流程

        # 创建文档生成器配置
        doc_generator_config = {
            'use_llm_for_checkbox': True,
            'process_checkboxes': True,
            'llm_api_url': app.config.get('CHECKBOX_LLM_API_URL') or app.config['LLM_API_URL'],
            'llm_api_key': app.config.get('CHECKBOX_LLM_API_KEY') or app.config['LLM_API_KEY'],
            'llm_model': app.config.get('CHECKBOX_LLM_MODEL') or app.config['LLM_MODEL'],
            'llm_temperature': app.config.get('CHECKBOX_LLM_TEMPERATURE') or app.config.get('LLM_TEMPERATURE', 0.3),
            'llm_max_tokens': app.config.get('CHECKBOX_LLM_MAX_TOKENS') or app.config.get('LLM_MAX_TOKENS', 1000),
            'llm_top_p': app.config.get('CHECKBOX_LLM_TOP_P') or app.config.get('LLM_TOP_P', 0.9),
            'case_type_keywords': app.config.get('CASE_TYPE_KEYWORDS', {}),
            'template_mapping': app.config.get('TEMPLATE_MAPPING', {}),
            'ocr_original_text': app.config.get('LAST_OCR_TEXT', ''),
            'debug_mode': app.config.get('DEBUG', True)
        }

        # 创建文档生成器实例
        doc_generator = DocumentGenerator(
            template_path=template_path,
            llm_api_url=app.config['LLM_API_URL'],
            llm_api_key=app.config['LLM_API_KEY'],
            config=doc_generator_config
        )

        # 合并当事人信息到格式化文本中
        final_data = formatted_text.copy()
        final_data.update(party_info)

        # 生成Word文档
        print("开始生成Word文档，使用当事人信息模板")
        result_dir = os.path.join(download_dir, task_id)
        output_path = doc_generator.generate_document(final_data, result_dir)

        # 获取下载链接
        if output_path:
            download_filename = os.path.basename(output_path)
            download_url = f"/download/{task_id}/{download_filename}"

            # 将结果存储在会话中
            app.config['LAST_RESULT'] = {
                'result': final_data,
                'download_url': download_url
            }

            # 更新状态为完成
            app.config['PROCESSING_STATUS'] = {
                'status': '完成',
                'message': '处理完成'
            }
            print(f"Processing completed, status set to: {app.config['PROCESSING_STATUS']}")
        else:
            app.config['PROCESSING_STATUS'] = {
                'status': '错误',
                'message': '生成文档失败'
            }
            print(f"Document generation failed")

    except Exception as e:
        app.config['PROCESSING_STATUS'] = {
            'status': '错误',
            'message': f'生成文档错误: {str(e)}'
        }
        print(f"Document generation error: {str(e)}")

def process_multiple_files_async(files, ocr_engine_type, download_dir, classification_mode, template_type, task_id):
    """异步处理多个文件并合并OCR结果"""
    with app.app_context():
        app.config['PROCESSING_STATUS'] = {
            'status': '处理中',
            'message': '正在处理文件'
        }

        try:
            # 创建下载目录
            result_dir = os.path.join(download_dir, task_id)
            os.makedirs(result_dir, exist_ok=True)

            combined_text = ""
            total_files = len(files)

            # 如果OCR引擎类型为空，使用默认配置
            if not ocr_engine_type or ocr_engine_type.strip() == '':
                ocr_engine_type = app.config['OCR_ENGINE']

            # 创建OCR引擎配置
            ocr_config = create_ocr_config(ocr_engine_type)

            # 创建OCR引擎实例 - 使用同一个实例处理所有文件
            ocr_engine = OCRFactory.get_ocr_engine(ocr_engine_type, ocr_config)

            # 逐个处理文件并合并OCR结果
            for idx, file_info in enumerate(files):
                file_path = file_info['saved_path']
                file_name = os.path.basename(file_info['original_name'])

                # 更新处理状态
                progress_percentage = (idx / total_files) * 40  # 总进度的前40%用于OCR
                app.config['PROCESSING_STATUS'] = {
                    'status': 'OCR中',
                    'message': f'正在识别文件 {idx+1}/{total_files}: {file_name}',
                    'progress': progress_percentage
                }

                # 处理文件
                file_text = ocr_engine.process_file(file_path)

                # 添加文件分隔标记并合并文本
                if combined_text:
                    combined_text += f"\n\n--- 下一文件: {file_name} ---\n\n"
                else:
                    combined_text += f"--- 文件: {file_name} ---\n\n"

                combined_text += file_text

            # 保存合并后的OCR原始文本到app.config中，供CHECKBOX处理使用
            app.config['LAST_OCR_TEXT'] = combined_text
            print(f"已保存合并的OCR原始文本到app.config，长度: {len(combined_text)}")

            # 更新状态
            app.config['PROCESSING_STATUS'] = {
                'status': 'LLM处理中',
                'message': '正在分析文本',
                'progress': 50
            }

            # 使用LLM处理合并后的文本
            app.config['PROCESSING_STATUS']['message'] = '使用大模型分析文本'
            llm_processor = LLMProcessor(
                app.config['LLM_API_URL'],
                app.config['LLM_API_KEY'],
                app.config['LLM_MODEL'],
                app.config['CLASSIFICATION_MODEL'],  # 分类模型参数
                app.config['CLASSIFICATION_API_URL'],  # 分类API URL
                app.config['CLASSIFICATION_API_KEY']   # 分类API密钥
            )

            # 第一步：文档分类和模板选择
            app.config['PROCESSING_STATUS'] = {
                'status': 'LLM文档分类',
                'message': '正在分析文档'
            }
            print(f"Document classification, status set to: {app.config['PROCESSING_STATUS']}")

            # 进行文档分类以选择正确的模板
            if classification_mode == "manual" and template_type:
                print(f"手动模式：使用指定的模板类型: {template_type}")
                selected_template_type = template_type
            else:
                print("自动模式：调用LLM进行文档分类...")
                selected_template_type = llm_processor.classify_case_type(combined_text)
                print(f"分类结果: {selected_template_type}")

            # 选择适当的模板
            template_path, _ = select_template({'template_type': selected_template_type}, classification_mode, selected_template_type)
            if template_path is None:
                template_path = app.config['DEFAULT_TEMPLATE_PATH']

            print(f"选定的模板路径: {template_path}")

            # 第二步：并行处理当事人提取和占位符内容提取
            app.config['PROCESSING_STATUS'] = {
                'status': '并行处理',
                'message': '正在并行分析当事人信息和占位符内容'
            }
            print(f"Parallel processing, status set to: {app.config['PROCESSING_STATUS']}")

            # 引入并行处理器
            from document.parallel_processor import ParallelProcessor

            # 创建并行处理器实例
            parallel_processor = ParallelProcessor(debug=True)

            # 准备当事人处理器配置
            party_processor_config = {
                'api_url': app.config['LLM_API_URL'],
                'api_key': app.config['LLM_API_KEY'],
                'model': app.config['LLM_MODEL'],
                'debug': app.config.get('DEBUG', False)
            }

            # 准备LLM处理器配置
            llm_processor_config = {
                'api_url': app.config['LLM_API_URL'],
                'api_key': app.config['LLM_API_KEY'],
                'model': app.config['LLM_MODEL'],
                'classification_model': app.config['CLASSIFICATION_MODEL'],
                'classification_api_url': app.config['CLASSIFICATION_API_URL'],
                'classification_api_key': app.config['CLASSIFICATION_API_KEY']
            }

            # 并行执行当事人提取和占位符内容提取
            party_info, formatted_text = parallel_processor.extract_party_and_placeholders_parallel(
                combined_text, template_path, party_processor_config, llm_processor_config,
                selected_template_type, classification_mode, flask_app=app
            )

            print(f"并行处理完成 - 当事人信息: {len(party_info)} 个列表, 格式化文本: {len(formatted_text)} 个字段")

            # 检查并行处理结果
            if "error" in formatted_text:
                print(f"警告: 占位符内容提取失败: {formatted_text['error']}")
                # 如果并行处理失败，回退到原始流程
                app.config['PROCESSING_STATUS'] = {
                    'status': 'LLM占位符处理',
                    'message': '正在分析占位符信息（回退模式）'
                }
                print(f"Fallback placeholder processing, status set to: {app.config['PROCESSING_STATUS']}")

                # 生成中间文档
                from document.intermediate_processor import IntermediateDocumentProcessor
                intermediate_processor = IntermediateDocumentProcessor(template_path=template_path, debug=True)
                intermediate_doc, remaining_placeholders = intermediate_processor.create_intermediate_document(party_info)

                # 使用原始方法处理占位符
                formatted_text = llm_processor.process_text_with_placeholders(
                    combined_text, selected_template_type, classification_mode,
                    remaining_placeholders, intermediate_processor.get_document_text(intermediate_doc)
                )

                # 合并当事人信息
                if isinstance(formatted_text, dict):
                    formatted_text.update(party_info)
                    formatted_text['template_type'] = selected_template_type
                    print("当事人信息已合并到格式化文本中（回退模式）")
            else:
                print("并行处理成功，跳过中间文档生成步骤")

            # 在生成文档前处理CHECKBOX
            app.config['PROCESSING_STATUS'] = {
                'status': '处理复选框',
                'message': '正在分析文档中的复选框'
            }
            print(f"CHECKBOX processing, status set to: {app.config['PROCESSING_STATUS']}")

            # 创建文档生成器
            app.config['PROCESSING_STATUS'] = {
                'status': '生成文档',
                'message': '正在生成Word文档'
            }
            print(f"Document generation, status set to: {app.config['PROCESSING_STATUS']}")

            # 确保模板路径存在
            if not os.path.exists(template_path):
                raise FileNotFoundError(f"模板文件不存在: {template_path}")

            print(f"最终使用的模板路径: {template_path}")
            app.config['FORMATTED_TEXT'] = formatted_text

            # 创建文档生成器配置
            doc_generator_config = {
                'use_llm_for_checkbox': True,
                'process_checkboxes': True,
                'llm_api_url': app.config.get('CHECKBOX_LLM_API_URL') or app.config['LLM_API_URL'],
                'llm_api_key': app.config.get('CHECKBOX_LLM_API_KEY') or app.config['LLM_API_KEY'],
                'llm_model': app.config.get('CHECKBOX_LLM_MODEL') or app.config['LLM_MODEL'],
                'llm_temperature': app.config.get('CHECKBOX_LLM_TEMPERATURE') or app.config.get('LLM_TEMPERATURE', 0.3),
                'llm_max_tokens': app.config.get('CHECKBOX_LLM_MAX_TOKENS') or app.config.get('LLM_MAX_TOKENS', 1000),
                'llm_top_p': app.config.get('CHECKBOX_LLM_TOP_P') or app.config.get('LLM_TOP_P', 0.9),
                'case_type_keywords': app.config.get('CASE_TYPE_KEYWORDS', {}),
                'template_mapping': app.config.get('TEMPLATE_MAPPING', {}),
                'ocr_original_text': combined_text,
                'debug_mode': app.config.get('DEBUG', True)  # 启用调试模式，记录更多日志
            }

            # 添加检测到的模板类型
            if 'template_type' in formatted_text:
                doc_generator_config['detected_template_type'] = formatted_text['template_type']

            # 检查是否启用预览编辑
            if os.environ.get('PREVIEW_EDIT', 'false').lower() == 'true':
                print("批量处理预览编辑已启用，开始生成完整预览数据...")

                # 生成完整的预览数据，包括当事人模板处理后的内容
                try:
                    complete_preview_data = generate_complete_preview_data(
                        formatted_text, template_path, party_info
                    )
                    print(f"批量处理完整预览数据生成成功，包含字段: {list(complete_preview_data.keys())}")

                    # 检查是否包含中间文档内容
                    if '_complete_document_text' in complete_preview_data:
                        print("✅ 批量处理完整文档内容已包含在预览数据中")
                    else:
                        print("⚠️ 警告：批量处理完整文档内容未包含在预览数据中")

                except Exception as e:
                    print(f"批量处理生成完整预览数据时出错: {str(e)}")
                    # 如果出错，使用原始数据
                    complete_preview_data = formatted_text

                # 保存完整的预览数据到临时存储
                from utils.preview_storage import get_preview_storage
                storage = get_preview_storage()
                # 使用任务ID作为file_id
                file_id = task_id if 'task_id' in locals() else 'batch_files'
                preview_id = storage.save_preview_data(complete_preview_data, template_path, file_id)

                # 返回所见即所得编辑URL
                app.config['PROCESSING_STATUS'] = {
                    'status': '文档编辑',
                    'message': '请在表单中编辑内容后生成文档',
                    'edit_url': f'/edit-document/{preview_id}'
                }
                print(f"WYSIWYG edit enabled for batch, redirecting to: /edit-document/{preview_id}")
                return

            # 创建文档生成器实例
            doc_generator = DocumentGenerator(
                template_path=template_path,
                llm_api_url=app.config['LLM_API_URL'],
                llm_api_key=app.config['LLM_API_KEY'],
                config=doc_generator_config
            )

            # 生成文档 - 与单文件处理保持一致，传递目录
            print("开始生成Word文档，使用当事人信息模板")
            output_path = doc_generator.generate_document(formatted_text, result_dir)

            if output_path:
                # 获取相对路径用于下载
                download_filename = os.path.basename(output_path)
                download_url = f"/download/{task_id}/{download_filename}"

                # 更新处理状态为完成
                app.config['PROCESSING_STATUS'] = {
                    'status': '完成',
                    'message': '处理完成',
                    'progress': 100,
                }

                # 与单文件处理保持一致，使用LAST_RESULT
                app.config['LAST_RESULT'] = {
                    'result': formatted_text,
                    'download_url': download_url
                }

                print(f"处理完成，下载链接: {download_url}")
            else:
                app.config['PROCESSING_STATUS'] = {
                    'status': '错误',
                    'message': '生成文档失败'
                }

        except Exception as e:
            app.config['PROCESSING_STATUS'] = {
                'status': '错误',
                'message': f'处理错误: {str(e)}'
            }
            print(f"Processing error: {str(e)}, status set to: {app.config['PROCESSING_STATUS']}")
            raise

@app.route('/api/template-hierarchy')
def get_template_hierarchy():
    """返回分层级的模板结构"""
    return jsonify(app.config['TEMPLATE_BATCHES'])

@app.route('/api/templates2', methods=['GET'])
def get_templates2():
    """返回templates2目录中的所有模板文件"""
    templates_dir = os.path.join(app.static_folder, 'templates2')

    # 确保目录存在
    if not os.path.exists(templates_dir):
        return jsonify({'templates': []})

    # 获取目录中的所有docx文件
    templates = []
    for file in os.listdir(templates_dir):
        if file.endswith('.docx') and not file.startswith('~') and not file.startswith('.'):
            # 提取文件名（不含扩展名）作为显示名称
            display_name = os.path.splitext(file)[0]
            # 如果文件名以数字和连字符开头（如"1-xxx"），则移除前缀
            import re
            match = re.match(r'^\d+-(.+)$', display_name)
            if match:
                display_name = match.group(1)

            templates.append({
                'filename': file,
                'name': display_name
            })

    return jsonify({'templates': templates})

@app.route('/download-template/<path:filename>', methods=['GET'])
def download_template(filename):
    """下载templates2目录中的模板文件"""
    templates_dir = os.path.join(app.static_folder, 'templates2')

    # 验证文件名，防止目录遍历攻击
    if '..' in filename or filename.startswith('/'):
        return "无效的文件名", 400

    # 确保文件存在
    file_path = os.path.join(templates_dir, filename)
    if not os.path.exists(file_path) or not os.path.isfile(file_path):
        return "文件不存在", 404

    return send_from_directory(directory=templates_dir, path=filename, as_attachment=True)

@app.route('/print-template/<path:filename>', methods=['POST'])
def print_template(filename):
    """直接打印templates-raw目录中的原始模板文件"""
    templates_dir = os.path.join(app.static_folder, 'templates-raw')

    # 验证文件名，防止目录遍历攻击
    if '..' in filename or filename.startswith('/'):
        return jsonify({
            'success': False,
            'message': '无效的文件名'
        }), 400

    # 确保文件存在
    file_path = os.path.join(templates_dir, filename)
    if not os.path.exists(file_path) or not os.path.isfile(file_path):
        return jsonify({
            'success': False,
            'message': '文件不存在'
        }), 404

    try:
        # 获取打印机信息
        default_printer = printer_service.get_default_printer()
        if not default_printer:
            print("未找到默认打印机")
            return jsonify({
                'success': False,
                'message': '未找到默认打印机',
            }), 500

        # 打印文件
        job_id = printer_service.print_file(file_path, default_printer, f"打印模板: {filename}")

        if job_id:
            return jsonify({
                'success': True,
                'message': f'文件已发送到打印机 {default_printer}',
                'job_id': job_id
            })
        else:
            return jsonify({
                'success': False,
                'message': '打印失败，请检查打印机状态'
            }), 500
    except Exception as e:
        print(f"打印文件时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'打印出错: {str(e)}'
        }), 500

@app.route('/print-document/<path:filepath>', methods=['POST'])
def print_document(filepath):
    """直接打印生成的文档文件

    支持两种路径格式：
    1. /print-document/filename - 直接从下载目录获取文件
    2. /print-document/task_id/filename - 从任务特定的子目录获取文件
    """
    # 添加调试日志
    print(f"打印文档请求: filepath={filepath}")

    # 分割路径
    parts = filepath.split('/')
    print(f"分割后的路径部分: {parts}")

    # 确定文件路径
    if len(parts) == 1:
        # 单个文件名，直接从下载目录获取
        file_path = os.path.join(download_dir, parts[0])
        print(f"单文件模式，完整路径: {file_path}")
    elif len(parts) == 2:
        # 任务ID和文件名，从任务子目录获取
        task_id = parts[0]
        filename = parts[1]
        file_path = os.path.join(download_dir, task_id, filename)
        print(f"任务子目录模式，完整路径: {file_path}")
    else:
        # 无效的路径
        print(f"无效的路径格式: {parts}")
        return jsonify({
            'success': False,
            'message': '无效的文件路径'
        }), 400

    # 确保文件存在
    if not os.path.exists(file_path) or not os.path.isfile(file_path):
        return jsonify({
            'success': False,
            'message': '文件不存在'
        }), 404

    try:
        # 获取打印机信息
        default_printer = printer_service.get_default_printer()
        if not default_printer:
            print("未找到默认打印机")
            return jsonify({
                'success': False,
                'message': '未找到默认打印机',
            }), 500

        # 打印文件
        job_id = printer_service.print_file(file_path, default_printer, f"打印文档: {os.path.basename(file_path)}")

        if job_id:
            return jsonify({
                'success': True,
                'message': f'文件已发送到打印机 {default_printer}',
                'job_id': job_id
            })
        else:
            return jsonify({
                'success': False,
                'message': '打印失败，请检查打印机状态'
            }), 500
    except Exception as e:
        print(f"打印文件时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'打印出错: {str(e)}'
        }), 500

# 二维码和移动端相关路由
@app.route('/generate-qr-code')
def generate_qr_code():
    """生成用于扫码上传的二维码"""
    try:
        # 生成会话ID
        session_id = str(uuid.uuid4())

        # 创建移动端上传URL
        if app.config.get('USE_EXTERNAL_URL', False) and app.config.get('EXTERNAL_URL'):
            # 使用配置的外部URL
            base_url = app.config.get('EXTERNAL_URL').rstrip('/')
            print(f"使用外部URL生成二维码: {base_url}")
        else:
            # 使用请求的主机URL
            base_url = request.host_url.rstrip('/')
            print(f"使用本地URL生成二维码: {base_url}")

        mobile_url = f"{base_url}/mobile-upload/{session_id}"

        # 生成二维码
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(mobile_url)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        # 将图片保存为临时文件
        buffer = BytesIO()
        img.save(buffer)
        buffer.seek(0)

        # 编码为Base64
        img_str = base64.b64encode(buffer.getvalue()).decode('utf-8')
        data_url = f"data:image/png;base64,{img_str}"

        return jsonify({
            'success': True,
            'qr_code_url': data_url,
            'session_id': session_id,
            'mobile_url': mobile_url
        })
    except Exception as e:
        print(f"生成二维码错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"生成二维码错误: {str(e)}"
        }), 500

@app.route('/generate-download-qr-code')
def generate_download_qr_code():
    """生成用于下载文件的二维码"""
    try:
        # 获取下载URL参数
        download_url = request.args.get('url')
        if not download_url:
            return jsonify({
                'success': False,
                'message': '缺少下载URL参数'
            }), 400

        # 确保URL是完整的绝对URL
        if download_url.startswith('/'):
            # 如果是相对URL，转换为绝对URL
            if app.config.get('USE_EXTERNAL_URL', False) and app.config.get('EXTERNAL_URL'):
                # 使用配置的外部URL
                base_url = app.config.get('EXTERNAL_URL').rstrip('/')
                print(f"使用外部URL生成下载二维码: {base_url}")
            else:
                # 使用请求的主机URL
                base_url = request.host_url.rstrip('/')
                print(f"使用本地URL生成下载二维码: {base_url}")

            # 构建完整URL，确保URL中的中文字符正确编码
            from urllib.parse import quote
            path_part = download_url.lstrip('/')
            # 对路径部分进行URL编码，确保中文字符正确处理
            encoded_path = quote(path_part)
            full_url = f"{base_url}/{encoded_path}"
        else:
            # 已经是完整URL
            full_url = download_url

        print(f"生成下载二维码的URL: {full_url}")

        # 生成二维码
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(full_url)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        # 将图片保存为临时文件
        buffer = BytesIO()
        img.save(buffer)
        buffer.seek(0)

        # 编码为Base64
        img_str = base64.b64encode(buffer.getvalue()).decode('utf-8')
        data_url = f"data:image/png;base64,{img_str}"

        return jsonify({
            'success': True,
            'qr_code_url': data_url,
            'download_url': full_url
        })
    except Exception as e:
        print(f"生成下载二维码错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f"生成下载二维码错误: {str(e)}"
        }), 500

@app.route('/mobile-upload/<session_id>')
def mobile_upload(session_id):
    """移动端上传页面"""
    if not session_id:
        abort(404)

    return render_template('mobile_upload.html', session_id=session_id)

# 清理临时文件
@app.route('/clean-temp-files', methods=['POST'])
def clean_temp_files():
    """清理临时文件，包括上传的文件和生成的下载文件"""
    try:
        # 获取当前用户的会话ID
        session_id = request.cookies.get('session_id', '')

        # 如果没有会话ID，生成一个随机ID作为标识
        if not session_id:
            session_id = str(uuid.uuid4())

        # 记录清理操作
        print(f"用户 {session_id} 请求清理临时文件")

        # 清理uploads目录
        uploads_dir = app.config.get('UPLOAD_FOLDER', 'uploads')
        cleaned_uploads = clean_directory(uploads_dir)

        # 清理downloads目录
        downloads_dir = os.path.join('static', 'downloads')
        cleaned_downloads = clean_directory(downloads_dir)

        return jsonify({
            'success': True,
            'message': f'成功清理临时文件: {cleaned_uploads + cleaned_downloads} 个文件已删除',
            'details': {
                'uploads_cleaned': cleaned_uploads,
                'downloads_cleaned': cleaned_downloads
            }
        })
    except Exception as e:
        print(f"清理临时文件错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'清理临时文件错误: {str(e)}'
        }), 500

# 清理指定目录中的所有文件
def clean_directory(directory):
    """清理指定目录中的所有文件，返回删除的文件数量"""
    count = 0
    try:
        if os.path.exists(directory):
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                try:
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                        count += 1
                    elif os.path.isdir(file_path):
                        # 递归清理子目录
                        count += clean_directory(file_path)
                except Exception as e:
                    print(f"删除文件 {file_path} 时出错: {str(e)}")
    except Exception as e:
        print(f"清理目录 {directory} 时出错: {str(e)}")

    return count

# Socket.IO事件处理
@socketio.on('connect')
def handle_connect():
    """处理客户端连接"""
    print(f"客户端已连接: {request.sid}")

@socketio.on('disconnect')
def handle_disconnect():
    """处理客户端断开连接"""
    print(f"客户端已断开连接: {request.sid}")

@socketio.on('join_session')
def handle_join_session(data):
    """处理客户端加入会话"""
    session_id = data.get('session_id')
    client_type = data.get('client_type', 'unknown')

    if session_id:
        join_room(session_id)
        print(f"客户端 {request.sid} ({client_type}) 已加入会话: {session_id}")

        # 如果是移动端加入，通知PC端
        if client_type == 'mobile':
            emit('mobile_joined', room=session_id, skip_sid=request.sid)

@socketio.on('mobile_upload')
def handle_mobile_upload(data):
    """处理移动端上传的文件"""
    try:
        session_id = data.get('session_id')

        if not session_id:
            emit('upload_response', {
                'success': False,
                'message': '会话ID不存在'
            })
            return

        # 获取文件数据
        file_data = {
            'filename': data.get('filename'),
            'file_type': data.get('file_type'),
            'file_size': data.get('file_size'),
            'data': data.get('data'),
            'total_files': data.get('total_files', 1),
            'current_file': data.get('current_file', 1)
        }

        # 将文件数据发送给PC端
        emit('file_uploaded', {
            'file_data': file_data
        }, room=session_id, skip_sid=request.sid)

        print(f"已从移动端接收文件: {file_data['filename']} ({file_data['current_file']}/{file_data['total_files']})")

        # 直接向发送请求的客户端(移动端)发送成功响应
        emit('upload_response', {
            'success': True,
            'message': f"文件上传成功 ({file_data['current_file']}/{file_data['total_files']})"
        })

    except Exception as e:
        print(f"处理移动端上传错误: {str(e)}")
        # 向发送请求的客户端发送错误响应
        emit('upload_response', {
            'success': False,
            'message': f"处理上传错误: {str(e)}"
        })

@socketio.on('upload_response')
def handle_upload_response(data):
    """处理上传响应"""
    session_id = data.get('session_id')

    if session_id:
        # 将响应转发给移动端
        emit('upload_response', {
            'success': data.get('success', False),
            'message': data.get('message', '')
        }, room=session_id, skip_sid=request.sid)

# 注册Web表单路由
try:
    from web_form_routes import register_web_form_routes
    register_web_form_routes(app)
    print("Web表单路由注册成功")
except Exception as e:
    print(f"Web表单路由注册失败: {str(e)}")

# 注册预览编辑路由
try:
    from preview_edit_routes import register_preview_edit_routes
    register_preview_edit_routes(app)
    print("预览编辑路由注册成功")
except Exception as e:
    print(f"预览编辑路由注册失败: {str(e)}")

if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=8888, debug=True)
