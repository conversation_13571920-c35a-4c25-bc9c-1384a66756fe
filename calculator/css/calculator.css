/* 司法计算器样式文件 */
/* 针对触摸屏设备优化 */

/* 限制重置样式到容器内部，避免影响侧边导航 */
.container * {
    box-sizing: border-box;
}

html {
    /* 禁止文本选择，防止长按触发上下文菜单 */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
    background-color: #f0f2f5;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    -webkit-tap-highlight-color: transparent; /* 移除触摸高亮效果 */
    /* 禁止长按操作 */
    -webkit-touch-callout: none;
}

.container {
    flex: 1;
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    margin: 30px 30px 30px 230px; /* 为固定侧边导航留出空间 - 与touchscreen保持一致 */
}

/* 头部样式 */
header {
    background-color: #4a90e2; /* Kiosk 风格蓝色 - 与touchscreen保持一致 */
    color: #fff;
    padding: 30px 40px;
    text-align: center;
}

header h1 {
    font-size: 2.5em;
    margin: 0 0 10px 0;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-logo {
    height: 40px;
    margin-right: 15px;
    vertical-align: middle;
}

header p {
    font-size: 1.2em;
    margin: 0;
    opacity: 0.9;
}

/* 主导航样式 */
.main-nav {
    display: flex;
    background-color: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.nav-btn {
    flex: 1;
    min-width: 200px;
    padding: 20px 15px;
    border: none;
    background-color: transparent;
    color: #6c757d;
    font-size: 1.1em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    border-bottom: 3px solid transparent;
}

.nav-btn i {
    font-size: 1.5em;
    margin-bottom: 5px;
}

.nav-btn:hover {
    background-color: #e9ecef;
    color: #495057;
}

.nav-btn.active {
    background-color: #fff;
    color: #4a90e2;
    border-bottom-color: #4a90e2;
    box-shadow: 0 -2px 10px rgba(74, 144, 226, 0.1);
}

/* 主内容区域 */
.main-content {
    padding: 40px;
    min-height: calc(100vh - 200px);
}

/* 计算器模块 */
.calculator-module {
    display: none;
}

.calculator-module.active {
    display: block;
}

.module-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.module-header h2 {
    font-size: 2.2em;
    color: #2c3e50;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.module-header h2 i {
    color: #4a90e2;
}

.module-header p {
    font-size: 1.1em;
    color: #6c757d;
    margin: 0;
}

/* 标签页样式 */
.calculator-tabs {
    display: flex;
    margin-bottom: 30px;
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 5px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.tab-btn {
    flex: 1;
    min-width: 150px;
    padding: 15px 20px;
    border: none;
    background-color: transparent;
    color: #6c757d;
    font-size: 1em;
    font-weight: 500;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-btn:hover {
    background-color: #e9ecef;
    color: #495057;
}

.tab-btn.active {
    background-color: #4a90e2;
    color: white;
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

/* 标签页内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 输入区域 */
.input-section {
    background-color: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.input-group {
    margin-bottom: 25px;
}

.input-group label {
    display: block;
    font-size: 1.1em;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.touch-input {
    width: 100%;
    padding: 15px 20px;
    font-size: 1.1em;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    background-color: #fff;
    color: #495057;
    transition: all 0.3s ease;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.touch-input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.touch-input.small {
    width: 150px;
    margin-left: 10px;
}

/* 计算按钮 */
.calculate-btn {
    width: 100%;
    padding: 18px 30px;
    font-size: 1.3em;
    font-weight: 600;
    color: white;
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    border: none;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
    margin-top: 20px;
}

.calculate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

.calculate-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(74, 144, 226, 0.3);
}

/* 结果区域 */
.result-section {
    background-color: #fff;
    padding: 30px;
    border-radius: 15px;
    border: 2px solid #e9ecef;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.result-section h3 {
    font-size: 1.5em;
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.result-section h3 i {
    color: #28a745;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
    font-size: 1.2em;
}

.result-label {
    font-weight: 600;
    color: #495057;
}

.result-value {
    font-weight: 700;
    color: #28a745;
    font-size: 1.3em;
}

/* 计算详情 */
.calculation-details {
    margin-top: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #4a90e2;
}

.calculation-step {
    margin-bottom: 10px;
    font-size: 1em;
    color: #6c757d;
}

.calculation-step:last-child {
    margin-bottom: 0;
}

/* 操作按钮 */
.result-actions {
    display: flex;
    gap: 15px;
    margin-top: 25px;
    flex-wrap: wrap;
    justify-content: center;
}

.action-btn {
    min-width: 150px;
    padding: 12px 20px;
    font-size: 1em;
    font-weight: 500;
    color: white;
    background-color: #6c757d;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.action-btn:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
}

/* 复选框组 */
.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

/* 复选框相关的输入框默认隐藏 */
#lost-work-days,
#daily-income,
#nursing-days,
#nutrition-days,
#disability-level {
    display: none;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 1.1em;
    color: #495057;
    min-width: 120px;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 24px;
    height: 24px;
    border: 2px solid #4a90e2;
    border-radius: 4px;
    margin-right: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: #4a90e2;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
}



/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4em;
    margin-bottom: 20px;
    color: #dee2e6;
}

.empty-state h3 {
    font-size: 1.5em;
    margin-bottom: 10px;
    color: #495057;
}

.empty-state p {
    font-size: 1em;
    margin: 0;
}

/* 加载状态样式 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6c757d;
}

.loading i {
    font-size: 2em;
    margin-right: 15px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 赔偿项目样式 */
.compensation-items {
    margin-top: 25px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.compensation-items h4 {
    font-size: 1.2em;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

/* 提示信息样式 */
.info-tip {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    color: #0c5460;
    font-size: 0.95em;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.info-tip i {
    color: #17a2b8;
    margin-top: 2px;
}

.warning-tip {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    color: #856404;
    font-size: 0.95em;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.warning-tip i {
    color: #ffc107;
    margin-top: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        padding: 20px;
    }

    .input-section {
        padding: 20px;
    }

    .result-section {
        padding: 20px;
    }

    header h1 {
        font-size: 2em;
    }

    .nav-btn {
        min-width: 150px;
        padding: 15px 10px;
        font-size: 1em;
    }

    .result-actions {
        flex-direction: column;
    }

    .action-btn {
        min-width: auto;
    }

    .checkbox-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .touch-input.small {
        width: 100%;
        margin-left: 0;
        margin-top: 10px;
    }

    .compensation-items {
        padding: 15px;
    }


}

@media (max-width: 480px) {
    header {
        padding: 20px;
    }

    header h1 {
        font-size: 1.8em;
        flex-direction: column;
        gap: 10px;
    }

    .main-content {
        padding: 15px;
    }

    .calculator-tabs {
        flex-direction: column;
        gap: 5px;
    }

    .tab-btn {
        min-width: auto;
    }

    .result-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .result-value {
        font-size: 1.1em;
    }
}

/* 侧边导航样式已移至共享文件 /static/css/shared-navigation.css */

/* 响应式设计已移至共享文件 /static/css/shared-navigation.css */
