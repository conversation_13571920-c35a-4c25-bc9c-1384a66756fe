/**
 * 司法计算标准数据
 * 包含各地区的赔偿标准、收费标准等基础数据
 */

const JudicialStandards = {
    // 版本信息
    version: '2024.12',
    lastUpdated: '2024-12-01',
    
    // 全国各省市赔偿标准
    compensationStandards: {
        // 直辖市
        beijing: {
            name: '北京市',
            code: 'beijing',
            type: 'municipality',
            year: 2024,
            urbanDisposableIncome: 85415,
            ruralDisposableIncome: 33195,
            urbanConsumption: 52000,
            ruralConsumption: 22000,
            averageWage: 194651
        },
        shanghai: {
            name: '上海市',
            code: 'shanghai',
            type: 'municipality',
            year: 2024,
            urbanDisposableIncome: 88366,
            ruralDisposableIncome: 40000,
            urbanConsumption: 55000,
            ruralConsumption: 25000,
            averageWage: 171549
        },
        tianjin: {
            name: '天津市',
            code: 'tianjin',
            type: 'municipality',
            year: 2024,
            urbanDisposableIncome: 53581,
            ruralDisposableIncome: 28090,
            urbanConsumption: 35000,
            ruralConsumption: 18000,
            averageWage: 89268
        },
        chongqing: {
            name: '重庆市',
            code: 'chongqing',
            type: 'municipality',
            year: 2024,
            urbanDisposableIncome: 39713,
            ruralDisposableIncome: 20000,
            urbanConsumption: 26000,
            ruralConsumption: 14000,
            averageWage: 75000
        },
        
        // 省份
        guangdong: {
            name: '广东省',
            code: 'guangdong',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 51474,
            ruralDisposableIncome: 22306,
            urbanConsumption: 35130,
            ruralConsumption: 17275,
            averageWage: 95000
        },
        jiangsu: {
            name: '江苏省',
            code: 'jiangsu',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 55415,
            ruralDisposableIncome: 26791,
            urbanConsumption: 37239,
            ruralConsumption: 19145,
            averageWage: 105000
        },
        zhejiang: {
            name: '浙江省',
            code: 'zhejiang',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 67015,
            ruralDisposableIncome: 37565,
            urbanConsumption: 42507,
            ruralConsumption: 24798,
            averageWage: 115000
        },
        shandong: {
            name: '山东省',
            code: 'shandong',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 42077,
            ruralDisposableIncome: 22000,
            urbanConsumption: 28000,
            ruralConsumption: 15000,
            averageWage: 85000
        },
        fujian: {
            name: '福建省',
            code: 'fujian',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 47857,
            ruralDisposableIncome: 24000,
            urbanConsumption: 32000,
            ruralConsumption: 16000,
            averageWage: 88000
        },
        liaoning: {
            name: '辽宁省',
            code: 'liaoning',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 39844,
            ruralDisposableIncome: 20000,
            urbanConsumption: 26000,
            ruralConsumption: 14000,
            averageWage: 78000
        },
        neimenggu: {
            name: '内蒙古自治区',
            code: 'neimenggu',
            type: 'autonomous_region',
            year: 2024,
            urbanDisposableIncome: 40077,
            ruralDisposableIncome: 19000,
            urbanConsumption: 26000,
            ruralConsumption: 13000,
            averageWage: 75000
        },
        hunan: {
            name: '湖南省',
            code: 'hunan',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 37679,
            ruralDisposableIncome: 18000,
            urbanConsumption: 25000,
            ruralConsumption: 13000,
            averageWage: 72000
        },
        hubei: {
            name: '湖北省',
            code: 'hubei',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 36947,
            ruralDisposableIncome: 18500,
            urbanConsumption: 24500,
            ruralConsumption: 13500,
            averageWage: 74000
        },
        anhui: {
            name: '安徽省',
            code: 'anhui',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 36782,
            ruralDisposableIncome: 18000,
            urbanConsumption: 24000,
            ruralConsumption: 13000,
            averageWage: 70000
        },
        jiangxi: {
            name: '江西省',
            code: 'jiangxi',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 36007,
            ruralDisposableIncome: 17500,
            urbanConsumption: 23500,
            ruralConsumption: 12500,
            averageWage: 68000
        },
        hainan: {
            name: '海南省',
            code: 'hainan',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 34829,
            ruralDisposableIncome: 17000,
            urbanConsumption: 23000,
            ruralConsumption: 12000,
            averageWage: 66000
        },
        hebei: {
            name: '河北省',
            code: 'hebei',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 34655,
            ruralDisposableIncome: 17000,
            urbanConsumption: 22500,
            ruralConsumption: 12000,
            averageWage: 65000
        },
        sichuan: {
            name: '四川省',
            code: 'sichuan',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 34325,
            ruralDisposableIncome: 16500,
            urbanConsumption: 22000,
            ruralConsumption: 11500,
            averageWage: 64000
        },
        shaanxi: {
            name: '陕西省',
            code: 'shaanxi',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 33905,
            ruralDisposableIncome: 16992,
            urbanConsumption: 22000,
            ruralConsumption: 15647,
            averageWage: 102041
        },
        ningxia: {
            name: '宁夏回族自治区',
            code: 'ningxia',
            type: 'autonomous_region',
            year: 2024,
            urbanDisposableIncome: 33355,
            ruralDisposableIncome: 16000,
            urbanConsumption: 21500,
            ruralConsumption: 11000,
            averageWage: 62000
        },
        shanxi: {
            name: '山西省',
            code: 'shanxi',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 32441,
            ruralDisposableIncome: 15500,
            urbanConsumption: 21000,
            ruralConsumption: 10500,
            averageWage: 60000
        },
        henan: {
            name: '河南省',
            code: 'henan',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 31552,
            ruralDisposableIncome: 15000,
            urbanConsumption: 20500,
            ruralConsumption: 10000,
            averageWage: 58000
        },
        xizang: {
            name: '西藏自治区',
            code: 'xizang',
            type: 'autonomous_region',
            year: 2024,
            urbanDisposableIncome: 31358,
            ruralDisposableIncome: 15000,
            urbanConsumption: 20000,
            ruralConsumption: 10000,
            averageWage: 55000
        },
        jilin: {
            name: '吉林省',
            code: 'jilin',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 31318,
            ruralDisposableIncome: 14500,
            urbanConsumption: 20000,
            ruralConsumption: 9500,
            averageWage: 56000
        },
        heilongjiang: {
            name: '黑龙江省',
            code: 'heilongjiang',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 31296,
            ruralDisposableIncome: 14500,
            urbanConsumption: 20000,
            ruralConsumption: 9500,
            averageWage: 55000
        },
        guangxi: {
            name: '广西壮族自治区',
            code: 'guangxi',
            type: 'autonomous_region',
            year: 2024,
            urbanDisposableIncome: 31125,
            ruralDisposableIncome: 14000,
            urbanConsumption: 19500,
            ruralConsumption: 9000,
            averageWage: 54000
        },
        xinjiang: {
            name: '新疆维吾尔自治区',
            code: 'xinjiang',
            type: 'autonomous_region',
            year: 2024,
            urbanDisposableIncome: 30899,
            ruralDisposableIncome: 14000,
            urbanConsumption: 19000,
            ruralConsumption: 9000,
            averageWage: 53000
        },
        qinghai: {
            name: '青海省',
            code: 'qinghai',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 30117,
            ruralDisposableIncome: 13500,
            urbanConsumption: 18500,
            ruralConsumption: 8500,
            averageWage: 52000
        },
        yunnan: {
            name: '云南省',
            code: 'yunnan',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 29932,
            ruralDisposableIncome: 13000,
            urbanConsumption: 18000,
            ruralConsumption: 8000,
            averageWage: 50000
        },
        guizhou: {
            name: '贵州省',
            code: 'guizhou',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 28561,
            ruralDisposableIncome: 12500,
            urbanConsumption: 17500,
            ruralConsumption: 7500,
            averageWage: 48000
        },
        gansu: {
            name: '甘肃省',
            code: 'gansu',
            type: 'province',
            year: 2024,
            urbanDisposableIncome: 26612,
            ruralDisposableIncome: 12000,
            urbanConsumption: 16500,
            ruralConsumption: 7000,
            averageWage: 45000
        }
    },
    
    // LPR利率历史数据
    lprRates: {
        '2024-01': { oneYear: 3.45, fiveYear: 4.20 },
        '2023-12': { oneYear: 3.45, fiveYear: 4.20 },
        '2023-11': { oneYear: 3.45, fiveYear: 4.20 },
        '2023-10': { oneYear: 3.45, fiveYear: 4.20 },
        '2023-09': { oneYear: 3.45, fiveYear: 4.20 },
        '2023-08': { oneYear: 3.45, fiveYear: 4.20 },
        '2023-07': { oneYear: 3.55, fiveYear: 4.20 },
        '2023-06': { oneYear: 3.55, fiveYear: 4.20 }
    },
    
    // 工伤赔偿标准
    workInjuryStandards: {
        // 2024年一次性工亡补助金标准（51821×20倍）
        deathSubsidy: 1036420,
        
        // 伤残等级对应的补助金月数
        disabilityMonths: {
            1: 27, 2: 25, 3: 23, 4: 21, 5: 18,
            6: 16, 7: 13, 8: 11, 9: 9, 10: 7
        },
        
        // 一次性工伤医疗补助金月数（5-10级）
        medicalSubsidyMonths: {
            5: 6, 6: 4, 7: 2, 8: 2, 9: 1, 10: 1
        },
        
        // 一次性伤残就业补助金月数（5-10级）
        employmentSubsidyMonths: {
            5: 12, 6: 10, 7: 8, 8: 6, 9: 4, 10: 2
        }
    },
    
    // 诉讼费收费标准
    litigationFeeStandards: {
        // 财产案件受理费标准
        propertyCase: [
            { min: 0, max: 10000, rate: 0.01, fixed: 50 },
            { min: 10000, max: 100000, rate: 0.025, fixed: 0 },
            { min: 100000, max: 200000, rate: 0.02, fixed: 0 },
            { min: 200000, max: 500000, rate: 0.015, fixed: 0 },
            { min: 500000, max: 1000000, rate: 0.01, fixed: 0 },
            { min: 1000000, max: 2000000, rate: 0.009, fixed: 0 },
            { min: 2000000, max: 5000000, rate: 0.008, fixed: 0 },
            { min: 5000000, max: 10000000, rate: 0.007, fixed: 0 },
            { min: 10000000, max: 20000000, rate: 0.006, fixed: 0 },
            { min: 20000000, max: Infinity, rate: 0.005, fixed: 0 }
        ],
        
        // 非财产案件受理费
        nonPropertyCase: {
            divorce: 300,
            otherMarriage: 300,
            intellectualProperty: 1000,
            laborDispute: 10,
            other: 100
        },
        
        // 诉讼保全费标准
        preservation: [
            { min: 0, max: 10000, rate: 0.01, fixed: 30 },
            { min: 10000, max: 100000, rate: 0.01, fixed: 0 },
            { min: 100000, max: Infinity, rate: 0.005, fixed: 0, maxFee: 5000 }
        ],
        
        // 执行申请费标准
        execution: [
            { min: 0, max: 10000, rate: 0.01, fixed: 50 },
            { min: 10000, max: 500000, rate: 0.015, fixed: 0 },
            { min: 500000, max: 5000000, rate: 0.01, fixed: 0 },
            { min: 5000000, max: 10000000, rate: 0.005, fixed: 0 },
            { min: 10000000, max: Infinity, rate: 0.001, fixed: 0, maxFee: 100000 }
        ]
    },
    
    // 律师收费标准
    lawyerFeeStandards: {
        // 代理费按标的额收费标准
        representation: [
            { min: 0, max: 100000, rate: 0.08, minFee: 3000 },
            { min: 100000, max: 1000000, rate: 0.06, minFee: 0 },
            { min: 1000000, max: 5000000, rate: 0.04, minFee: 0 },
            { min: 5000000, max: 10000000, rate: 0.02, minFee: 0 },
            { min: 10000000, max: Infinity, rate: 0.01, minFee: 0 }
        ],
        
        // 咨询费标准
        consultation: {
            hourlyRate: 200,
            minHours: 0.5
        },
        
        // 风险代理费率
        riskRepresentation: {
            maxRate: 0.30, // 最高30%
            minFee: 5000
        }
    },
    
    // 利率相关标准
    interestStandards: {
        // 民间借贷利率上限
        privateLending: {
            protectedRate: 13.8, // 受保护的利率上限（LPR的4倍，2024年约13.8%）
            supportedRate: 24,   // 支持的利率上限（已废止）
            maxRate: 36         // 绝对上限（已废止）
        },
        
        // 银行贷款基准利率
        bankLoanRates: {
            oneYear: 4.35,
            oneToFiveYear: 4.75,
            fiveYearPlus: 4.90
        }
    },
    
    // 工具函数
    utils: {
        /**
         * 获取省份列表
         */
        getProvinceList() {
            return Object.keys(this.compensationStandards).map(key => ({
                value: key,
                name: this.compensationStandards[key].name,
                type: this.compensationStandards[key].type
            }));
        },
        
        /**
         * 获取最新LPR利率
         */
        getLatestLPR() {
            const dates = Object.keys(this.lprRates).sort().reverse();
            return this.lprRates[dates[0]];
        },
        
        /**
         * 根据省份代码获取赔偿标准
         */
        getCompensationStandard(provinceCode) {
            return this.compensationStandards[provinceCode] || null;
        },
        
        /**
         * 格式化货币
         */
        formatCurrency(amount) {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        },
        
        /**
         * 计算日期差
         */
        calculateDays(startDate, endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            return Math.ceil((end - start) / (1000 * 60 * 60 * 24));
        }
    }
};

// 绑定工具函数到对象
Object.keys(JudicialStandards.utils).forEach(key => {
    JudicialStandards[key] = JudicialStandards.utils[key].bind(JudicialStandards);
});

// 导出到全局作用域
window.JudicialStandards = JudicialStandards;
