/**
 * 司法计算器主控制器
 * 负责界面交互、模块切换、数据管理等核心功能
 */

class JudicialCalculator {
    constructor() {
        this.currentModule = 'litigation';
        this.currentTab = {};
        this.init();
    }

    /**
     * 初始化计算器
     */
    init() {
        this.bindEvents();
        this.initModules();
        console.log('司法计算器初始化完成');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 主导航切换
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const module = e.currentTarget.dataset.module;
                this.switchModule(module);
            });
        });

        // 标签页切换
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('tab-btn')) {
                const tab = e.target.dataset.tab;
                const module = this.currentModule;
                this.switchTab(module, tab);
            }
        });

        // 案件类型变化事件
        const caseTypeSelect = document.getElementById('case-type');
        if (caseTypeSelect) {
            caseTypeSelect.addEventListener('change', this.handleCaseTypeChange.bind(this));
        }

        // 输入框实时验证
        document.querySelectorAll('.touch-input[type="number"]').forEach(input => {
            input.addEventListener('input', this.validateNumberInput.bind(this));
            input.addEventListener('blur', this.formatNumberInput.bind(this));
        });

        // 键盘事件
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));

        // 工伤类型变化事件
        const injuryTypeSelect = document.getElementById('injury-type');
        if (injuryTypeSelect) {
            injuryTypeSelect.addEventListener('change', this.handleInjuryTypeChange.bind(this));
        }

        // 复选框变化事件
        this.bindCheckboxEvents();
    }

    /**
     * 初始化各个模块
     */
    initModules() {
        // 设置默认标签页
        this.currentTab = {
            litigation: 'case-fee',
            compensation: 'traffic-compensation',
            financial: 'interest-calculator'
        };

        // 显示默认模块和标签页
        this.switchModule(this.currentModule);
    }

    /**
     * 切换主模块
     */
    switchModule(module) {
        // 更新导航按钮状态
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-module="${module}"]`).classList.add('active');

        // 隐藏所有模块
        document.querySelectorAll('.calculator-module').forEach(mod => {
            mod.classList.remove('active');
        });

        // 显示目标模块
        const targetModule = document.getElementById(`${module}-module`);
        if (targetModule) {
            targetModule.classList.add('active');
            this.currentModule = module;

            // 切换到该模块的默认标签页
            if (this.currentTab[module]) {
                this.switchTab(module, this.currentTab[module]);
            }


        }


    }

    /**
     * 切换标签页
     */
    switchTab(module, tab) {
        const moduleElement = document.getElementById(`${module}-module`);
        if (!moduleElement) return;

        // 更新标签按钮状态
        moduleElement.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        moduleElement.querySelector(`[data-tab="${tab}"]`)?.classList.add('active');

        // 隐藏所有标签内容
        moduleElement.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // 显示目标标签内容
        const targetTab = document.getElementById(tab);
        if (targetTab) {
            targetTab.classList.add('active');
            this.currentTab[module] = tab;
        }
    }

    /**
     * 处理案件类型变化
     */
    handleCaseTypeChange(e) {
        const caseType = e.target.value;
        const amountGroup = document.getElementById('amount-group');
        const nonPropertyGroup = document.getElementById('non-property-group');

        if (caseType === 'non-property') {
            amountGroup.style.display = 'none';
            nonPropertyGroup.style.display = 'block';
        } else {
            amountGroup.style.display = 'block';
            nonPropertyGroup.style.display = 'none';
        }
    }

    /**
     * 处理工伤类型变化
     */
    handleInjuryTypeChange(e) {
        const injuryType = e.target.value;
        const disabilityGroup = document.getElementById('injury-disability-group');

        if (injuryType === 'death') {
            disabilityGroup.style.display = 'none';
        } else {
            disabilityGroup.style.display = 'block';
        }
    }

    /**
     * 绑定复选框事件
     */
    bindCheckboxEvents() {
        // 残疾赔偿金复选框事件
        const disabilityCheckbox = document.getElementById('disability-compensation');
        const disabilitySelect = document.getElementById('disability-level');

        if (disabilityCheckbox && disabilitySelect) {
            disabilityCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    disabilitySelect.style.display = 'inline-block';
                } else {
                    disabilitySelect.style.display = 'none';
                    disabilitySelect.value = '0';
                }
            });
        }

        // 误工费复选框事件
        const lostWorkCheckbox = document.getElementById('lost-work-fee');
        const lostWorkInputs = document.querySelectorAll('#lost-work-days, #daily-income');

        if (lostWorkCheckbox) {
            lostWorkCheckbox.addEventListener('change', function() {
                lostWorkInputs.forEach(input => {
                    if (this.checked) {
                        input.style.display = 'inline-block';
                    } else {
                        input.style.display = 'none';
                        input.value = '';
                    }
                });
            });
        }

        // 护理费复选框事件
        const nursingCheckbox = document.getElementById('nursing-fee');
        const nursingInput = document.getElementById('nursing-days');

        if (nursingCheckbox && nursingInput) {
            nursingCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    nursingInput.style.display = 'inline-block';
                } else {
                    nursingInput.style.display = 'none';
                    nursingInput.value = '';
                }
            });
        }

        // 营养费复选框事件
        const nutritionCheckbox = document.getElementById('nutrition-fee');
        const nutritionInput = document.getElementById('nutrition-days');

        if (nutritionCheckbox && nutritionInput) {
            nutritionCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    nutritionInput.style.display = 'inline-block';
                } else {
                    nutritionInput.style.display = 'none';
                    nutritionInput.value = '';
                }
            });
        }

        // 医疗费复选框事件
        const medicalCheckbox = document.getElementById('medical-fee');
        const medicalInput = document.getElementById('medical-amount');

        if (medicalCheckbox && medicalInput) {
            medicalCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    medicalInput.style.display = 'inline-block';
                } else {
                    medicalInput.style.display = 'none';
                    medicalInput.value = '';
                }
            });
        }
    }

    /**
     * 验证数字输入
     */
    validateNumberInput(e) {
        const input = e.target;
        const value = input.value;

        // 移除非数字字符（除了小数点）
        const cleanValue = value.replace(/[^\d.]/g, '');
        
        // 确保只有一个小数点
        const parts = cleanValue.split('.');
        if (parts.length > 2) {
            input.value = parts[0] + '.' + parts.slice(1).join('');
        } else {
            input.value = cleanValue;
        }

        // 实时显示格式化的数字
        this.updateInputDisplay(input);
    }

    /**
     * 格式化数字输入
     */
    formatNumberInput(e) {
        const input = e.target;
        const value = parseFloat(input.value);
        
        if (!isNaN(value)) {
            input.value = value.toFixed(2);
        }
    }

    /**
     * 更新输入框显示
     */
    updateInputDisplay(input) {
        const value = parseFloat(input.value);
        if (!isNaN(value) && value > 0) {
            const formatted = this.formatCurrency(value);
            input.setAttribute('title', `格式化金额: ${formatted}`);
        }
    }

    /**
     * 格式化货币显示
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }

    /**
     * 格式化数字（添加千分位分隔符）
     */
    formatNumber(num) {
        return new Intl.NumberFormat('zh-CN').format(num);
    }

    /**
     * 显示计算结果
     */
    showResult(resultId, amount, details = []) {
        const resultSection = document.getElementById(resultId);
        const amountElement = document.getElementById(`${resultId.replace('-result', '')}-amount`);
        const detailsElement = document.getElementById(`${resultId.replace('-result', '')}-details`);

        if (resultSection && amountElement) {
            // 显示结果区域
            resultSection.style.display = 'block';
            
            // 设置金额
            amountElement.textContent = this.formatCurrency(amount);
            
            // 设置计算详情
            if (detailsElement && details.length > 0) {
                detailsElement.innerHTML = details.map(detail => 
                    `<div class="calculation-step">${detail}</div>`
                ).join('');
            }

            // 滚动到结果区域
            resultSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        // 创建错误提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <span>${message}</span>
        `;
        
        // 添加样式
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #dc3545;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1em;
            max-width: 300px;
        `;

        document.body.appendChild(errorDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 3000);
    }

    /**
     * 显示成功信息
     */
    showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        `;
        
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1em;
            max-width: 300px;
        `;

        document.body.appendChild(successDiv);

        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    }





    /**
     * 处理键盘快捷键
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + Enter 执行计算
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            const activeModule = document.querySelector('.calculator-module.active');
            const calculateBtn = activeModule?.querySelector('.calculate-btn');
            if (calculateBtn) {
                calculateBtn.click();
            }
        }
    }


}

// 全局函数，供HTML调用
let calculator;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    calculator = new JudicialCalculator();
});



// 清空结果函数
function clearResult(type) {
    if (!calculator) return;

    const resultSection = document.getElementById(`${type}-result`);
    if (resultSection) {
        resultSection.style.display = 'none';
    }

    // 清空相关输入框
    let selector = `#${type} input, #${type} select`;
    // 特殊处理金融计算模块的选择器
    if (type.includes('calculator')) {
        selector = `#${type} input, #${type} select`;
    }

    const inputs = document.querySelectorAll(selector);
    inputs.forEach(input => {
        if (input.type === 'checkbox') {
            input.checked = false;
            // 触发change事件以隐藏相关输入框
            input.dispatchEvent(new Event('change'));
        } else {
            input.value = '';
        }
    });

    calculator.showSuccess('已清空计算结果');
}



// 计算工伤赔偿
function calculateWorkInjuryCompensation() {
    try {
        const province = document.getElementById('injury-province').value;
        const monthlyWage = parseFloat(document.getElementById('monthly-wage').value);
        const injuryType = document.getElementById('injury-type').value;
        const disabilityLevel = parseInt(document.getElementById('injury-disability-level').value);

        if (isNaN(monthlyWage) || monthlyWage <= 0) {
            calculator.showError('请输入有效的月平均工资');
            return;
        }

        const params = {
            province,
            monthlyWage,
            disabilityLevel,
            isDeathCase: injuryType === 'death'
        };

        const result = compensationCalculator.calculateWorkInjuryCompensation(params);

        calculator.showResult('work-injury-result', result.totalCompensation, result.details);

    } catch (error) {
        calculator.showError(error.message);
    }
}

// 计算人身损害赔偿
function calculatePersonalInjuryCompensation() {
    try {
        const province = document.getElementById('personal-injury-province').value;
        const victimType = document.getElementById('personal-victim-type').value;
        const age = parseInt(document.getElementById('personal-victim-age').value);
        const medicalFee = parseFloat(document.getElementById('personal-medical-fee').value || 0);

        if (isNaN(age) || age < 0 || age > 120) {
            calculator.showError('请输入有效的年龄（0-120岁）');
            return;
        }

        const params = {
            province,
            victimType,
            age,
            medicalFee
        };

        const result = compensationCalculator.calculateTrafficCompensation(params);

        calculator.showResult('personal-injury-result', result.totalCompensation, result.details);

    } catch (error) {
        calculator.showError(error.message);
    }
}
