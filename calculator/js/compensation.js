/**
 * 赔偿计算模块
 * 计算交通事故、工伤、人身损害等各类赔偿
 */

class CompensationCalculator {
    constructor() {
        this.compensationStandards = {
            // 2024年各省市赔偿标准（基于最新统计数据）
            provinces: {
                'beijing': {
                    name: '北京市',
                    urbanDisposableIncome: 85415,  // 2024年城镇居民人均可支配收入
                    ruralDisposableIncome: 33195,  // 农村居民人均可支配收入
                    urbanConsumption: 52000,       // 城镇居民人均消费支出（估算）
                    ruralConsumption: 22000,       // 农村居民人均消费支出（估算）
                    averageWage: 194651            // 职工平均工资
                },
                'shanghai': {
                    name: '上海市',
                    urbanDisposableIncome: 88366,
                    ruralDisposableIncome: 40000,
                    urbanConsumption: 55000,
                    ruralConsumption: 25000,
                    averageWage: 171549
                },
                'tianjin': {
                    name: '天津市',
                    urbanDisposableIncome: 53581,
                    ruralDisposableIncome: 28090,
                    urbanConsumption: 35000,
                    ruralConsumption: 18000,
                    averageWage: 89268
                },
                'chongqing': {
                    name: '重庆市',
                    urbanDisposableIncome: 39713,
                    ruralDisposableIncome: 20000,
                    urbanConsumption: 26000,
                    ruralConsumption: 14000,
                    averageWage: 75000
                },
                'guangdong': {
                    name: '广东省',
                    urbanDisposableIncome: 51474,
                    ruralDisposableIncome: 22306,
                    urbanConsumption: 35130,
                    ruralConsumption: 17275,
                    averageWage: 95000
                },
                'jiangsu': {
                    name: '江苏省',
                    urbanDisposableIncome: 55415,
                    ruralDisposableIncome: 26791,
                    urbanConsumption: 37239,
                    ruralConsumption: 19145,
                    averageWage: 105000
                },
                'zhejiang': {
                    name: '浙江省',
                    urbanDisposableIncome: 67015,
                    ruralDisposableIncome: 37565,
                    urbanConsumption: 42507,
                    ruralConsumption: 24798,
                    averageWage: 115000
                },
                'shandong': {
                    name: '山东省',
                    urbanDisposableIncome: 42077,
                    ruralDisposableIncome: 22000,
                    urbanConsumption: 28000,
                    ruralConsumption: 15000,
                    averageWage: 85000
                },
                'fujian': {
                    name: '福建省',
                    urbanDisposableIncome: 47857,
                    ruralDisposableIncome: 24000,
                    urbanConsumption: 32000,
                    ruralConsumption: 16000,
                    averageWage: 88000
                },
                'liaoning': {
                    name: '辽宁省',
                    urbanDisposableIncome: 39844,
                    ruralDisposableIncome: 20000,
                    urbanConsumption: 26000,
                    ruralConsumption: 14000,
                    averageWage: 78000
                },
                'hunan': {
                    name: '湖南省',
                    urbanDisposableIncome: 37679,
                    ruralDisposableIncome: 18000,
                    urbanConsumption: 25000,
                    ruralConsumption: 13000,
                    averageWage: 72000
                },
                'hubei': {
                    name: '湖北省',
                    urbanDisposableIncome: 36947,
                    ruralDisposableIncome: 18500,
                    urbanConsumption: 24500,
                    ruralConsumption: 13500,
                    averageWage: 74000
                },
                'anhui': {
                    name: '安徽省',
                    urbanDisposableIncome: 36782,
                    ruralDisposableIncome: 18000,
                    urbanConsumption: 24000,
                    ruralConsumption: 13000,
                    averageWage: 70000
                },
                'jiangxi': {
                    name: '江西省',
                    urbanDisposableIncome: 36007,
                    ruralDisposableIncome: 17500,
                    urbanConsumption: 23500,
                    ruralConsumption: 12500,
                    averageWage: 68000
                },
                'hainan': {
                    name: '海南省',
                    urbanDisposableIncome: 34829,
                    ruralDisposableIncome: 17000,
                    urbanConsumption: 23000,
                    ruralConsumption: 12000,
                    averageWage: 66000
                },
                'hebei': {
                    name: '河北省',
                    urbanDisposableIncome: 34655,
                    ruralDisposableIncome: 17000,
                    urbanConsumption: 22500,
                    ruralConsumption: 12000,
                    averageWage: 65000
                },
                'sichuan': {
                    name: '四川省',
                    urbanDisposableIncome: 34325,
                    ruralDisposableIncome: 16500,
                    urbanConsumption: 22000,
                    ruralConsumption: 11500,
                    averageWage: 64000
                },
                'shaanxi': {
                    name: '陕西省',
                    urbanDisposableIncome: 33905,
                    ruralDisposableIncome: 16992,
                    urbanConsumption: 22000,
                    ruralConsumption: 15647,
                    averageWage: 102041
                },
                'ningxia': {
                    name: '宁夏回族自治区',
                    urbanDisposableIncome: 33355,
                    ruralDisposableIncome: 16000,
                    urbanConsumption: 21500,
                    ruralConsumption: 11000,
                    averageWage: 62000
                },
                'shanxi': {
                    name: '山西省',
                    urbanDisposableIncome: 32441,
                    ruralDisposableIncome: 15500,
                    urbanConsumption: 21000,
                    ruralConsumption: 10500,
                    averageWage: 60000
                },
                'henan': {
                    name: '河南省',
                    urbanDisposableIncome: 31552,
                    ruralDisposableIncome: 15000,
                    urbanConsumption: 20500,
                    ruralConsumption: 10000,
                    averageWage: 58000
                },
                'xizang': {
                    name: '西藏自治区',
                    urbanDisposableIncome: 31358,
                    ruralDisposableIncome: 15000,
                    urbanConsumption: 20000,
                    ruralConsumption: 10000,
                    averageWage: 55000
                },
                'jilin': {
                    name: '吉林省',
                    urbanDisposableIncome: 31318,
                    ruralDisposableIncome: 14500,
                    urbanConsumption: 20000,
                    ruralConsumption: 9500,
                    averageWage: 56000
                },
                'heilongjiang': {
                    name: '黑龙江省',
                    urbanDisposableIncome: 31296,
                    ruralDisposableIncome: 14500,
                    urbanConsumption: 20000,
                    ruralConsumption: 9500,
                    averageWage: 55000
                },
                'guangxi': {
                    name: '广西壮族自治区',
                    urbanDisposableIncome: 31125,
                    ruralDisposableIncome: 14000,
                    urbanConsumption: 19500,
                    ruralConsumption: 9000,
                    averageWage: 54000
                },
                'xinjiang': {
                    name: '新疆维吾尔自治区',
                    urbanDisposableIncome: 30899,
                    ruralDisposableIncome: 14000,
                    urbanConsumption: 19000,
                    ruralConsumption: 9000,
                    averageWage: 53000
                },
                'qinghai': {
                    name: '青海省',
                    urbanDisposableIncome: 30117,
                    ruralDisposableIncome: 13500,
                    urbanConsumption: 18500,
                    ruralConsumption: 8500,
                    averageWage: 52000
                },
                'yunnan': {
                    name: '云南省',
                    urbanDisposableIncome: 29932,
                    ruralDisposableIncome: 13000,
                    urbanConsumption: 18000,
                    ruralConsumption: 8000,
                    averageWage: 50000
                },
                'guizhou': {
                    name: '贵州省',
                    urbanDisposableIncome: 28561,
                    ruralDisposableIncome: 12500,
                    urbanConsumption: 17500,
                    ruralConsumption: 7500,
                    averageWage: 48000
                },
                'gansu': {
                    name: '甘肃省',
                    urbanDisposableIncome: 26612,
                    ruralDisposableIncome: 12000,
                    urbanConsumption: 16500,
                    ruralConsumption: 7000,
                    averageWage: 45000
                },
                'neimenggu': {
                    name: '内蒙古自治区',
                    urbanDisposableIncome: 40077,
                    ruralDisposableIncome: 19000,
                    urbanConsumption: 26000,
                    ruralConsumption: 13000,
                    averageWage: 75000
                }
            }
        };
    }

    /**
     * 计算交通事故赔偿
     */
    calculateTrafficCompensation(params) {
        const {
            province,
            victimType,
            age,
            medicalFee = 0,
            lostWorkDays = 0,
            dailyIncome = 0,
            nursingDays = 0,
            nutritionDays = 0,
            disabilityLevel = 0,
            isDeathCase = false
        } = params;

        const standards = this.compensationStandards.provinces[province];
        if (!standards) {
            throw new Error('未找到该省份的赔偿标准');
        }

        let totalCompensation = 0;
        let details = [];

        // 1. 医疗费
        if (medicalFee > 0) {
            totalCompensation += medicalFee;
            details.push(`医疗费：${this.formatCurrency(medicalFee)}`);
        }

        // 2. 误工费
        if (lostWorkDays > 0 && dailyIncome > 0) {
            const lostWorkFee = lostWorkDays * dailyIncome;
            totalCompensation += lostWorkFee;
            details.push(`误工费：${lostWorkDays}天 × ${this.formatCurrency(dailyIncome)}/天 = ${this.formatCurrency(lostWorkFee)}`);
        }

        // 3. 护理费
        if (nursingDays > 0) {
            // 护理费按当地护工平均工资计算，这里使用100元/天作为示例
            const nursingFeePerDay = 100;
            const nursingFee = nursingDays * nursingFeePerDay;
            totalCompensation += nursingFee;
            details.push(`护理费：${nursingDays}天 × ${this.formatCurrency(nursingFeePerDay)}/天 = ${this.formatCurrency(nursingFee)}`);
        }

        // 4. 营养费
        if (nutritionDays > 0) {
            // 营养费一般按20-40元/天计算，这里使用30元/天
            const nutritionFeePerDay = 30;
            const nutritionFee = nutritionDays * nutritionFeePerDay;
            totalCompensation += nutritionFee;
            details.push(`营养费：${nutritionDays}天 × ${this.formatCurrency(nutritionFeePerDay)}/天 = ${this.formatCurrency(nutritionFee)}`);
        }

        // 5. 残疾赔偿金或死亡赔偿金
        if (isDeathCase) {
            const deathCompensation = this.calculateDeathCompensation(standards, victimType, age);
            totalCompensation += deathCompensation.amount;
            details.push(`死亡赔偿金：${deathCompensation.description}`);
        } else if (disabilityLevel > 0) {
            const disabilityCompensation = this.calculateDisabilityCompensation(standards, victimType, age, disabilityLevel);
            totalCompensation += disabilityCompensation.amount;
            details.push(`残疾赔偿金：${disabilityCompensation.description}`);
        }

        return {
            totalCompensation: Math.round(totalCompensation * 100) / 100,
            details: details,
            province: standards.name
        };
    }

    /**
     * 计算死亡赔偿金
     */
    calculateDeathCompensation(standards, victimType, age) {
        const baseIncome = victimType === 'urban' ? standards.urbanDisposableIncome : standards.ruralDisposableIncome;
        
        // 死亡赔偿金按20年计算，60岁以上的，年龄每增加一岁减少一年；75岁以上的，按5年计算
        let years = 20;
        if (age >= 75) {
            years = 5;
        } else if (age >= 60) {
            years = 20 - (age - 60);
        }

        const amount = baseIncome * years;
        const description = `${this.formatCurrency(baseIncome)} × ${years}年 = ${this.formatCurrency(amount)}`;

        return { amount, description };
    }

    /**
     * 计算残疾赔偿金
     */
    calculateDisabilityCompensation(standards, victimType, age, disabilityLevel) {
        const baseIncome = victimType === 'urban' ? standards.urbanDisposableIncome : standards.ruralDisposableIncome;
        
        // 残疾赔偿金按20年计算
        let years = 20;
        if (age >= 75) {
            years = 5;
        } else if (age >= 60) {
            years = 20 - (age - 60);
        }

        // 根据伤残等级确定赔偿比例
        const disabilityRates = {
            1: 1.0,   // 一级伤残100%
            2: 0.9,   // 二级伤残90%
            3: 0.8,   // 三级伤残80%
            4: 0.7,   // 四级伤残70%
            5: 0.6,   // 五级伤残60%
            6: 0.5,   // 六级伤残50%
            7: 0.4,   // 七级伤残40%
            8: 0.3,   // 八级伤残30%
            9: 0.2,   // 九级伤残20%
            10: 0.1   // 十级伤残10%
        };

        const rate = disabilityRates[disabilityLevel] || 0;
        const amount = baseIncome * years * rate;
        const description = `${this.formatCurrency(baseIncome)} × ${years}年 × ${(rate * 100)}% = ${this.formatCurrency(amount)}`;

        return { amount, description };
    }

    /**
     * 计算工伤赔偿
     */
    calculateWorkInjuryCompensation(params) {
        const {
            province,
            disabilityLevel,
            monthlyWage,
            isDeathCase = false
        } = params;

        let totalCompensation = 0;
        let details = [];

        if (isDeathCase) {
            // 工亡赔偿
            const deathSubsidy = 1083760; // 2025年一次性工亡补助金标准（54188×20倍，基于2024年全国城镇居民人均可支配收入）
            totalCompensation += deathSubsidy;
            details.push(`一次性工亡补助金：${this.formatCurrency(deathSubsidy)}`);

            // 丧葬补助金根据各省市职工平均工资计算
            const standards = this.compensationStandards.provinces[province];
            const monthlyAverageWage = standards ? (standards.averageWage / 12) : (monthlyWage || 5000);
            const funeralSubsidy = monthlyAverageWage * 6; // 丧葬补助金
            totalCompensation += funeralSubsidy;
            details.push(`丧葬补助金：${this.formatCurrency(monthlyAverageWage)} × 6个月 = ${this.formatCurrency(funeralSubsidy)}`);

        } else if (disabilityLevel >= 1 && disabilityLevel <= 10) {
            // 伤残赔偿
            const disabilitySubsidy = this.calculateWorkDisabilitySubsidy(disabilityLevel, monthlyWage);
            totalCompensation += disabilitySubsidy.amount;
            details.push(`一次性伤残补助金：${disabilitySubsidy.description}`);

            // 如果是5-10级伤残，还有一次性工伤医疗补助金和伤残就业补助金
            if (disabilityLevel >= 5) {
                const medicalSubsidy = this.calculateWorkMedicalSubsidy(disabilityLevel, monthlyWage);
                const employmentSubsidy = this.calculateWorkEmploymentSubsidy(disabilityLevel, monthlyWage);
                
                totalCompensation += medicalSubsidy.amount + employmentSubsidy.amount;
                details.push(`一次性工伤医疗补助金：${medicalSubsidy.description}`);
                details.push(`一次性伤残就业补助金：${employmentSubsidy.description}`);
            }
        }

        return {
            totalCompensation: Math.round(totalCompensation * 100) / 100,
            details: details
        };
    }

    /**
     * 计算一次性伤残补助金
     */
    calculateWorkDisabilitySubsidy(disabilityLevel, monthlyWage) {
        const months = {
            1: 27, 2: 25, 3: 23, 4: 21, 5: 18,
            6: 16, 7: 13, 8: 11, 9: 9, 10: 7
        };

        const monthCount = months[disabilityLevel] || 0;
        const amount = monthlyWage * monthCount;
        const description = `${this.formatCurrency(monthlyWage)} × ${monthCount}个月 = ${this.formatCurrency(amount)}`;

        return { amount, description };
    }

    /**
     * 计算一次性工伤医疗补助金
     */
    calculateWorkMedicalSubsidy(disabilityLevel, monthlyWage) {
        const months = {
            5: 6, 6: 4, 7: 2, 8: 2, 9: 1, 10: 1
        };

        const monthCount = months[disabilityLevel] || 0;
        const amount = monthlyWage * monthCount;
        const description = `${this.formatCurrency(monthlyWage)} × ${monthCount}个月 = ${this.formatCurrency(amount)}`;

        return { amount, description };
    }

    /**
     * 计算一次性伤残就业补助金
     */
    calculateWorkEmploymentSubsidy(disabilityLevel, monthlyWage) {
        const months = {
            5: 12, 6: 10, 7: 8, 8: 6, 9: 4, 10: 2
        };

        const monthCount = months[disabilityLevel] || 0;
        const amount = monthlyWage * monthCount;
        const description = `${this.formatCurrency(monthlyWage)} × ${monthCount}个月 = ${this.formatCurrency(amount)}`;

        return { amount, description };
    }

    /**
     * 格式化货币显示
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }

    /**
     * 获取省份列表
     */
    getProvinceList() {
        return Object.keys(this.compensationStandards.provinces).map(key => ({
            value: key,
            name: this.compensationStandards.provinces[key].name
        }));
    }
}

// 创建赔偿计算器实例
const compensationCalculator = new CompensationCalculator();

/**
 * 计算交通事故赔偿
 */
function calculateTrafficCompensation() {
    try {
        const province = document.getElementById('accident-province').value;
        const victimType = document.getElementById('victim-type').value;
        const age = parseInt(document.getElementById('victim-age').value);

        if (isNaN(age) || age < 0 || age > 120) {
            calculator.showError('请输入有效的年龄（0-120岁）');
            return;
        }

        // 收集赔偿项目数据
        let medicalFee = 0;
        let lostWorkDays = 0;
        let dailyIncome = 0;
        let nursingDays = 0;
        let nutritionDays = 0;
        let disabilityLevel = 0;
        let isDeathCase = false;

        // 检查医疗费
        if (document.getElementById('medical-fee').checked) {
            medicalFee = parseFloat(document.getElementById('medical-amount').value || 0);
        }

        // 检查误工费
        if (document.getElementById('lost-work-fee').checked) {
            lostWorkDays = parseInt(document.getElementById('lost-work-days').value || 0);
            dailyIncome = parseFloat(document.getElementById('daily-income').value || 0);
        }

        // 检查护理费
        if (document.getElementById('nursing-fee').checked) {
            nursingDays = parseInt(document.getElementById('nursing-days').value || 0);
        }

        // 检查营养费
        if (document.getElementById('nutrition-fee').checked) {
            nutritionDays = parseInt(document.getElementById('nutrition-days').value || 0);
        }

        // 检查残疾赔偿金
        if (document.getElementById('disability-compensation').checked) {
            disabilityLevel = parseInt(document.getElementById('disability-level').value || 0);
        }

        // 检查死亡赔偿金
        if (document.getElementById('death-compensation').checked) {
            isDeathCase = true;
        }

        const params = {
            province,
            victimType,
            age,
            medicalFee,
            lostWorkDays,
            dailyIncome,
            nursingDays,
            nutritionDays,
            disabilityLevel,
            isDeathCase
        };

        const result = compensationCalculator.calculateTrafficCompensation(params);

        calculator.showResult('traffic-compensation-result', result.totalCompensation, result.details);

    } catch (error) {
        calculator.showError(error.message);
    }
}
