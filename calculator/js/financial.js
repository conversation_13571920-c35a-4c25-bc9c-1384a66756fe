/**
 * 金融计算模块
 * 计算利息、违约金、律师费等金融相关费用
 */

class FinancialCalculator {
    constructor() {
        this.lprRates = {
            // LPR利率历史数据（示例）
            '2023-12': { oneYear: 3.45, fiveYear: 4.20 },
            '2023-11': { oneYear: 3.45, fiveYear: 4.20 },
            '2023-10': { oneYear: 3.45, fiveYear: 4.20 },
            '2023-09': { oneYear: 3.45, fiveYear: 4.20 },
            '2023-08': { oneYear: 3.45, fiveYear: 4.20 }
        };

        this.lawyerFeeStandards = {
            // 律师收费标准（按标的额）
            consultation: 200, // 咨询费每小时
            representation: [
                { min: 0, max: 100000, rate: 0.08, min_fee: 3000 },
                { min: 100000, max: 1000000, rate: 0.06, min_fee: 0 },
                { min: 1000000, max: 5000000, rate: 0.04, min_fee: 0 },
                { min: 5000000, max: 10000000, rate: 0.02, min_fee: 0 },
                { min: 10000000, max: Infinity, rate: 0.01, min_fee: 0 }
            ]
        };
    }

    /**
     * 计算民间借贷利息
     */
    calculatePrivateLendingInterest(params) {
        const {
            principal,
            annualRate,
            startDate,
            endDate,
            compoundInterest = false,
            rateType = 'fixed' // fixed, lpr
        } = params;

        if (principal <= 0) {
            throw new Error('本金必须大于0');
        }

        if (annualRate <= 0) {
            throw new Error('年利率必须大于0');
        }

        if (annualRate > 36) {
            throw new Error('年利率不能超过36%');
        }

        const start = new Date(startDate);
        const end = new Date(endDate);
        const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24));

        if (days <= 0) {
            throw new Error('结束日期必须晚于开始日期');
        }

        let totalInterest = 0;
        let details = [];

        if (compoundInterest) {
            // 复利计算
            const dailyRate = annualRate / 100 / 365;
            const totalAmount = principal * Math.pow(1 + dailyRate, days);
            totalInterest = totalAmount - principal;
            
            details.push(`本金：${this.formatCurrency(principal)}`);
            details.push(`年利率：${annualRate}%`);
            details.push(`计息天数：${days}天`);
            details.push(`复利计算：${this.formatCurrency(principal)} × (1 + ${(dailyRate * 100).toFixed(6)}%)^${days}`);
            details.push(`本息合计：${this.formatCurrency(totalAmount)}`);
            details.push(`利息总额：${this.formatCurrency(totalInterest)}`);
        } else {
            // 单利计算
            totalInterest = principal * (annualRate / 100) * (days / 365);
            
            details.push(`本金：${this.formatCurrency(principal)}`);
            details.push(`年利率：${annualRate}%`);
            details.push(`计息天数：${days}天`);
            details.push(`单利计算：${this.formatCurrency(principal)} × ${annualRate}% × ${days}/365`);
            details.push(`利息总额：${this.formatCurrency(totalInterest)}`);
        }

        // 检查利率是否超过法定上限
        const currentLPR = 3.45; // 2024年LPR利率
        const legalLimit = currentLPR * 4; // LPR的4倍

        if (annualRate > legalLimit) {
            const legalInterest = principal * (legalLimit / 100) * (days / 365);
            details.push(`⚠️ 注意：年利率超过LPR的4倍（${legalLimit.toFixed(2)}%），超出部分不受法律保护`);
            details.push(`法定上限利率对应利息：${this.formatCurrency(legalInterest)}`);
        } else if (annualRate > 15.4) {
            details.push(`💡 提示：利率在合理范围内，受法律保护`);
        }

        return {
            totalInterest: Math.round(totalInterest * 100) / 100,
            totalAmount: Math.round((principal + totalInterest) * 100) / 100,
            details: details,
            days: days
        };
    }

    /**
     * 计算逾期利息
     */
    calculateOverdueInterest(params) {
        const {
            principal,
            contractRate,
            overdueRate,
            normalDays,
            overdueDays
        } = params;

        if (principal <= 0) {
            throw new Error('本金必须大于0');
        }

        let totalInterest = 0;
        let details = [];

        // 正常期间利息
        if (normalDays > 0) {
            const normalInterest = principal * (contractRate / 100) * (normalDays / 365);
            totalInterest += normalInterest;
            details.push(`正常期间利息：${this.formatCurrency(principal)} × ${contractRate}% × ${normalDays}/365 = ${this.formatCurrency(normalInterest)}`);
        }

        // 逾期利息
        if (overdueDays > 0) {
            const overdueInterest = principal * (overdueRate / 100) * (overdueDays / 365);
            totalInterest += overdueInterest;
            details.push(`逾期利息：${this.formatCurrency(principal)} × ${overdueRate}% × ${overdueDays}/365 = ${this.formatCurrency(overdueInterest)}`);
        }

        details.push(`利息总计：${this.formatCurrency(totalInterest)}`);

        return {
            totalInterest: Math.round(totalInterest * 100) / 100,
            details: details
        };
    }

    /**
     * 计算迟延履行利息
     */
    calculateDelayInterest(params) {
        const {
            debtAmount,
            startDate,
            endDate,
            baseRate,
            rateMultiplier = 2
        } = params;

        if (debtAmount <= 0) {
            throw new Error('债务金额必须大于0');
        }

        if (baseRate <= 0) {
            throw new Error('基准利率必须大于0');
        }

        const start = new Date(startDate);
        const end = new Date(endDate);
        const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24));

        if (days <= 0) {
            throw new Error('实际履行日期必须晚于迟延履行起算日期');
        }

        const actualRate = baseRate * rateMultiplier;
        const delayInterest = debtAmount * (actualRate / 100) * (days / 365);

        let details = [];
        details.push(`债务金额：${this.formatCurrency(debtAmount)}`);
        details.push(`迟延履行天数：${days}天`);
        details.push(`基准利率：${baseRate}%`);
        details.push(`利率倍数：${rateMultiplier}倍`);
        details.push(`实际利率：${actualRate}%`);
        details.push(`计算公式：${this.formatCurrency(debtAmount)} × ${actualRate}% × ${days}/365`);
        details.push(`迟延履行利息：${this.formatCurrency(delayInterest)}`);

        // 法律依据说明
        if (rateMultiplier === 2) {
            details.push(`📖 法律依据：《最高人民法院关于执行程序中计算迟延履行期间的债务利息适用法律若干问题的解释》`);
            details.push(`迟延履行期间的债务利息，按照银行同期贷款利率的二倍计算`);
        }

        return {
            delayInterest: Math.round(delayInterest * 100) / 100,
            totalAmount: Math.round((debtAmount + delayInterest) * 100) / 100,
            details: details,
            days: days
        };
    }

    /**
     * 计算违约金
     */
    calculatePenalty(params) {
        const {
            contractAmount,
            penaltyRate,
            penaltyType = 'rate', // rate, fixed
            fixedAmount = 0,
            actualLoss = 0
        } = params;

        let penalty = 0;
        let details = [];

        if (penaltyType === 'fixed') {
            penalty = fixedAmount;
            details.push(`固定违约金：${this.formatCurrency(penalty)}`);
        } else {
            penalty = contractAmount * (penaltyRate / 100);
            details.push(`违约金计算：${this.formatCurrency(contractAmount)} × ${penaltyRate}% = ${this.formatCurrency(penalty)}`);
        }

        // 违约金调整建议
        if (actualLoss > 0) {
            const ratio = penalty / actualLoss;
            if (ratio > 1.3) {
                const suggestedPenalty = actualLoss * 1.3;
                details.push(`⚠️ 违约金过高提示：违约金是实际损失的${ratio.toFixed(2)}倍`);
                details.push(`建议调整为：${this.formatCurrency(suggestedPenalty)}（实际损失的130%）`);
            } else if (ratio < 0.7) {
                const suggestedPenalty = actualLoss * 0.7;
                details.push(`💡 违约金偏低提示：违约金低于实际损失的70%`);
                details.push(`可以要求调整为：${this.formatCurrency(suggestedPenalty)}（实际损失的70%）`);
            }
        }

        return {
            penalty: Math.round(penalty * 100) / 100,
            details: details
        };
    }

    /**
     * 计算律师费
     */
    calculateLawyerFee(params) {
        const {
            caseAmount,
            feeType = 'representation', // representation, consultation
            consultationHours = 0,
            riskMultiplier = 1,
            complexityMultiplier = 1
        } = params;

        let baseFee = 0;
        let details = [];

        if (feeType === 'consultation') {
            baseFee = consultationHours * this.lawyerFeeStandards.consultation;
            details.push(`咨询费：${consultationHours}小时 × ${this.formatCurrency(this.lawyerFeeStandards.consultation)}/小时 = ${this.formatCurrency(baseFee)}`);
        } else {
            // 代理费按标的额计算
            let remainingAmount = caseAmount;
            
            for (let bracket of this.lawyerFeeStandards.representation) {
                if (remainingAmount <= 0) break;
                
                const bracketAmount = Math.min(remainingAmount, bracket.max - bracket.min);
                
                if (bracketAmount > 0) {
                    let bracketFee = bracketAmount * bracket.rate;
                    
                    // 检查最低收费
                    if (bracket.min_fee > 0 && bracketFee < bracket.min_fee) {
                        bracketFee = bracket.min_fee;
                    }
                    
                    baseFee += bracketFee;
                    details.push(`${this.formatCurrency(bracket.min + 1)} - ${this.formatCurrency(Math.min(bracket.min + bracketAmount, bracket.max))}：${(bracket.rate * 100)}% = ${this.formatCurrency(bracketFee)}`);
                    
                    remainingAmount -= bracketAmount;
                }
            }
        }

        // 应用风险系数和复杂程度系数
        let finalFee = baseFee * riskMultiplier * complexityMultiplier;
        
        if (riskMultiplier !== 1) {
            details.push(`风险系数调整：${this.formatCurrency(baseFee)} × ${riskMultiplier} = ${this.formatCurrency(baseFee * riskMultiplier)}`);
        }
        
        if (complexityMultiplier !== 1) {
            details.push(`复杂程度调整：× ${complexityMultiplier} = ${this.formatCurrency(finalFee)}`);
        }

        return {
            lawyerFee: Math.round(finalFee * 100) / 100,
            details: details
        };
    }

    /**
     * 获取当前LPR利率
     */
    getCurrentLPR() {
        const dates = Object.keys(this.lprRates).sort().reverse();
        return this.lprRates[dates[0]];
    }

    /**
     * 格式化货币显示
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }

    /**
     * 计算日期差
     */
    calculateDaysDifference(startDate, endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        return Math.ceil((end - start) / (1000 * 60 * 60 * 24));
    }
}

// 创建金融计算器实例
const financialCalculator = new FinancialCalculator();

/**
 * 计算利息
 */
function calculateInterest() {
    try {
        const principal = parseFloat(document.getElementById('interest-principal')?.value || 0);
        const annualRate = parseFloat(document.getElementById('interest-rate')?.value || 0);
        const startDate = document.getElementById('interest-start-date')?.value;
        const endDate = document.getElementById('interest-end-date')?.value;
        const compoundInterest = document.getElementById('compound-interest')?.checked || false;

        if (principal <= 0) {
            calculator.showError('请输入有效的本金金额');
            return;
        }

        if (annualRate <= 0) {
            calculator.showError('请输入有效的年利率');
            return;
        }

        if (!startDate || !endDate) {
            calculator.showError('请选择起始日期和结束日期');
            return;
        }

        const params = {
            principal,
            annualRate,
            startDate,
            endDate,
            compoundInterest
        };

        const result = financialCalculator.calculatePrivateLendingInterest(params);
        
        calculator.showResult('interest-result', result.totalInterest, result.details);
        
    } catch (error) {
        calculator.showError(error.message);
    }
}

/**
 * 计算违约金
 */
function calculatePenalty() {
    try {
        const contractAmount = parseFloat(document.getElementById('contract-amount')?.value || 0);
        const penaltyRate = parseFloat(document.getElementById('penalty-rate')?.value || 0);
        const actualLoss = parseFloat(document.getElementById('actual-loss')?.value || 0);

        if (contractAmount <= 0) {
            calculator.showError('请输入有效的合同金额');
            return;
        }

        if (penaltyRate <= 0) {
            calculator.showError('请输入有效的违约金比例');
            return;
        }

        const params = {
            contractAmount,
            penaltyRate,
            actualLoss
        };

        const result = financialCalculator.calculatePenalty(params);
        
        calculator.showResult('penalty-result', result.penalty, result.details);
        
    } catch (error) {
        calculator.showError(error.message);
    }
}

/**
 * 计算迟延履行利息
 */
function calculateDelayInterest() {
    try {
        const debtAmount = parseFloat(document.getElementById('debt-amount')?.value || 0);
        const startDate = document.getElementById('delay-start-date')?.value;
        const endDate = document.getElementById('delay-end-date')?.value;
        const baseRate = parseFloat(document.getElementById('base-rate')?.value || 0);
        const rateMultiplier = parseFloat(document.getElementById('rate-multiplier')?.value || 2);

        if (debtAmount <= 0) {
            calculator.showError('请输入有效的债务金额');
            return;
        }

        if (baseRate <= 0) {
            calculator.showError('请输入有效的基准利率');
            return;
        }

        if (!startDate || !endDate) {
            calculator.showError('请选择迟延履行起算日期和实际履行日期');
            return;
        }

        const params = {
            debtAmount,
            startDate,
            endDate,
            baseRate,
            rateMultiplier
        };

        const result = financialCalculator.calculateDelayInterest(params);

        calculator.showResult('delay-interest-result', result.delayInterest, result.details);

    } catch (error) {
        calculator.showError(error.message);
    }
}

/**
 * 计算律师费
 */
function calculateLawyerFee() {
    try {
        const caseAmount = parseFloat(document.getElementById('case-amount-lawyer')?.value || 0);
        const feeType = document.getElementById('lawyer-fee-type')?.value || 'representation';

        if (caseAmount <= 0) {
            calculator.showError('请输入有效的案件标的额');
            return;
        }

        const params = {
            caseAmount,
            feeType
        };

        const result = financialCalculator.calculateLawyerFee(params);

        calculator.showResult('lawyer-fee-result', result.lawyerFee, result.details);

    } catch (error) {
        calculator.showError(error.message);
    }
}
