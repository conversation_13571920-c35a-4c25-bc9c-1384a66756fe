/**
 * 诉讼费计算模块
 * 根据最新的诉讼费收费标准计算各类案件费用
 */

class LitigationFeesCalculator {
    constructor() {
        this.feeStandards = {
            // 财产案件受理费标准（2007年4月1日起施行）
            propertyCase: [
                { min: 0, max: 10000, rate: 0.01, fixed: 50 },
                { min: 10000, max: 100000, rate: 0.025, fixed: 0 },
                { min: 100000, max: 200000, rate: 0.02, fixed: 0 },
                { min: 200000, max: 500000, rate: 0.015, fixed: 0 },
                { min: 500000, max: 1000000, rate: 0.01, fixed: 0 },
                { min: 1000000, max: 2000000, rate: 0.009, fixed: 0 },
                { min: 2000000, max: 5000000, rate: 0.008, fixed: 0 },
                { min: 5000000, max: 10000000, rate: 0.007, fixed: 0 },
                { min: 10000000, max: 20000000, rate: 0.006, fixed: 0 },
                { min: 20000000, max: Infinity, rate: 0.005, fixed: 0 }
            ],
            
            // 非财产案件受理费标准
            nonPropertyCase: {
                divorce: 300,                    // 离婚案件
                otherMarriage: 300,             // 其他婚姻家庭案件
                intellectualProperty: 1000,      // 知识产权案件
                laborDispute: 10,               // 劳动争议案件
                other: 100                      // 其他非财产案件
            },
            
            // 诉讼保全费标准
            preservation: [
                { min: 0, max: 10000, rate: 0.01, fixed: 30 },
                { min: 10000, max: 100000, rate: 0.01, fixed: 0 },
                { min: 100000, max: Infinity, rate: 0.005, fixed: 0, max_fee: 5000 }
            ],
            
            // 执行申请费标准
            execution: [
                { min: 0, max: 10000, rate: 0.01, fixed: 50 },
                { min: 10000, max: 500000, rate: 0.015, fixed: 0 },
                { min: 500000, max: 5000000, rate: 0.01, fixed: 0 },
                { min: 5000000, max: 10000000, rate: 0.005, fixed: 0 },
                { min: 10000000, max: Infinity, rate: 0.001, fixed: 0, max_fee: 100000 }
            ]
        };
    }

    /**
     * 计算财产案件受理费
     */
    calculatePropertyCaseFee(amount) {
        if (amount <= 0) {
            throw new Error('标的额必须大于0');
        }

        let totalFee = 0;
        let details = [];
        let remainingAmount = amount;

        for (let i = 0; i < this.feeStandards.propertyCase.length; i++) {
            const bracket = this.feeStandards.propertyCase[i];
            
            if (remainingAmount <= 0) break;
            
            const bracketMin = bracket.min;
            const bracketMax = bracket.max;
            const bracketAmount = Math.min(remainingAmount, bracketMax - bracketMin);
            
            if (bracketAmount > 0) {
                let bracketFee;
                
                if (bracket.fixed > 0 && bracketAmount <= (bracketMax - bracketMin)) {
                    // 固定费用
                    bracketFee = bracket.fixed;
                    details.push(`${this.formatCurrency(bracketMin + 1)} - ${this.formatCurrency(bracketMax)}：固定收费 ${this.formatCurrency(bracketFee)}`);
                } else {
                    // 按比例收费
                    bracketFee = bracketAmount * bracket.rate;
                    details.push(`${this.formatCurrency(bracketMin + 1)} - ${this.formatCurrency(Math.min(bracketMin + bracketAmount, bracketMax))}：${(bracket.rate * 100).toFixed(1)}% = ${this.formatCurrency(bracketFee)}`);
                }
                
                totalFee += bracketFee;
                remainingAmount -= bracketAmount;
            }
        }

        return {
            fee: Math.round(totalFee * 100) / 100,
            details: details
        };
    }

    /**
     * 计算非财产案件受理费
     */
    calculateNonPropertyCaseFee(caseType) {
        const fees = this.feeStandards.nonPropertyCase;
        let fee = 0;
        let description = '';

        switch (caseType) {
            case 'divorce':
                fee = fees.divorce;
                description = '离婚案件固定收费';
                break;
            case 'other-marriage':
                fee = fees.otherMarriage;
                description = '其他婚姻家庭案件固定收费';
                break;
            case 'intellectual-property':
                fee = fees.intellectualProperty;
                description = '知识产权案件固定收费';
                break;
            case 'labor-dispute':
                fee = fees.laborDispute;
                description = '劳动争议案件固定收费';
                break;
            default:
                fee = fees.other;
                description = '其他非财产案件固定收费';
        }

        return {
            fee: fee,
            details: [description + '：' + this.formatCurrency(fee)]
        };
    }

    /**
     * 计算诉讼保全费
     */
    calculatePreservationFee(amount) {
        if (amount <= 0) {
            throw new Error('保全标的额必须大于0');
        }

        let totalFee = 0;
        let details = [];
        let remainingAmount = amount;

        for (let bracket of this.feeStandards.preservation) {
            if (remainingAmount <= 0) break;
            
            const bracketAmount = Math.min(remainingAmount, bracket.max - bracket.min);
            
            if (bracketAmount > 0) {
                let bracketFee;
                
                if (bracket.fixed > 0 && remainingAmount <= (bracket.max - bracket.min)) {
                    bracketFee = bracket.fixed;
                    details.push(`${this.formatCurrency(bracket.min + 1)} - ${this.formatCurrency(bracket.max)}：固定收费 ${this.formatCurrency(bracketFee)}`);
                } else {
                    bracketFee = bracketAmount * bracket.rate;
                    details.push(`${this.formatCurrency(bracket.min + 1)} - ${this.formatCurrency(Math.min(bracket.min + bracketAmount, bracket.max))}：${(bracket.rate * 100).toFixed(1)}% = ${this.formatCurrency(bracketFee)}`);
                }
                
                totalFee += bracketFee;
                remainingAmount -= bracketAmount;
                
                // 检查是否有最高限额
                if (bracket.max_fee && totalFee > bracket.max_fee) {
                    totalFee = bracket.max_fee;
                    details.push(`最高限额：${this.formatCurrency(bracket.max_fee)}`);
                    break;
                }
            }
        }

        return {
            fee: Math.round(totalFee * 100) / 100,
            details: details
        };
    }

    /**
     * 计算执行申请费
     */
    calculateExecutionFee(amount) {
        if (amount <= 0) {
            throw new Error('执行标的额必须大于0');
        }

        let totalFee = 0;
        let details = [];
        let remainingAmount = amount;

        for (let bracket of this.feeStandards.execution) {
            if (remainingAmount <= 0) break;
            
            const bracketAmount = Math.min(remainingAmount, bracket.max - bracket.min);
            
            if (bracketAmount > 0) {
                let bracketFee;
                
                if (bracket.fixed > 0 && remainingAmount <= (bracket.max - bracket.min)) {
                    bracketFee = bracket.fixed;
                    details.push(`${this.formatCurrency(bracket.min + 1)} - ${this.formatCurrency(bracket.max)}：固定收费 ${this.formatCurrency(bracketFee)}`);
                } else {
                    bracketFee = bracketAmount * bracket.rate;
                    details.push(`${this.formatCurrency(bracket.min + 1)} - ${this.formatCurrency(Math.min(bracket.min + bracketAmount, bracket.max))}：${(bracket.rate * 100).toFixed(1)}% = ${this.formatCurrency(bracketFee)}`);
                }
                
                totalFee += bracketFee;
                remainingAmount -= bracketAmount;
                
                // 检查是否有最高限额
                if (bracket.max_fee && totalFee > bracket.max_fee) {
                    totalFee = bracket.max_fee;
                    details.push(`最高限额：${this.formatCurrency(bracket.max_fee)}`);
                    break;
                }
            }
        }

        return {
            fee: Math.round(totalFee * 100) / 100,
            details: details
        };
    }

    /**
     * 格式化货币显示
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }
}

// 创建诉讼费计算器实例
const litigationCalculator = new LitigationFeesCalculator();

/**
 * 计算案件受理费
 */
function calculateCaseFee() {
    try {
        const caseType = document.getElementById('case-type').value;
        
        if (caseType === 'non-property') {
            // 非财产案件
            const nonPropertyType = document.getElementById('non-property-type').value;
            const result = litigationCalculator.calculateNonPropertyCaseFee(nonPropertyType);
            
            calculator.showResult('case-fee-result', result.fee, result.details);
            
        } else {
            // 财产案件
            const amount = parseFloat(document.getElementById('case-amount').value);
            
            if (isNaN(amount) || amount <= 0) {
                calculator.showError('请输入有效的标的额');
                return;
            }
            
            const result = litigationCalculator.calculatePropertyCaseFee(amount);
            
            calculator.showResult('case-fee-result', result.fee, result.details);
        }
        
    } catch (error) {
        calculator.showError(error.message);
    }
}

/**
 * 计算诉讼保全费
 */
function calculatePreservationFee() {
    try {
        const amount = parseFloat(document.getElementById('preservation-amount').value);
        const preservationType = document.getElementById('preservation-type').value;
        
        if (isNaN(amount) || amount <= 0) {
            calculator.showError('请输入有效的保全标的额');
            return;
        }
        
        const result = litigationCalculator.calculatePreservationFee(amount);
        
        calculator.showResult('preservation-fee-result', result.fee, result.details);
        
    } catch (error) {
        calculator.showError(error.message);
    }
}

/**
 * 计算执行申请费
 */
function calculateExecutionFee() {
    try {
        const amount = parseFloat(document.getElementById('execution-amount').value);
        
        if (isNaN(amount) || amount <= 0) {
            calculator.showError('请输入有效的执行标的额');
            return;
        }
        
        const result = litigationCalculator.calculateExecutionFee(amount);
        
        calculator.showResult('execution-fee-result', result.fee, result.details);
        
    } catch (error) {
        calculator.showError(error.message);
    }
}
