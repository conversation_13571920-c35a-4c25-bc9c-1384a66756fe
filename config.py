import os
from dotenv import load_dotenv

# 加载.env文件中的环境变量
load_dotenv()

class Config:
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'
    PORT = int(os.environ.get('PORT') or 8080)  # 默认端口号，避免与macOS AirPlay冲突

    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO').upper()  # 默认为INFO级别
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'pdf', 'doc', 'docx'}
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 最大上传文件大小：16MB

    # OCR配置
    OCR_ENGINE = os.environ.get('OCR_ENGINE') or 'baidu'  # 默认使用百度OCR
    TESSERACT_CMD = os.environ.get('TESSERACT_CMD') or 'tesseract'  # Tesseract命令路径
    OCR_LANGUAGE = os.environ.get('OCR_LANGUAGE') or 'chi_sim+eng'  # 默认使用简体中文+英文

    # PaddleOCR配置
    PADDLE_USE_GPU = os.environ.get('PADDLE_USE_GPU', 'false').lower() == 'true'
    PADDLE_LANG = os.environ.get('PADDLE_LANG') or 'ch'  # 默认使用中文
    PADDLE_USE_ANGLE_CLS = os.environ.get('PADDLE_USE_ANGLE_CLS', 'true').lower() == 'true'  # 默认使用方向分类器
    PADDLE_MODELS_BASE_DIR = os.environ.get('PADDLE_MODELS_BASE_DIR') or os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ocr', 'models')
    PADDLE_DET_MODEL_DIR = os.environ.get('PADDLE_DET_MODEL_DIR') or os.path.join(PADDLE_MODELS_BASE_DIR, 'ch_PP-OCRv4_det_server_infer')
    PADDLE_REC_MODEL_DIR = os.environ.get('PADDLE_REC_MODEL_DIR') or os.path.join(PADDLE_MODELS_BASE_DIR, 'ch_PP-OCRv4_rec_server_infer')
    PADDLE_CLS_MODEL_DIR = os.environ.get('PADDLE_CLS_MODEL_DIR') or os.path.join(PADDLE_MODELS_BASE_DIR, 'ch_ppocr_mobile_v2.0_cls_infer')

    # 百度云OCR配置
    BAIDU_OCR_APP_ID = os.environ.get('BAIDU_OCR_APP_ID')
    BAIDU_OCR_API_KEY = os.environ.get('BAIDU_OCR_API_KEY')
    BAIDU_OCR_SECRET_KEY = os.environ.get('BAIDU_OCR_SECRET_KEY')
    BAIDU_OCR_LANGUAGE_TYPE = os.environ.get('BAIDU_OCR_LANGUAGE_TYPE') or 'CHN_ENG'  # 默认中英文混合

    # 大模型API配置
    LLM_API_URL = os.environ.get('LLM_API_URL') or 'https://api.openai.com/v1/chat/completions'
    LLM_API_KEY = os.environ.get('LLM_API_KEY')
    LLM_MODEL = os.environ.get('LLM_MODEL') or 'gpt-3.5-turbo'

    # LLM请求参数配置
    LLM_TEMPERATURE = float(os.environ.get('LLM_TEMPERATURE') or 0.5)
    LLM_MAX_TOKENS = int(os.environ.get('LLM_MAX_TOKENS') or 16000)  # 增加到16000，避免输出被截断
    LLM_TOP_P = float(os.environ.get('LLM_TOP_P') or 0.8)

    # 阿里云思维链配置
    LLM_ENABLE_ALI_THINKING = os.environ.get('LLM_ENABLE_ALI_THINKING', 'false').lower() == 'true'  # 是否启用阿里云思维链功能，默认为false

    # 复选框专用LLM配置
    CHECKBOX_LLM_API_URL = os.environ.get('CHECKBOX_LLM_API_URL') or LLM_API_URL
    CHECKBOX_LLM_API_KEY = os.environ.get('CHECKBOX_LLM_API_KEY') or LLM_API_KEY
    CHECKBOX_LLM_MODEL = os.environ.get('CHECKBOX_LLM_MODEL') or LLM_MODEL
    CHECKBOX_LLM_TEMPERATURE = float(os.environ.get('CHECKBOX_LLM_TEMPERATURE') or LLM_TEMPERATURE)
    CHECKBOX_LLM_MAX_TOKENS = int(os.environ.get('CHECKBOX_LLM_MAX_TOKENS') or LLM_MAX_TOKENS)
    CHECKBOX_LLM_TOP_P = float(os.environ.get('CHECKBOX_LLM_TOP_P') or LLM_TOP_P)

    # 分类模型配置
    CLASSIFICATION_API_URL = os.environ.get('CLASSIFICATION_API_URL') or LLM_API_URL  # 分类LLM的API URL，默认与主LLM相同
    CLASSIFICATION_API_KEY = os.environ.get('CLASSIFICATION_API_KEY') or LLM_API_KEY  # 分类LLM的API密钥，默认与主LLM相同
    CLASSIFICATION_MODEL = os.environ.get('CLASSIFICATION_MODEL') or LLM_MODEL  # 默认使用与主模型相同的模型
    CLASSIFICATION_TEMPERATURE = float(os.environ.get('CLASSIFICATION_TEMPERATURE') or LLM_TEMPERATURE)
    CLASSIFICATION_MAX_TOKENS = int(os.environ.get('CLASSIFICATION_MAX_TOKENS') or 500)
    CLASSIFICATION_TOP_P = float(os.environ.get('CLASSIFICATION_TOP_P') or LLM_TOP_P)

    # 文档模板配置
    TEMPLATES_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'templates')
    DEFAULT_TEMPLATE_PATH = os.path.join(TEMPLATES_DIR, 'complaint_template.docx')

    # LLM响应处理配置
    INCLUDE_THINKING_TAG = os.environ.get('INCLUDE_THINKING_TAG', 'false').lower() == 'true'  # 是否保留thinking/think标签，默认为false

    # 复选框合并处理配置
    COMBINE_CHECKBOXES = os.environ.get('COMBINE_CHECKBOXES', 'false').lower() == 'true'  # 是否合并多个栏目的复选框到单个LLM请求中，默认为false
    COMBINE_CHECKBOXES_BY = os.environ.get('COMBINE_CHECKBOXES_BY', 'fulltext').lower()  # 合并维度：fulltext（全文）或table（表格），默认为fulltext

    # 复选框批量处理配置
    PROCESS_CHECKBOX_BATCH = os.environ.get('PROCESS_CHECKBOX_BATCH', 'false').lower() == 'true'  # 是否批量并发调用LLM API，默认为false（顺序调用）
    PROCESS_THREAD = int(os.environ.get('PROCESS_THREAD') or 10)  # 并发处理的最大线程数，默认为10

    # 二维码URL配置
    USE_EXTERNAL_URL = os.environ.get('USE_EXTERNAL_URL', 'False').lower() == 'true'
    EXTERNAL_URL = os.environ.get('EXTERNAL_URL', '')

    # LLM分类选项配置 - 只包含我们有模板的类型
    # 与 templates 目录中的 11 个模板一致的案件类型列表
    CASE_TYPES = [
        "民间借贷纠纷起诉状",
        "离婚纠纷起诉状",
        "买卖合同纠纷起诉状",
        "金融借款合同纠纷起诉状",
        "物业服务合同纠纷起诉状",
        "银行信用卡纠纷起诉状",
        "机动车交通事故责任纠纷起诉状",
        "劳动争议起诉状",
        "融资租赁合同纠纷起诉状",
        "保证保险合同纠纷起诉状",
        "证券虚假陈述责任纠纷起诉状"
    ]

    # 完整的案件类型列表（与 templates 目录中的模板一致）
    ALL_CASE_TYPES = [
        "民间借贷纠纷起诉状",
        "离婚纠纷起诉状",
        "买卖合同纠纷起诉状",
        "金融借款合同纠纷起诉状",
        "物业服务合同纠纷起诉状",
        "银行信用卡纠纷起诉状",
        "机动车交通事故责任纠纷起诉状",
        "劳动争议起诉状",
        "融资租赁合同纠纷起诉状",
        "保证保险合同纠纷起诉状",
        "证券虚假陈述责任纠纷起诉状"
    ]

    # 分批次的模板配置
    TEMPLATE_BATCHES = {
        "第一批": {
            "民事": {
                "婚姻家庭": [
                    {"name": "离婚纠纷起诉状", "file": "第一批/民事/2-离婚纠纷起诉状.docx"}
                ],
                "合同纠纷": [
                    {"name": "民间借贷纠纷起诉状", "file": "第一批/民事/1-民间借贷纠纷起诉状.docx"},
                    {"name": "买卖合同纠纷起诉状", "file": "第一批/民事/3-买卖合同纠纷起诉状.docx"},
                    {"name": "金融借款合同纠纷起诉状", "file": "第一批/民事/4-金融借款合同纠纷起诉状.docx"},
                    {"name": "物业服务合同纠纷起诉状", "file": "第一批/民事/5-物业服务合同纠纷起诉状.docx"},
                    {"name": "融资租赁合同纠纷起诉状", "file": "第一批/民事/9-融资租赁合同纠纷起诉状.docx"}
                ],
                "金融纠纷": [
                    {"name": "银行信用卡纠纷起诉状", "file": "第一批/民事/6-银行信用卡纠纷起诉状.docx"},
                    {"name": "保证保险合同纠纷起诉状", "file": "第一批/民事/10-保证保险合同纠纷起诉状.docx"},
                    {"name": "证券虚假陈述责任纠纷起诉状", "file": "第一批/民事/11-证券虚假陈述责任纠纷起诉状.docx"}
                ],
                "侵权纠纷": [
                    {"name": "机动车交通事故责任纠纷起诉状", "file": "第一批/民事/7-机动车交通事故责任纠纷起诉状.docx"}
                ],
                "劳动争议": [
                    {"name": "劳动争议起诉状", "file": "第一批/民事/8-劳动争议起诉状.docx"}
                ]
            }
        },
        "第二批": {
            "民事": {
                "合同纠纷": [
                    {"name": "房屋买卖合同纠纷民事起诉状", "file": "第二批/民事/16-房屋买卖合同纠纷民事起诉状.docx"},
                    {"name": "房屋租赁合同纠纷民事起诉状", "file": "第二批/民事/17-房屋租赁合同纠纷民事起诉状.docx"},
                    {"name": "建设工程施工合同纠纷民事起诉状", "file": "第二批/民事/22-建设工程施工合同纠纷民事起诉状.docx"},
                    {"name": "技术合同纠纷民事起诉状", "file": "第二批/民事/21-技术合同纠纷民事起诉状.docx"},
                    {"name": "船员劳务合同纠纷民事起诉状", "file": "第二批/民事/15-船员劳务合同纠纷民事起诉状.docx"}
                ],
                "知识产权": [
                    {"name": "侵害发明专利权纠纷民事起诉状", "file": "第二批/民事/24-侵害发明专利权纠纷民事起诉状.docx"},
                    {"name": "侵害商标权纠纷民事起诉状", "file": "第二批/民事/25-侵害商标权纠纷民事起诉状.docx"},
                    {"name": "侵害著作权及邻接权纠纷民事起诉状", "file": "第二批/民事/29-侵害著作权及邻接权纠纷民事起诉状.docx"},
                    {"name": "侵害外观设计专利权纠纷民事起诉状", "file": "第二批/民事/27-侵害外观设计专利权纠纷民事起诉状.docx"},
                    {"name": "侵害植物新品种权纠纷民事起诉状", "file": "第二批/民事/28-侵害植物新品种权纠纷民事起诉状.docx"},
                    {"name": "侵害商业秘密纠纷民事起诉状", "file": "第二批/民事/26-侵害商业秘密纠纷民事起诉状.docx"}
                ],
                "保险纠纷": [
                    {"name": "人身保险合同纠纷民事起诉状", "file": "第二批/民事/30-人身保险合同纠纷民事起诉状.docx"},
                    {"name": "财产损失保险合同纠纷民事起诉状", "file": "第二批/民事/13-财产损失保险合同纠纷民事起诉状.docx"},
                    {"name": "责任保险合同纠纷民事起诉状", "file": "第二批/民事/34-责任保险合同纠纷民事起诉状.docx"}
                ],
                "侵权纠纷": [
                    {"name": "不正当竞争纠纷民事起诉状", "file": "第二批/民事/12-不正当竞争纠纷民事起诉状.docx"},
                    {"name": "垄断纠纷民事起诉状", "file": "第二批/民事/23-垄断纠纷民事起诉状.docx"},
                    {"name": "船舶碰撞损害责任纠纷民事起诉状", "file": "第二批/民事/14-船舶碰撞损害责任纠纷民事起诉状.docx"}
                ],
                "环境保护": [
                    {"name": "环境污染民事公益诉讼民事起诉状", "file": "第二批/民事/20-环境污染民事公益诉讼民事起诉状.docx"},
                    {"name": "生态环境损害赔偿诉讼民事起诉状", "file": "第二批/民事/31-生态环境损害赔偿诉讼民事起诉状.docx"},
                    {"name": "生态破坏民事公益诉讼民事起诉状", "file": "第二批/民事/32-生态破坏民事公益诉讼民事起诉状.docx"}
                ],
                "海事海商": [
                    {"name": "海上、通海水域人身损害责任纠纷民事起诉状", "file": "第二批/民事/19-海上、通海水域人身损害责任纠纷民事起诉状.docx"},
                    {"name": "海上、通海水域货运代理合同纠纷民事起诉状", "file": "第二批/民事/18-海上、通海水域货运代理合同纠纷民事起诉状.docx"}
                ]
            },
            "刑事": {
                "人身权利": [
                    {"name": "诽谤案刑事（附带民事）自诉状", "file": "第二批/刑事/35-诽谤案刑事（附带民事）自诉状.docx"},
                    {"name": "拒不执行判决、裁定案刑事（附带民事）自诉状", "file": "第二批/刑事/36-拒不执行判决、裁定案刑事（附带民事）自诉状.docx"},
                    {"name": "侮辱案刑事（附带民事）自诉状", "file": "第二批/刑事/37-侮辱案刑事（附带民事）自诉状.docx"},
                    {"name": "重婚案刑事（附带民事）自诉状", "file": "第二批/刑事/38-重婚案刑事（附带民事）自诉状.docx"}
                ]
            },
            "行政": {
                "知识产权行政": [
                    {"name": "商标撤销复审行政纠纷行政起诉状", "file": "第二批/行政/43-商标撤销复审行政纠纷行政起诉状.docx"},
                    {"name": "商标申请驳回复审纠纷行政起诉状", "file": "第二批/行政/44-商标申请驳回复审纠纷行政起诉状.docx"},
                    {"name": "商标无效行政纠纷行政起诉状", "file": "第二批/行政/45-商标无效行政纠纷行政起诉状.docx"},
                    {"name": "专利申请驳回复审行政纠纷行政起诉状", "file": "第二批/行政/54-专利申请驳回复审行政纠纷行政起诉状.docx"},
                    {"name": "专利无效行政纠纷行政起诉状", "file": "第二批/行政/55-专利无效行政纠纷行政起诉状.docx"}
                ],
                "行政行为": [
                    {"name": "不履行法定职责行政起诉状", "file": "第二批/行政/39-不履行法定职责行政起诉状.docx"},
                    {"name": "行政补偿行政起诉状", "file": "第二批/行政/46-行政补偿行政起诉 .docx"},
                    {"name": "行政处罚行政起诉状", "file": "第二批/行政/47-行政处罚行政起诉状 .docx"},
                    {"name": "行政复议行政起诉状", "file": "第二批/行政/48-行政复议行政起诉状.docx"},
                    {"name": "行政赔偿行政起诉状", "file": "第二批/行政/49-行政赔偿行政起诉状 .docx"},
                    {"name": "行政强制执行行政起诉状", "file": "第二批/行政/50-行政强制执行行政起诉状 .docx"},
                    {"name": "行政协议行政起诉状", "file": "第二批/行政/51-行政协议行政起诉状 .docx"},
                    {"name": "行政许可行政起诉状", "file": "第二批/行政/52-行政许可行政起诉状 .docx"},
                    {"name": "政府信息公开行政起诉状", "file": "第二批/行政/53-政府信息公开行政起诉状.docx"}
                ],
                "征收拆迁": [
                    {"name": "国有土地上房屋征收决定行政起诉状", "file": "第二批/行政/41-国有土地上房屋征收决定行政起诉状.docx"}
                ],
                "社会保障": [
                    {"name": "工伤保险资格或者待遇认定行政起诉状", "file": "第二批/行政/40-工伤保险资格或者待遇认定行政起诉状.docx"}
                ],
                "竞争监管": [
                    {"name": "垄断纠纷行政起诉状", "file": "第二批/行政/42-垄断纠纷行政起诉状.docx"}
                ]
            }
        }
    }

    # 生成扁平的模板映射（向后兼容）
    TEMPLATE_MAPPING = {}
    for batch_name, batch_data in TEMPLATE_BATCHES.items():
        for category_name, category_data in batch_data.items():
            for subcategory_name, templates in category_data.items():
                for template in templates:
                    TEMPLATE_MAPPING[template["name"]] = template["file"]

    # 添加默认模板（通用模板保持在根目录）
    TEMPLATE_MAPPING["其他"] = "complaint_template.docx"

    @staticmethod
    def init_app(app):
        # 确保上传目录存在
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
