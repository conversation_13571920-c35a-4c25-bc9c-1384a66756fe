#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建起诉状Word模板文件
"""

import os
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

def create_template():
    """创建起诉状Word模板"""
    # 创建文档对象
    doc = Document()
    
    # 设置文档样式
    style = doc.styles['Normal']
    style.font.name = '宋体'
    style.font.size = Pt(12)
    
    # 添加标题
    title = doc.add_paragraph()
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_run = title.add_run('起诉状')
    title_run.font.size = Pt(16)
    title_run.font.bold = True
    
    # 添加占位符
    doc.add_paragraph('原告：')
    doc.add_paragraph('被告：')
    doc.add_paragraph('诉讼请求：')
    doc.add_paragraph('事实与理由：')
    doc.add_paragraph('证据：')
    
    # 添加日期
    date_para = doc.add_paragraph()
    date_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    date_para.add_run('日期：')
    
    # 确保目录存在
    template_dir = os.path.join('static', 'templates')
    os.makedirs(template_dir, exist_ok=True)
    
    # 保存文档
    template_path = os.path.join(template_dir, 'complaint_template.docx')
    doc.save(template_path)
    
    print(f"模板已创建: {template_path}")
    return template_path

if __name__ == '__main__':
    create_template()
