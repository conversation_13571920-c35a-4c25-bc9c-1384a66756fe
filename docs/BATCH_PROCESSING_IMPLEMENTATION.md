# 复选框批量处理功能实现

## 功能概述

本次实现了两个重要的复选框处理功能：

### 1. COMBINE 功能
- **功能描述**: 将row checkbox进行聚合处理
- **配置参数**: `COMBINE_CHECKBOXES` 和 `COMBINE_CHECKBOXES_BY`
- **聚合策略**:
  - `fulltext`: 按全文聚合
  - `table`: 按表格聚合

### 2. PROCESS_CHECKBOX_BATCH 功能
- **功能描述**: 控制LLM API调用方式
- **配置参数**: `PROCESS_CHECKBOX_BATCH`
- **调用模式**:
  - `true`: 一次性同时调用多个API（并发模式）
  - `false`: 顺序调用聚合或未聚合的结果（顺序模式）

## 技术实现

### 配置管理

#### config.py
```python
# 复选框批量处理配置
PROCESS_CHECKBOX_BATCH = os.environ.get('PROCESS_CHECKBOX_BATCH', 'false').lower() == 'true'
```

#### .env文件
```bash
# 复选框合并处理配置
COMBINE_CHECKBOXES=false
COMBINE_CHECKBOXES_BY=table

# 复选框批量处理配置
PROCESS_CHECKBOX_BATCH=true
```

### 核心实现

#### LLMCheckboxHandler 增强
1. **添加批量处理配置读取**
   ```python
   def _get_process_checkbox_batch_setting(self) -> bool:
       # 优先从环境变量获取
       env_setting = os.getenv('PROCESS_CHECKBOX_BATCH', '').lower()
       if env_setting in ('true', 'yes', '1', 'on'):
           return True
       elif env_setting in ('false', 'no', '0', 'off'):
           return False
       # 从配置中获取
       return self.config.get('process_checkbox_batch', False)
   ```

2. **并发API调用实现**
   ```python
   def _call_llm_api_concurrent(self, prompts: List[str], system_prompts: List[str] = None) -> List[str]:
       # 使用ThreadPoolExecutor进行并发调用
       max_workers = min(len(prompts), 5)  # 限制最大并发数为5
       with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
           # 提交所有任务并等待完成
   ```

3. **智能处理模式选择**
   ```python
   def process_combined_checkboxes(self, checkbox_sections: List[Dict]) -> List[bool]:
       # 根据配置决定处理方式
       if self.process_checkbox_batch:
           return self._process_sections_concurrent(checkbox_sections)
       else:
           return self._process_sections_sequential(checkbox_sections)
   ```

#### CheckboxProcessor 增强
1. **批量处理模式选择**
   ```python
   def _process_document_tables_improved(self, doc, table_rows_with_checkboxes):
       # 检查是否启用批量处理
       if self.llm_handler.process_checkbox_batch:
           self._process_tables_batch(doc, table_rows_with_checkboxes)
       else:
           self._process_tables_sequential(doc, table_rows_with_checkboxes)
   ```

2. **批量并发处理实现**
   ```python
   def _process_tables_batch(self, doc, table_rows_with_checkboxes):
       # 收集所有行信息，准备批量处理
       # 使用LLM批量处理所有行
       # 将结果分配到各行的复选框
   ```

### 错误处理与回退机制

1. **并发失败回退**
   ```python
   except Exception as e:
       print(f"批量处理失败: {str(e)}")
       # 回退到顺序处理
       self._process_tables_sequential(doc, table_rows_with_checkboxes)
   ```

2. **并发控制**
   - 最大并发数限制为5，避免过多并发请求
   - 使用线程池管理并发任务
   - 支持超时和异常处理

## 使用方式

### 启用批量并发处理
```bash
# 在.env文件中设置
PROCESS_CHECKBOX_BATCH=true
```

### 禁用批量处理（使用顺序模式）
```bash
# 在.env文件中设置
PROCESS_CHECKBOX_BATCH=false
```

## 性能优势

### 并发模式优势
1. **提高处理速度**: 多个API请求同时进行，减少总处理时间
2. **资源利用率**: 更好地利用网络和计算资源
3. **可扩展性**: 支持处理更多复选框栏目

### 顺序模式优势
1. **稳定性**: 减少并发带来的复杂性
2. **资源控制**: 避免过多并发请求对API服务造成压力
3. **调试友好**: 更容易追踪和调试问题

## 测试验证

实现了完整的测试脚本 `test_batch_processing.py`，验证了：
1. 批量并发模式的正确性
2. 顺序模式的兼容性
3. 配置切换的有效性
4. 错误处理的健壮性

## 配置建议

### 生产环境
- 建议启用批量处理 (`PROCESS_CHECKBOX_BATCH=true`) 以提高性能
- 根据API服务能力调整并发数限制

### 开发/调试环境
- 可以禁用批量处理 (`PROCESS_CHECKBOX_BATCH=false`) 便于调试
- 使用顺序模式更容易追踪问题

## 兼容性

- 完全向后兼容现有功能
- 默认配置保持原有行为（顺序处理）
- 可以通过配置灵活切换处理模式
