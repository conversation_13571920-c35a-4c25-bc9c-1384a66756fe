# LLM API调用调试日志

## 功能概述

为了便于调试和监控LLM API调用，系统在所有LLM API访问点添加了详细的调试日志，记录API URL和关键参数信息。

## 日志内容

每次调用LLM API时，系统会记录以下信息：

1. **API URL**: 完整的LLM API调用地址
2. **模型参数**: model, temperature, max_tokens, top_p等关键参数
3. **批量请求**: 对于批量调用，还会记录请求数量和进度

## 日志示例

### 单次API调用
```
INFO - 调用LLM API: http://112.132.240.73:3000/v1/chat/completions
INFO - LLM API参数: model=deepseek-671b, temperature=0.3, max_tokens=4000, top_p=0.9
```

### 批量API调用
```
INFO - 批量调用LLM API: http://112.132.240.73:3000/v1/chat/completions
INFO - LLM API参数: model=deepseek-671b, temperature=0.0, max_tokens=100
INFO - 批量请求数量: 5
DEBUG - 发送第 1/5 个请求
DEBUG - 发送第 2/5 个请求
...
```

## 应用范围

调试日志已集成到以下模块中：

### 1. llm/processor.py
- `call_api()` 方法: 通用LLM API调用
- `process_text()` 方法: 文本处理API调用

### 2. llm/party_processor.py  
- `extract_party_info()` 方法: 当事人信息提取API调用
- `call_api()` 方法: 通用API调用

### 3. document/checkbox/llm_handler.py
- `_call_llm_api()` 方法: 单次复选框处理API调用
- `_call_llm_api_batch()` 方法: 批量复选框处理API调用

## 日志级别

- **INFO**: API URL和主要参数信息
- **DEBUG**: 详细的调用进度和耗时信息

## 配置说明

调试日志使用Python标准logging模块，可以通过以下方式控制日志输出：

### 1. 在代码中设置日志级别
```python
import logging
logging.basicConfig(level=logging.INFO)  # 显示INFO及以上级别日志
logging.basicConfig(level=logging.DEBUG) # 显示所有调试信息
```

### 2. 环境变量控制（如果支持）
```bash
export LOG_LEVEL=INFO
export LOG_LEVEL=DEBUG
```

## 使用场景

1. **API连接调试**: 验证API URL是否正确
2. **参数调试**: 检查传递给LLM的参数是否符合预期
3. **性能监控**: 观察API调用频率和响应时间
4. **错误排查**: 当LLM调用失败时，快速定位问题
5. **批量处理监控**: 跟踪批量请求的进度

## 注意事项

1. **敏感信息**: 日志中不包含API密钥等敏感信息
2. **性能影响**: 日志记录对性能影响极小
3. **日志文件**: 建议配置日志轮转，避免日志文件过大
4. **生产环境**: 生产环境可以设置为INFO级别，减少日志量

## 示例输出

以下是一个完整的LLM API调用日志示例：

```
2024-01-XX 10:30:15,123 - llm.processor - INFO - 调用LLM API: http://112.132.240.73:3000/v1/chat/completions
2024-01-XX 10:30:15,124 - llm.processor - INFO - LLM API参数: model=deepseek-671b, temperature=0.3, max_tokens=4000, top_p=0.9
2024-01-XX 10:30:15,125 - llm.processor - INFO - Response status: 200
2024-01-XX 10:30:15,126 - llm.processor - DEBUG - API调用耗时: 1.23秒
```

这些日志信息可以帮助开发者和运维人员快速了解系统的LLM API调用情况，便于问题排查和性能优化。
