# 日志级别配置说明

## 功能概述

系统现在支持通过.env文件中的`LOG_LEVEL`环境变量来控制全局日志输出级别，便于调试和监控应用运行状态。

## 配置方法

### 在.env文件中设置

```bash
# 日志级别配置
# 可选值: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=DEBUG
```

### 支持的日志级别

1. **DEBUG**: 显示所有日志信息，包括详细的调试信息
   - 适用于开发和调试阶段
   - 会显示所有LLM API调用的详细参数和响应

2. **INFO**: 显示一般信息和重要操作日志
   - 适用于生产环境的正常监控
   - 会显示LLM API调用的基本信息

3. **WARNING**: 只显示警告和错误信息
   - 适用于生产环境，减少日志量
   - 只显示潜在问题和错误

4. **ERROR**: 只显示错误信息
   - 适用于生产环境的错误监控
   - 只显示严重错误

## 配置生效范围

日志级别配置会影响以下组件：

### 1. Flask应用主程序 (app.py)
- 全局日志配置
- Flask应用日志
- 请求处理日志

### 2. LLM处理器 (llm/processor.py)
- LLM API调用日志
- 文本处理日志
- 分类处理日志

### 3. 当事人信息处理器 (llm/party_processor.py)
- 当事人信息提取日志
- API调用参数日志
- 处理结果日志

### 4. 复选框处理器 (document/checkbox/llm_handler.py)
- 复选框处理日志
- 批量API调用日志

## 日志格式

系统使用统一的日志格式：

```
YYYY-MM-DD HH:MM:SS - 模块名 - 日志级别 - 日志消息
```

示例：
```
2024-01-XX 10:30:15 - llm.processor - INFO - 调用LLM API: http://112.132.240.73:3000/v1/chat/completions
2024-01-XX 10:30:15 - llm.processor - INFO - LLM API参数: model=deepseek-671b, temperature=0.5, max_tokens=16000, top_p=0.8
```

## 使用场景

### 开发调试
```bash
LOG_LEVEL=DEBUG
```
- 查看所有LLM API调用的详细信息
- 调试参数配置问题
- 跟踪处理流程

### 生产监控
```bash
LOG_LEVEL=INFO
```
- 监控系统正常运行状态
- 记录重要操作
- 平衡信息量和性能

### 错误排查
```bash
LOG_LEVEL=WARNING
```
- 专注于问题和错误
- 减少日志噪音
- 快速定位问题

## 配置更新

### 方法1: 修改.env文件
1. 编辑`.env`文件
2. 修改`LOG_LEVEL=DEBUG`为所需级别
3. 重启应用

### 方法2: 环境变量
```bash
export LOG_LEVEL=DEBUG
python run.py
```

## 技术实现

### config.py
```python
# 日志配置
LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO').upper()
```

### app.py
```python
# 配置全局日志级别
log_level = getattr(logging, Config.LOG_LEVEL, logging.INFO)
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
```

### party_processor.py
```python
# 从环境变量或Flask配置中获取日志级别
try:
    from flask import current_app
    log_level_str = current_app.config.get('LOG_LEVEL', 'INFO')
except:
    import os
    log_level_str = os.environ.get('LOG_LEVEL', 'INFO')

# 转换并设置日志级别
log_level = getattr(logging, log_level_str.upper(), logging.INFO)
self.logger.setLevel(log_level)
```

## 注意事项

1. **性能影响**: DEBUG级别会产生大量日志，可能影响性能
2. **磁盘空间**: 高级别日志会占用更多磁盘空间
3. **敏感信息**: 系统不会记录API密钥等敏感信息
4. **重启生效**: 修改LOG_LEVEL后需要重启应用才能生效

## 故障排除

### 问题: 看不到调试日志
**解决方案**:
1. 检查.env文件中的LOG_LEVEL设置
2. 确认应用已重启
3. 验证日志级别配置是否正确

### 问题: 日志过多
**解决方案**:
1. 将LOG_LEVEL从DEBUG改为INFO或WARNING
2. 配置日志轮转
3. 定期清理日志文件

### 问题: 配置不生效
**解决方案**:
1. 检查.env文件是否在正确位置
2. 确认环境变量名称拼写正确
3. 重启应用程序

现在您可以通过设置`LOG_LEVEL=DEBUG`来查看所有LLM API调用的详细日志信息！
