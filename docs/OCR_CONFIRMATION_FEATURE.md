# OCR文字确认功能

## 功能概述

在用户上传文档或图片进行OCR识别后，系统现在会显示一个确认页面，让用户检查和修改OCR识别的文字内容，确认无误后再继续进行LLM处理。

## 功能特点

1. **OCR结果预览** - 显示识别出的文字内容，包括字符统计
2. **内容编辑** - 用户可以直接修改识别错误的文字
3. **操作选择** - 用户可以选择确认继续、重置内容或返回首页
4. **响应式设计** - 支持桌面和移动设备
5. **实时反馈** - 显示处理状态和加载动画

## 处理流程

### 原有流程
```
文件上传 → OCR识别 → LLM处理 → 文档生成 → 完成
```

### 新流程
```
文件上传 → OCR识别 → 文字确认页面 → LLM处理 → 文档生成 → 完成
```

## 技术实现

### 后端实现

#### 1. 修改上传处理函数
- 将原有的 `process_multiple_files_async` 替换为 `process_files_with_confirmation`
- 新函数只执行OCR处理，然后等待用户确认

#### 2. 新增确认相关路由
```python
@app.route('/confirm-text/<task_id>')
def confirm_text(task_id):
    """显示OCR文字确认页面"""

@app.route('/confirm-text/<task_id>', methods=['POST'])
def process_confirmed_text(task_id):
    """处理用户确认的文字内容"""
```

#### 3. 新增处理函数
- `perform_ocr_processing()` - 执行OCR处理
- `continue_processing_after_confirmation()` - 用户确认后继续处理
- `process_confirmed_text_with_llm()` - 使用确认的文字进行LLM处理
- `generate_final_document()` - 生成最终文档

### 前端实现

#### 1. 状态处理更新
在 `main.js` 和 `touchscreen.js` 中添加对 `等待确认` 状态的处理：

```javascript
case '等待确认':
    progressBar.style.width = '60%';
    clearInterval(statusCheckInterval);
    
    if (data.confirmation_url) {
        setTimeout(() => {
            window.location.href = data.confirmation_url;
        }, 1000);
    }
    break;
```

#### 2. 确认页面模板
创建 `templates/confirm_text.html` 模板，包含：
- 文字内容预览区域
- 可编辑的文本框
- 操作按钮（确认、重置、返回）
- 响应式样式

## 使用说明

### 用户操作流程

1. **上传文件** - 在首页或触摸屏版本上传文档/图片
2. **等待OCR** - 系统自动进行OCR文字识别
3. **确认文字** - 系统跳转到确认页面，显示识别的文字内容
4. **检查修改** - 用户检查文字内容，如有错误可直接修改
5. **确认继续** - 点击"确认并继续"按钮进行下一步处理
6. **或者返回** - 点击"返回首页"重新开始

### 操作选项

- **确认并继续** - 使用当前文字内容继续LLM处理
- **重置内容** - 恢复为原始OCR识别结果
- **返回首页** - 取消当前处理，返回首页重新开始

## 配置说明

无需额外配置，该功能会自动在OCR处理完成后激活。

## 兼容性

- 支持普通首页和触摸屏首页
- 支持所有OCR引擎（百度OCR、Tesseract、PaddleOCR、OLM OCR）
- 支持单文件和多文件上传
- 支持所有文件格式（图片、PDF、Word文档）

## 技术细节

### 数据存储
确认数据临时存储在 `app.config` 中，格式为：
```python
app.config[f'CONFIRMATION_DATA_{task_id}'] = {
    'task_id': task_id,
    'ocr_text': combined_text,
    'files': files,
    'ocr_engine_type': ocr_engine_type,
    'download_dir': download_dir,
    'classification_mode': classification_mode,
    'template_type': template_type
}
```

### 状态管理
新增 `等待确认` 状态，前端会自动跳转到确认页面。

### 错误处理
- 如果确认数据不存在，自动跳转回首页
- 如果文字内容为空，显示错误提示
- 网络错误时显示重试提示

## 未来改进

1. **历史记录** - 保存用户的修改历史
2. **智能建议** - 基于常见错误提供修改建议
3. **批量确认** - 支持批量处理时的确认功能
4. **语音输入** - 支持语音修改文字内容
5. **OCR置信度** - 显示识别置信度，高亮可能错误的部分

## 故障排除

### 常见问题

1. **确认页面无法加载**
   - 检查任务ID是否有效
   - 确认OCR处理是否完成

2. **无法跳转到确认页面**
   - 检查浏览器控制台错误
   - 确认状态查询是否正常

3. **确认后无响应**
   - 检查网络连接
   - 确认LLM API配置是否正确

### 调试信息

在浏览器控制台中可以看到详细的状态变化和跳转信息：
```
状态变化: OCR中 -> 等待确认
检测到等待确认状态，准备跳转到确认页面
使用状态中的confirmation_url跳转: /confirm-text/abc123
```
