# 起诉状格式化软件设计文档

## 项目概述

本项目是一个基于大模型的起诉状格式化软件，能够接收多种格式的输入文件，通过OCR识别文本，利用大模型进行解析和格式化，最终生成符合标准格式的Word文档。

### 核心功能

1. **多格式文件处理**：支持图片、PDF、Word等格式的文件上传和处理
2. **中文OCR**：使用Tesseract OCR（默认）和OLM OCR（可选）进行中文文本识别
3. **大模型智能分类**：调用OpenAI兼容API进行模板分类
4. **大模型文本解析**：调用OpenAI兼容API进行文本解析和格式化
5. **智能复选框处理**：基于文本内容自动选择相关复选框选项
6. **Word文档生成**：根据模板生成标准格式的起诉状Word文档

## 安装指南

### 系统要求

- Python 3.11（推荐）
- Conda 虚拟环境
- Tesseract OCR 引擎
- Poppler（用于PDF处理）

### 安装步骤

#### 1. 克隆仓库

```bash
git clone https://github.com/yourusername/complaint-llm.git
cd complaint-llm
```

#### 2. 创建并激活Conda环境

```bash
conda create -y -n complaint-llm python=3.11
conda activate complaint-llm
```

#### 3. 安装依赖项

```bash
pip install -r requirements.txt
```

#### 4. 安装Tesseract OCR

**macOS**:
```bash
brew install tesseract
brew install tesseract-lang  # 安装语言包，包括中文
```

**Linux**:
```bash
apt-get install tesseract-ocr
apt-get install tesseract-ocr-chi-sim  # 安装中文语言包
```

**Windows**:
- 从[Tesseract官方GitHub](https://github.com/UB-Mannheim/tesseract/wiki)下载安装程序
- 安装时选择Additional Language Data中的Chinese (Simplified)
- 将Tesseract安装目录添加到系统环境变量PATH中

> **注意**：安装中文语言包是必要的，因为默认配置使用`chi_sim+eng`（简体中文+英文）作为OCR语言设置，这能显著提高中文文档的识别率。

#### 5. 安装Poppler（用于PDF处理）

**macOS**:
```bash
brew install poppler
```

**Linux**:
```bash
apt-get install poppler-utils
```

**Windows**:
- 从[Poppler for Windows](https://github.com/oschwartz10612/poppler-windows/releases/)下载二进制文件
- 解压并将bin目录添加到系统环境变量PATH中

#### 6. 安装Word文档处理依赖

##### 处理.docx文件
```bash
pip install python-docx
```

##### 处理.doc文件
当使用百度OCR引擎时，对于.doc格式文件，系统会尝试直接提取文本而不是通过OCR识别。

**Linux环境**：

```bash
# Debian/Ubuntu
sudo apt-get install antiword

# CentOS/RHEL
sudo yum install antiword
```

> **注意**：如果未安装antiword，系统将无法直接处理.doc格式的文件，会提示将其转换为.docx格式。

#### 7. 配置环境变量

```bash
cp .env.example .env
```

编辑`.env`文件，填写以下信息：
- `SECRET_KEY`: 随机字符串，用于Flask安全
- `PORT`: 应用端口号，默认为8080（避免与macOS AirPlay冲突）
- `OCR_LANGUAGE`: OCR语言设置，默认为`chi_sim+eng`（简体中文+英文）
- `LLM_API_KEY`: 您的OpenAI API密钥
- 根据需要调整其他配置

### 运行应用

```bash
python run.py
```

应用将在 http://127.0.0.1:8080 启动

### 测试功能

#### 测试OCR功能

使用测试脚本测试OCR功能：

```bash
# 使用默认的Tesseract OCR引擎
python test_ocr.py <图像文件路径>

# 指定使用的OCR引擎
python test_ocr.py <图像文件路径> tesseract
# 或
python test_ocr.py <图像文件路径> olm
```

该脚本将输出识别结果和识别时间。

#### 测试LLM功能

使用测试脚本测试大模型文本处理功能：

```bash
python test_llm.py <文本文件路径>
```

该脚本将调用大模型API对文本进行解析和格式化，并输出结果。

#### 测试Word模板生成

生成Word模板文件：

```bash
python create_template.py
```

该脚本将在`templates`目录下生成一个标准格式的起诉状Word模板文件。

## 技术架构

### 前端

- 简洁的HTML/CSS/JavaScript实现
- 文件上传组件
- 进度显示
- 结果预览和下载

### 后端

- **Web框架**：Flask
- **OCR处理**：PaddleOCR（主要）、Tesseract OCR和OLM OCR（可选）
- **大模型API**：支持OpenAI兼容API，适配主流大模型
- **文档生成**：python-docx
- **异步处理**：多线程与异步任务队列

## 技术特点与优势

### 多引擎OCR技术

- **PaddleOCR**：百度开源的高精度OCR引擎，对中文识别效果优异
- **自适应OCR处理**：根据文档特性自动选择最佳OCR引擎
- **文档格式智能处理**：区分处理图片、PDF和Word文档
- **原生DOCX处理**：直接处理Word文档无需OCR，保持原始格式和内容

### 智能模板分类与选择

- **自动模板匹配**：根据文档内容自动选择最佳模板
- **多模板支持**：内置11种法律文书模板，覆盖主要案件类型
- **关键词匹配系统**：基于专业法律术语库进行精准匹配

### 高级复选框处理

- **双引擎处理机制**：
  - **LLM智能判断**：利用大模型理解文本语义，智能决定复选框勾选状态
  - **规则引擎处理**：基于预定义规则快速处理标准复选框
- **可配置处理策略**：支持LLM和规则引擎的独立开关和组合使用
- **表格上下文感知**：识别复选框在表格中的位置和关系，处理互斥选项

### 大模型应用技术

- **提示工程优化**：精心设计的提示模板，提高大模型处理准确性
- **上下文管理**：智能管理和压缩文本上下文，优化API调用
- **多模型支持**：支持多种大模型API，包括通用和专业化法律模型
- **参数自适应**：根据不同任务自动调整大模型参数配置

### 系统优化

- **并行处理**：多线程OCR和文档处理，提高处理速度
- **内存优化**：分段处理大型文档，减少内存占用
- **可配置化**：通过环境变量实现灵活配置，无需代码修改
- **兼容性设计**：支持主流操作系统和不同版本的Python环境

## 项目结构

```
complaint-llm/
├── app.py                  # Flask应用主文件
├── config.py               # 配置文件
├── requirements.txt        # 依赖项
├── static/                 # 静态文件
│   ├── css/                # 样式文件
│   ├── js/                 # JavaScript文件
│   └── templates/          # Word文档模板
├── templates/              # HTML模板
│   ├── index.html          # 主页
│   └── result.html         # 结果页
├── ocr/                    # OCR处理模块
│   ├── __init__.py
│   ├── factory.py          # OCR工厂类
│   ├── olm_ocr.py          # OLM OCR实现
│   └── tesseract_ocr.py    # Tesseract OCR实现
├── llm/                    # 大模型处理模块
│   ├── __init__.py
│   └── processor.py        # 文本处理器
└── document/               # 文档生成模块
    ├── __init__.py
    └── generator.py        # Word文档生成器
```

## 模块设计

### OCR模块

OCR模块采用工厂模式设计，支持多种OCR引擎，默认使用OLM OCR。

#### OCR引擎接口

```python
class OCREngine:
    def process_image(self, image_path):
        """处理图像并返回识别的文本"""
        pass
        
    def process_pdf(self, pdf_path):
        """处理PDF并返回识别的文本"""
        pass
        
    def process_document(self, doc_path):
        """处理Word文档并返回识别的文本"""
        pass
```

#### OLM OCR实现

```python
class OlmOCR(OCREngine):
    def __init__(self, config=None):
        # 初始化OLM OCR模型
        pass
        
    def process_image(self, image_path):
        # 使用OLM OCR处理图像
        pass
        
    # 其他方法实现...
```

#### Tesseract OCR实现

```python
class TesseractOCR(OCREngine):
    def __init__(self, config=None):
        # 初始化Tesseract
        pass
        
    def process_image(self, image_path):
        # 使用Tesseract处理图像
        pass
        
    # 其他方法实现...
```

#### OCR工厂

```python
class OCRFactory:
    @staticmethod
    def get_ocr_engine(engine_type="olm", config=None):
        if engine_type.lower() == "olm":
            return OlmOCR(config)
        elif engine_type.lower() == "tesseract":
            return TesseractOCR(config)
        else:
            raise ValueError(f"Unsupported OCR engine: {engine_type}")
```

### 在应用中使用

```python
# 从配置中获取OCR引擎类型
ocr_engine_type = app.config.get("OCR_ENGINE", "olm")

# 创建OCR引擎
ocr_engine = OCRFactory.get_ocr_engine(ocr_engine_type)

# 处理上传的文件
text = ocr_engine.process_document(uploaded_file_path)
```

这样的设计使得OCR引擎可以灵活切换，同时保持了代码的简洁性和可维护性。

### 大模型处理模块

大模型处理模块负责解析和格式化OCR识别出的文本，并提取关键信息用于文档生成。

```python
class LLMProcessor:
    def __init__(self, api_key=None, api_base=None):
        # 初始化大模型API客户端
        pass
        
    def process_text(self, text):
        # 处理文本并返回格式化结果
        pass
        
    def _enhance_content_for_checkboxes(self, data):
        # 分析内容并添加复选框关键词
        pass
```

### 文档生成模块

文档生成模块负责根据模板和格式化数据生成最终的Word文档。

```python
class DocumentGenerator:
    def __init__(self, template_path=None):
        # 初始化文档生成器
        pass
        
    def generate(self, data, output_path=None):
        # 生成文档并保存
        pass
        
    def _handle_checkboxes(self, document, data):
        # 处理文档中的复选框
        pass
```

## 智能复选框处理

系统能够根据起诉状内容自动识别并选择相关的复选框选项，提高用户体验和效率。

### 工作流程

1. **内容分析**：`LLMProcessor`的`_enhance_content_for_checkboxes`方法分析起诉状的「事实与理由」和「诉讼请求」等字段
2. **关键词提取**：根据内容提取相关关键词，如「借贷纠纷」、「离婚纠纷」、「交通事故」等
3. **复选框匹配**：`DocumentGenerator`的`_handle_checkboxes`方法根据提取的关键词自动选择文档中的相应复选框
4. **文档生成**：生成的Word文档中已自动勾选相关选项，无需用户手动操作

### 提示词内容

1. 案件完整信息（整体上下文）
这部分信息有两种来源，优先使用OCR识别结果，如果没有则使用结构化数据：

使用OCR识别结果时（优先）：
经过清理处理的OCR原始文本（最多8000个字符）
清理过程包括：
移除连续的换行符，保留单个换行
移除连续的空格，保留单个空格
去除每行首尾的空白字符
使用结构化数据时（备选）：
案件类型
原告信息（以JSON格式展示）
被告信息（以JSON格式展示）
案件数据中的其他字段（每个字段最多显示1000个字符）
2. 当前段落文本
包含复选框的段落的完整文本内容
3. 表格信息（如果复选框在表格中）
第一列内容（作为判断依据）
完整行内容（经过清理，去除多余空格和符号）
4. 复选框详情
复选框文本：包括复选框前后的文本内容（经过清理和合并）
所在表格行的完整内容（如果在表格中）
5. 判断指南
提供给LLM的决策指南，包括：

基于整个案件内容作为上下文来判断
对于表格中的复选框，参考所在行的完整内容
只有确定应该勾选时才返回true，否则返回false
注意互斥选项不应同时勾选
如果信息不充分或含糊不清，不要勾选该复选框
对于无法确定的选项，选择不勾选
宁可错误地不勾选，也不能错误地勾选
这些信息共同构成了发送给LLM的完整上下文，帮助LLM做出是否勾选复选框的决策。系统会要求LLM以JSON数组格式返回结果，例如[true, false, true]，表示哪些复选框应该被勾选。

### 使用示例

```python
# 创建LLM处理器
llm_processor = LLMProcessor(api_key="your_api_key")

# 处理文本并获取格式化数据
formatted_data = llm_processor.process_text(ocr_text)

# 创建文档生成器
doc_generator = DocumentGenerator(template_path="templates/complaint_template.docx")

# 生成文档
doc_generator.generate(formatted_data, output_path="output/complaint.docx")
```

生成的文档将根据内容自动选择相应的复选框，如民间借贷纠纷会自动选择「借贷纠纷」和「民间借贷」选项。


