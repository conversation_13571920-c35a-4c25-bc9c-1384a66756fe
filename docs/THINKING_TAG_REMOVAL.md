# LLM响应中Thinking标签移除功能

## 功能概述

本功能用于自动移除LLM返回结果中的`<thinking>...</thinking>`和`<think>...</think>`标签及其内容，确保最终输出的内容更加简洁和专业。

## 配置说明

### 环境变量配置

在`.env`文件中添加以下配置：

```bash
# LLM响应处理配置
# 是否保留thinking标签 (true/false)，默认为false，即去除<thinking>和<think>标签
INCLUDE_THINKING_TAG=false
```

### 配置选项

- `INCLUDE_THINKING_TAG=false` (默认): 移除所有thinking和think标签及其内容
- `INCLUDE_THINKING_TAG=true`: 保留thinking和think标签及其内容

## 功能特性

1. **自动检测和移除**: 自动识别并移除`<thinking>...</thinking>`和`<think>...</think>`标签
2. **大小写不敏感**: 支持`<thinking>`, `<THINKING>`, `<Thinking>`, `<think>`, `<THINK>`, `<Think>`等各种大小写组合
3. **多行支持**: 支持跨多行的thinking/think标签内容
4. **多标签支持**: 可以处理一个响应中包含多个thinking/think标签的情况
5. **空白行清理**: 移除thinking标签后自动清理多余的空白行
6. **配置灵活**: 可通过环境变量控制是否启用此功能

## 应用范围

此功能已集成到以下模块中：

1. **llm/processor.py**: 主要的LLM处理器
   - `call_api()` 方法
   - `process_text()` 方法

2. **llm/party_processor.py**: 当事人信息处理器
   - `extract_party_info()` 方法
   - `call_api()` 方法

3. **document/checkbox/llm_handler.py**: 复选框处理器
   - `_call_llm_api()` 方法
   - `_call_llm_api_batch()` 方法

## 示例

### 示例1：包含thinking标签
**输入内容：**
```
<thinking>
这是一个思考过程，需要分析案件类型...
让我仔细看看这个案件的特征...
</thinking>

{
  "案件类型": "民间借贷纠纷起诉状",
  "原告姓名": "张三",
  "被告姓名": "李四"
}
```

**输出内容：**
```
{
  "案件类型": "民间借贷纠纷起诉状",
  "原告姓名": "张三",
  "被告姓名": "李四"
}
```

### 示例2：包含think标签
**输入内容：**
```
<think>
简短的思考过程...
</think>

这是正常的响应内容。
```

**输出内容：**
```
这是正常的响应内容。
```

### 示例3：混合标签
**输入内容：**
```
<thinking>
第一个思考过程...
</thinking>

正常内容

<think>
第二个思考过程...
</think>

更多内容
```

**输出内容：**
```
正常内容
更多内容
```

## 技术实现

使用正则表达式进行标签匹配和移除：

```python
def _remove_thinking_tags(self, content):
    """移除LLM响应中的thinking/think标签"""
    # 使用正则表达式移除thinking和think标签及其内容
    # 匹配 <thinking>...</thinking> 和 <think>...</think> 标签（支持多行）
    cleaned_content = re.sub(r'<think(?:ing)?>.*?</think(?:ing)?>', '', content, flags=re.DOTALL | re.IGNORECASE)

    # 清理可能留下的多余空白行
    cleaned_content = re.sub(r'\n\s*\n', '\n', cleaned_content)
    cleaned_content = cleaned_content.strip()

    return cleaned_content
```

### 正则表达式说明

- `<think(?:ing)?>`: 匹配`<think>`或`<thinking>`开始标签
- `.*?`: 非贪婪匹配任意内容（包括换行符）
- `</think(?:ing)?>`: 匹配`</think>`或`</thinking>`结束标签
- `re.DOTALL`: 使`.`匹配包括换行符在内的任意字符
- `re.IGNORECASE`: 大小写不敏感匹配

## 注意事项

1. 此功能默认启用（移除thinking/think标签）
2. 如需保留thinking/think标签用于调试，请设置`INCLUDE_THINKING_TAG=true`
3. 功能对所有LLM API调用生效，包括文本处理、当事人信息提取、复选框处理等
4. 移除thinking/think标签不会影响LLM的实际推理能力，只是清理输出格式
5. 支持同时处理`<thinking>`和`<think>`两种标签格式

## 更新日志

- **2024-01-XX**: 初始版本，支持基本的thinking标签移除功能
- **2024-01-XX**: 增强版本，同时支持`<thinking>`和`<think>`标签移除
- 集成到所有主要的LLM处理模块中
- 添加环境变量配置支持
- 使用优化的正则表达式，提高匹配效率和准确性
