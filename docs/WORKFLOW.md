# 起诉状格式化软件工作流程

## 概述

起诉状格式化软件是一个基于Flask的Web应用，通过OCR识别、大模型处理和文档生成，将用户上传的原始案件材料转换为标准格式的起诉状Word文档。系统支持多种文件格式，能够根据内容智能选择适用的法律文书模板，并支持多原告、被告的处理。

## 系统架构

```
                       ┌──────────────┐
                       │    前端界面    │
                       └───────┬──────┘
                               │
                               ▼
┌────────────┐        ┌──────────────┐        ┌────────────┐
│  文件上传处理  │◄──────│    Flask应用   │──────►│ 状态管理追踪 │
└───────┬────┘        └───────┬──────┘        └────────────┘
        │                     │
        ▼                     ▼
┌────────────┐        ┌──────────────┐
│   OCR处理   │        │   批量处理    │
└───────┬────┘        └───────┬──────┘
        │                     │
        │                     │
        ▼                     ▼
┌────────────┐        ┌──────────────┐
│  LLM分析处理 │◄──────┤   数据整合    │
└───────┬────┘        └──────────────┘
        │
        ▼
┌────────────┐        ┌──────────────┐        ┌────────────┐
│ 当事人信息处理 │──────►│  模板选择     │──────►│  文档生成器   │
└────────────┘        └──────────────┘        └────────────┘
```

## 主要工作流程

### 1. 应用初始化与配置

1. 从`.env`文件或环境变量加载配置
2. 初始化Flask应用
3. 确保必要的目录存在（上传、下载、模板等）
4. 设置IP访问控制（基于GeoLite2数据库）
5. 初始化OCR引擎（默认使用百度OCR）

### 2. 文件上传处理

1. 用户通过Web界面上传文件
2. 系统检查文件类型和大小是否符合要求
3. 为文件生成唯一标识符（UUID）
4. 保存文件到指定目录
5. 创建异步处理任务

### 3. OCR文本识别

1. 根据配置选择OCR引擎（Tesseract、百度OCR或PaddleOCR）
2. 针对不同文件类型执行相应的处理：
   - 图片文件：直接进行OCR处理
   - PDF文件：转换为图片后进行OCR处理
   - Word文件：提取文本内容，无需OCR处理
3. 将识别结果保存为文本文件

### 4. 大模型分析处理

1. 初始化LLM处理器，连接到配置的API端点
2. 对OCR处理后的文本进行分析：
   - 案件类型识别
   - 当事人信息提取（原告、被告）
   - 案件事实与理由分析
   - 诉讼请求提取
3. 将分析结果转换为结构化数据（JSON格式）

### 5. 当事人信息专项处理

1. 使用专门的`PartyInfoProcessor`类处理当事人信息
2. 从文本中提取各类当事人信息：
   ```json
   {
     "原告（自然人）列表": [
       {"姓名": "张三", "性别": "男", "身份证号码": "110101198001010000", ...}
     ],
     "原告（法人、非法人组织）列表": [
       {"名称": "某公司", "统一社会信用代码": "91310000XXXXXXXX", ...}
     ],
     "被告（自然人）列表": [...],
     "被告（法人、非法人组织）列表": [...],
     "第三人（自然人）列表": [...],
     "第三人（法人、非法人组织）列表": [...]
   }
   ```
3. 执行多重验证：
   - 确保原告或被告至少有一方不为空
   - 验证每个当事人必要信息的完整性
   - 检查并补全可能缺失的当事人列表
4. 支持调试模式，记录详细的处理日志

### 6. 智能模板选择

1. 基于大模型分析的案件类型，选择对应的文档模板
2. 支持两种选择模式：
   - 自动模式：系统根据内容自动选择合适的模板
   - 手动模式：提供模板列表供用户选择
3. 加载选定的Word模板文件

### 7. 文档生成

1. 初始化`DocumentGenerator`对象
2. 处理多当事人模板：
   - 查找主模板中的当事人插入点
   - 加载对应类型的当事人模板
   - 对每个当事人数据应用模板并进行占位符替换
   - 使用深拷贝方法将处理后的当事人模板完整插入到主文档中
3. 处理模板中的循环结构：
   - 识别`{% for ... %}`和`{% endfor %}`标记
   - 根据数据列表展开循环内容
4. 替换其他普通占位符
5. 处理文档中的复选框：
   - 通过配置控制复选框处理：
     ```json
     {
       "process_checkboxes": true,        // 是否启用复选框处理
       "use_llm_for_checkbox": true,      // 是否使用LLM处理复选框
       "use_rule_engine_for_checkbox": true, // 是否使用规则引擎处理复选框
       "debug_mode": false                // 是否启用调试模式
     }
     ```
   - 支持三种处理模式：
     1. LLM处理：使用大模型智能判断是否勾选
     2. 规则引擎处理：使用预定义规则判断是否勾选
     3. 基础处理：跳过判断，保持复选框原始状态
   - 可以通过设置`process_checkboxes=false`完全跳过复选框处理
   - 支持互斥选项组的自动识别（如性别选择、是/否选项）
   - 批量处理时支持上下文共享和结果复用
   - **跳过特定行的复选框处理**：
     - 方法1：在案件数据中添加忽略规则配置
       ```json
       {
         "checkbox_ignore_patterns": [
           "特定文本内容",  // 包含此文本的行将跳过复选框处理
           "^第.*条$"      // 支持正则表达式匹配
         ]
       }
       ```
     - 方法2：添加自定义规则处理器
       ```python
       # 在rule_handler.py的_get_common_rules中添加特定忽略规则
       {
         'pattern': r'需要忽略的特定文本',
         'field_path': ['checkbox_skip', 'enabled'],
         'condition': lambda value, match: value is True  # 返回True则表示该复选框应被忽略
       }
       ```
     - 方法3：使用数据预处理
       ```json
       {
         "checkbox_keywords": {
           "需要忽略的关键词": false  // 设置为false表示明确不勾选
         }
       }
       ```
6. 保存生成的Word文档

### 8. 结果展示与下载

1. 展示处理结果页面
2. 提供生成文档的下载链接
3. 显示处理日志和状态信息

## 批量处理流程

系统支持批量处理多个案件文件：

1. **批量任务创建**：
   - 生成批量任务ID
   - 为每个文件创建子任务
   - 初始化任务状态跟踪

2. **并行处理**：
   - 使用线程池并行处理多个文件
   - 每个文件独立执行OCR和分析流程
   - 实时更新处理进度

3. **状态管理**：
   - 使用全局字典跟踪任务状态
   - 记录每个子任务的处理结果
   - 支持实时查询进度

4. **结果整合**：
   - 收集所有子任务的处理结果
   - 生成批量下载包
   - 提供处理报告

## 错误处理与异常流程

1. **文件处理错误**：
   - 文件类型不支持：返回错误信息
   - 文件大小超限：提示用户重新上传
   - OCR识别失败：尝试备选OCR引擎

2. **API调用错误**：
   - LLM API超时：自动重试
   - API调用失败：记录错误并通知用户
   - 响应解析错误：返回友好的错误信息

3. **当事人信息处理错误**：
   - 信息不完整：提供手动编辑界面
   - 验证失败：显示具体错误原因
   - 格式错误：自动修正或提示用户

4. **模板处理错误**：
   - 模板不存在：提供备选模板
   - 占位符替换失败：记录错误并跳过
   - 格式错误：保持原有格式

## 安全性设计

1. **IP访问控制**：
   - 支持精确IP匹配
   - 支持CIDR范围控制
   - 基于GeoLite2数据库的地理位置控制

2. **文件安全**：
   - 文件类型白名单
   - 文件大小限制
   - 安全的文件名处理

3. **API安全**：
   - API密钥管理
   - 请求超时控制
   - CORS安全配置

## 性能优化

1. **异步处理**：
   - 文件处理使用异步任务
   - 状态更新实时推送
   - 避免长时间阻塞

2. **资源管理**：
   - 自动清理临时文件
   - 定期清理过期任务
   - 内存使用优化

3. **并发控制**：
   - 批量处理使用线程池
   - API调用限流
   - 资源使用监控

## 配置参数说明

主要配置参数通过`.env`文件或环境变量设置：

1. **服务配置**：
   - 服务端口
   - 调试模式
   - 允许的IP列表

2. **OCR配置**：
   - OCR引擎选择
   - API密钥
   - 语言设置

3. **LLM配置**：
   - API端点
   - 模型选择
   - 超时设置

4. **文件处理**：
   - 上传目录
   - 允许的文件类型
   - 文件大小限制

5. **模板配置**：
   - 模板目录
   - 模板映射关系
   - 案件类型定义 