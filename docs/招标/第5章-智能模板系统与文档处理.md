# 第5章：智能模板系统与文档处理

## 5.1 模板库架构设计

### 5.1.1 分层模板体系

云智讼智能模板系统采用分层架构设计，建立了完整的法律文书模板体系。该体系不仅涵盖了11种主要案件类型的标准模板，更通过智能化的模板管理和匹配机制，实现了高效、准确的文档生成。

```mermaid
graph TB
    subgraph "输入层"
        A1[原始文档]
        A2[案件信息]
        A3[当事人信息]
        A4[用户需求]
    end

    subgraph "分析层"
        B1[文档类型识别]
        B2[案件类型判断]
        B3[要素提取]
        B4[结构分析]
    end

    subgraph "模板匹配层"
        C1[模板检索引擎]
        C2[相似度计算]
        C3[智能推荐]
        C4[最优选择]
    end

    subgraph "分层模板库"
        D1[基础模板层]
        D2[案件类型模板层]
        D3[业务场景模板层]
        D4[个性化定制层]
    end

    subgraph "具体模板"
        E1[民间借贷模板]
        E2[合同纠纷模板]
        E3[侵权纠纷模板]
        E4[婚姻家庭模板]
        E5[劳动争议模板]
    end

    subgraph "生成层"
        F1[内容填充]
        F2[格式调整]
        F3[逻辑校验]
        F4[质量检查]
    end

    subgraph "输出层"
        G1[标准化文档]
        G2[要素化文档]
        G3[多格式输出]
        G4[批量生成]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4

    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4

    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4

    D1 --> E1
    D2 --> E2
    D3 --> E3
    D4 --> E4
    D2 --> E5

    E1 --> F1
    E2 --> F2
    E3 --> F3
    E4 --> F4
    E5 --> F1

    F1 --> G1
    F2 --> G2
    F3 --> G3
    F4 --> G4

    style A1 fill:#e3f2fd
    style A2 fill:#e3f2fd
    style B1 fill:#e8f5e8
    style B2 fill:#e8f5e8
    style C1 fill:#fff3e0
    style C2 fill:#fff3e0
    style D1 fill:#fce4ec
    style D2 fill:#fce4ec
    style D3 fill:#fce4ec
    style D4 fill:#fce4ec
    style E1 fill:#f3e5f5
    style E2 fill:#f3e5f5
    style F1 fill:#e0f2f1
    style F2 fill:#e0f2f1
    style G1 fill:#e1f5fe
    style G2 fill:#e1f5fe
```

智能模板系统采用了科学的分层架构设计，通过四个层次的模板体系，实现了从通用到专用、从标准到个性化的全方位覆盖。

基础模板层构成了整个模板体系的基础框架，为所有类型的法律文书提供统一的结构基础。通用文书结构模板定义了起诉状的基本结构框架，包括标题、当事人信息、诉讼请求、事实与理由、证据清单等核心要素的组织方式。标准格式模板严格按照最高人民法院制定的要素式文书标准进行设计，确保生成的文档完全符合司法规范要求。法院特定模板针对不同级别法院的格式要求进行了专门适配，包括基层法院、中级法院、高级法院等不同层级的特殊要求。地区定制模板考虑到不同地区在司法实践中的特殊要求和习惯做法，提供了地区化的模板支持。

案件类型模板层针对不同的法律案件类型提供了专业化的模板支持。民间借贷纠纷模板专门针对借贷关系的特点，包含了借款金额、利率、还款期限、担保方式等专业要素。合同纠纷模板涵盖了各类合同争议的处理，包括买卖合同、服务合同、租赁合同等不同类型的合同纠纷。侵权纠纷模板专门处理人身损害、财产损害等侵权案件，包含了损害事实、因果关系、赔偿标准等关键要素。婚姻家庭模板针对离婚、继承、抚养等家事纠纷的特点，提供了专业的模板支持。

业务场景模板层根据不同的诉讼程序和业务场景提供了差异化的模板支持。简易程序模板适用于事实清楚、争议不大的案件，采用简化的格式和流程，提高处理效率。普通程序模板按照标准民事诉讼程序的要求设计，适用于复杂案件的处理。小额诉讼模板专门为小额诉讼程序设计，体现了程序简化、快速审理的特点。公益诉讼模板专门为检察机关提起的公益诉讼设计，体现了公益诉讼的特殊性和专业要求。

个性化定制层为用户提供了灵活的模板定制能力，满足不同用户的个性化需求。用户自定义模板功能允许用户根据自己的业务特点和使用习惯创建个性化的模板。机构定制模板为律师事务所、法院、企业法务等特定机构提供专门定制的模板服务。动态生成模板基于人工智能技术，能够根据案件的具体情况动态生成最适合的模板。历史优化模板通过分析用户的使用反馈和效果评价，持续优化模板的质量和适用性。

智能模板系统采用了先进的数据结构设计，通过科学的数据模型确保模板的灵活性、可扩展性和易维护性。

模板数据结构设计基于面向对象的设计理念，采用分层的数据模型来描述模板的各个组成部分。系统定义了多种模板类型枚举，包括基础模板、案件特定模板、场景特定模板和自定义模板等，为不同类型的模板提供了清晰的分类标准。

模板类型分类体系确保了模板的有序管理和高效检索。基础模板类型提供了通用的文书结构和格式规范，作为其他模板类型的基础。案件特定模板类型针对不同的法律案件类型提供专业化的模板支持，确保模板内容的专业性和准确性。场景特定模板类型根据不同的业务场景和诉讼程序提供差异化的模板服务。自定义模板类型为用户提供了灵活的个性化定制能力。

字段类型定义系统为模板中的各种数据字段提供了标准化的类型定义，确保数据的一致性和准确性。
    TEXT = "text"
    NUMBER = "number"
    DATE = "date"
    SELECTION = "selection"
    MULTI_SELECTION = "multi_selection"
    PARTY_INFO = "party_info"
    AMOUNT = "amount"
    LEGAL_REFERENCE = "legal_reference"

@dataclass
class TemplateField:
    """模板字段定义"""
    name: str
    field_type: FieldType
    label: str
    description: str
    required: bool = True
    default_value: Any = None
    validation_rules: Dict[str, Any] = field(default_factory=dict)
    options: List[str] = field(default_factory=list)
    placeholder: str = ""
    help_text: str = ""

@dataclass
class TemplateSection:
    """模板章节定义"""
    name: str
    title: str
    order: int
    fields: List[TemplateField] = field(default_factory=list)
    subsections: List['TemplateSection'] = field(default_factory=list)
    conditional_logic: Dict[str, Any] = field(default_factory=dict)
    content_template: str = ""

@dataclass
class LegalTemplate:
    """法律文书模板"""
    id: str
    name: str
    template_type: TemplateType
    case_types: List[str]
    version: str
    description: str
    sections: List[TemplateSection] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: str = ""
    updated_at: str = ""
    created_by: str = ""
    status: str = "active"

    def get_all_fields(self) -> List[TemplateField]:
        """获取模板中的所有字段"""
        fields = []
        for section in self.sections:
            fields.extend(self._extract_fields_from_section(section))
        return fields

    def _extract_fields_from_section(self, section: TemplateSection) -> List[TemplateField]:
        """从章节中提取字段"""
        fields = section.fields.copy()
        for subsection in section.subsections:
            fields.extend(self._extract_fields_from_section(subsection))
        return fields

class TemplateRepository:
    """模板仓库管理"""

    def __init__(self, storage_backend):
        self.storage = storage_backend
        self.cache = TemplateCache()
        self.validator = TemplateValidator()
        self.version_manager = TemplateVersionManager()

模板创建功能采用了严格的验证和管理流程，确保新创建的模板符合法律文书的规范要求。

模板验证机制是创建流程的第一道关口，通过专门的验证器对模板的结构、字段、格式等进行全面检查。验证器不仅检查模板的语法正确性，还验证其是否符合法律文书的专业要求和格式规范。只有通过验证的模板才能进入后续的创建流程。

模板标识生成采用智能化的ID生成策略，根据模板的类型、创建时间、内容特征等信息生成唯一的模板标识。这种标识不仅保证了唯一性，还具有一定的可读性，便于模板的管理和检索。

版本信息管理为每个模板建立了完整的版本控制体系。新创建的模板自动设置为1.0.0版本，并记录创建时间和更新时间。版本信息的管理为模板的后续更新和维护提供了重要支撑。

模板存储和缓存机制确保了模板数据的可靠性和访问效率。系统将模板数据持久化存储到数据库中，同时在缓存中保留副本以提高访问速度。这种双重保障机制既确保了数据的安全性，又保证了系统的高性能。

版本历史记录功能为每个模板建立了完整的版本历史档案，记录模板的创建、修改、使用等关键信息。这些历史记录不仅有助于模板的管理和审计，还为模板的优化和改进提供了重要的数据支撑。

模板获取功能采用了高效的缓存优先策略，确保模板访问的高性能和低延迟。

缓存优先机制首先尝试从高速缓存中获取模板数据，如果缓存命中则直接返回结果，大大提升了访问速度。这种策略特别适合频繁访问的热门模板，能够显著改善用户体验。

存储回退机制在缓存未命中时，自动从持久化存储中加载模板数据。加载成功后，系统会自动将模板数据更新到缓存中，为后续的访问提供加速支持。这种缓存更新策略确保了缓存数据的时效性和完整性。

模板更新功能实现了安全可控的模板修改流程，确保更新操作不会破坏模板的完整性和一致性。

更新验证机制对待更新的模板进行全面的验证检查，确保修改后的模板仍然符合法律文书的规范要求。验证不通过的更新操作会被自动拒绝，并向用户提供详细的错误信息。

版本控制机制在更新前会获取当前模板的版本信息，确保更新操作基于最新的模板版本。这种机制有效避免了并发更新可能导致的数据冲突问题。
            raise TemplateNotFoundError(f"模板 {template.id} 不存在")

        # 更新版本号
        template.version = self.version_manager.increment_version(current_template.version)
        template.updated_at = datetime.utcnow().isoformat()

        # 保存更新
        await self.storage.save_template(template)

        # 更新缓存
        await self.cache.set_template(template.id, template)

        # 记录版本历史
        await self.version_manager.create_version_record(template)

        return True

    async def search_templates(self, criteria: Dict[str, Any]) -> List[LegalTemplate]:
        """搜索模板"""
        return await self.storage.search_templates(criteria)

    def generate_template_id(self, template: LegalTemplate) -> str:
        """生成模板ID"""
        import hashlib
        content = f"{template.name}_{template.template_type.value}_{template.case_types}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
```

### 5.1.2 模板版本管理

模板版本管理确保模板的可追溯性和向后兼容性，支持模板的迭代升级和回滚操作。

**版本管理策略**：

```python
class TemplateVersionManager:
    """模板版本管理器"""

    def __init__(self, storage_backend):
        self.storage = storage_backend
        self.diff_engine = TemplateDiffEngine()
        self.migration_engine = TemplateMigrationEngine()

    def increment_version(self, current_version: str, change_type: str = "minor") -> str:
        """递增版本号"""
        major, minor, patch = map(int, current_version.split('.'))

        if change_type == "major":
            return f"{major + 1}.0.0"
        elif change_type == "minor":
            return f"{major}.{minor + 1}.0"
        else:  # patch
            return f"{major}.{minor}.{patch + 1}"

    async def create_version_record(self, template: LegalTemplate):
        """创建版本记录"""
        version_record = {
            'template_id': template.id,
            'version': template.version,
            'template_data': template,
            'created_at': datetime.utcnow().isoformat(),
            'created_by': template.created_by,
            'change_summary': self.generate_change_summary(template)
        }

        await self.storage.save_version_record(version_record)

    async def get_version_history(self, template_id: str) -> List[Dict[str, Any]]:
        """获取版本历史"""
        return await self.storage.get_version_history(template_id)

    async def compare_versions(self, template_id: str, version1: str, version2: str) -> Dict[str, Any]:
        """比较版本差异"""
        template1 = await self.storage.get_template_version(template_id, version1)
        template2 = await self.storage.get_template_version(template_id, version2)

        if not template1 or not template2:
            raise VersionNotFoundError("指定版本不存在")

        diff_result = self.diff_engine.compare_templates(template1, template2)
        return diff_result

    async def rollback_template(self, template_id: str, target_version: str) -> bool:
        """回滚模板到指定版本"""
        target_template = await self.storage.get_template_version(template_id, target_version)
        if not target_template:
            raise VersionNotFoundError(f"版本 {target_version} 不存在")

        # 创建新版本（基于目标版本）
        current_template = await self.storage.load_template(template_id)
        new_version = self.increment_version(current_template.version, "patch")

        target_template.version = new_version
        target_template.updated_at = datetime.utcnow().isoformat()

        # 保存回滚后的模板
        await self.storage.save_template(target_template)
        await self.create_version_record(target_template)

        return True

class TemplateDiffEngine:
    """模板差异比较引擎"""

    def compare_templates(self, template1: LegalTemplate, template2: LegalTemplate) -> Dict[str, Any]:
        """比较两个模板的差异"""
        diff_result = {
            'metadata_changes': self.compare_metadata(template1, template2),
            'section_changes': self.compare_sections(template1.sections, template2.sections),
            'field_changes': self.compare_fields(template1.get_all_fields(), template2.get_all_fields())
        }

        return diff_result

    def compare_metadata(self, template1: LegalTemplate, template2: LegalTemplate) -> Dict[str, Any]:
        """比较元数据"""
        changes = {}

        metadata_fields = ['name', 'description', 'case_types', 'status']
        for field in metadata_fields:
            value1 = getattr(template1, field)
            value2 = getattr(template2, field)
            if value1 != value2:
                changes[field] = {'old': value1, 'new': value2}

        return changes

    def compare_sections(self, sections1: List[TemplateSection], sections2: List[TemplateSection]) -> Dict[str, Any]:
        """比较章节"""
        changes = {
            'added': [],
            'removed': [],
            'modified': []
        }

        sections1_dict = {s.name: s for s in sections1}
        sections2_dict = {s.name: s for s in sections2}

        # 查找新增的章节
        for name, section in sections2_dict.items():
            if name not in sections1_dict:
                changes['added'].append(section)

        # 查找删除的章节
        for name, section in sections1_dict.items():
            if name not in sections2_dict:
                changes['removed'].append(section)

        # 查找修改的章节
        for name in sections1_dict:
            if name in sections2_dict:
                section1 = sections1_dict[name]
                section2 = sections2_dict[name]
                if self.sections_differ(section1, section2):
                    changes['modified'].append({
                        'name': name,
                        'old': section1,
                        'new': section2
                    })

        return changes
```

### 5.1.3 模板标准化与规范

建立统一的模板标准和规范，确保生成文档的格式一致性和法律合规性。

**模板标准规范**：

```python
class TemplateStandard:
    """模板标准规范"""

    # 标准章节结构
    STANDARD_SECTIONS = {
        'header': {
            'name': '文书头部',
            'required': True,
            'order': 1,
            'fields': ['court_name', 'case_number', 'document_type']
        },
        'parties': {
            'name': '当事人信息',
            'required': True,
            'order': 2,
            'fields': ['plaintiff_info', 'defendant_info', 'third_party_info']
        },
        'claims': {
            'name': '诉讼请求',
            'required': True,
            'order': 3,
            'fields': ['claim_items', 'litigation_costs']
        },
        'facts_and_reasons': {
            'name': '事实与理由',
            'required': True,
            'order': 4,
            'fields': ['case_facts', 'legal_basis', 'reasoning']
        },
        'evidence': {
            'name': '证据',
            'required': False,
            'order': 5,
            'fields': ['evidence_list', 'witness_list']
        },
        'footer': {
            'name': '文书尾部',
            'required': True,
            'order': 6,
            'fields': ['court_designation', 'date', 'signature']
        }
    }

    # 字段验证规则
    FIELD_VALIDATION_RULES = {
        'court_name': {
            'type': 'text',
            'required': True,
            'max_length': 100,
            'pattern': r'.*人民法院$'
        },
        'case_number': {
            'type': 'text',
            'required': False,
            'pattern': r'\(\d{4}\).*\d+号'
        },
        'amount': {
            'type': 'number',
            'required': False,
            'min_value': 0,
            'max_value': 999999999.99
        },
        'date': {
            'type': 'date',
            'required': True,
            'format': 'YYYY-MM-DD'
        },
        'phone': {
            'type': 'text',
            'pattern': r'^1[3-9]\d{9}$|^\d{3,4}-\d{7,8}$'
        },
        'id_number': {
            'type': 'text',
            'pattern': r'^\d{17}[\dXx]$|^\d{15}$'
        }
    }

    # 格式规范
    FORMAT_STANDARDS = {
        'font_family': '宋体',
        'font_size': {
            'title': 16,
            'heading': 14,
            'body': 12,
            'footnote': 10
        },
        'line_spacing': 1.5,
        'margin': {
            'top': 2.5,
            'bottom': 2.5,
            'left': 2.5,
            'right': 2.5
        },
        'page_size': 'A4',
        'orientation': 'portrait'
    }

class TemplateValidator:
    """模板验证器"""

    def __init__(self):
        self.standard = TemplateStandard()
        self.legal_checker = LegalComplianceChecker()

    async def validate_template(self, template: LegalTemplate) -> ValidationResult:
        """验证模板"""
        errors = []
        warnings = []

        # 结构验证
        structure_errors = self.validate_structure(template)
        errors.extend(structure_errors)

        # 字段验证
        field_errors = self.validate_fields(template)
        errors.extend(field_errors)

        # 格式验证
        format_warnings = self.validate_format(template)
        warnings.extend(format_warnings)

        # 法律合规性验证
        compliance_result = await self.legal_checker.check_compliance(template)
        errors.extend(compliance_result.errors)
        warnings.extend(compliance_result.warnings)

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )

    def validate_structure(self, template: LegalTemplate) -> List[str]:
        """验证模板结构"""
        errors = []

        # 检查必需章节
        template_sections = {s.name for s in template.sections}
        for section_name, section_config in self.standard.STANDARD_SECTIONS.items():
            if section_config['required'] and section_name not in template_sections:
                errors.append(f"缺少必需章节: {section_config['name']}")

        # 检查章节顺序
        section_orders = {s.name: s.order for s in template.sections}
        for section_name, expected_order in [(name, config['order'])
                                           for name, config in self.standard.STANDARD_SECTIONS.items()]:
            if section_name in section_orders:
                actual_order = section_orders[section_name]
                if actual_order != expected_order:
                    errors.append(f"章节 {section_name} 顺序错误，期望: {expected_order}, 实际: {actual_order}")

        return errors

    def validate_fields(self, template: LegalTemplate) -> List[str]:
        """验证模板字段"""
        errors = []

        all_fields = template.get_all_fields()
        for field in all_fields:
            field_errors = self.validate_single_field(field)
            errors.extend(field_errors)

        return errors

    def validate_single_field(self, field: TemplateField) -> List[str]:
        """验证单个字段"""
        errors = []

        # 检查字段名称
        if not field.name:
            errors.append("字段名称不能为空")

        # 检查字段类型
        if field.field_type not in FieldType:
            errors.append(f"无效的字段类型: {field.field_type}")

        # 检查验证规则
        if field.name in self.standard.FIELD_VALIDATION_RULES:
            standard_rules = self.standard.FIELD_VALIDATION_RULES[field.name]
            field_errors = self.validate_field_rules(field, standard_rules)
            errors.extend(field_errors)

        return errors

    def validate_field_rules(self, field: TemplateField, rules: Dict[str, Any]) -> List[str]:
        """验证字段规则"""
        errors = []

        # 检查必填性
        if rules.get('required', False) and not field.required:
            errors.append(f"字段 {field.name} 应该是必填的")

        # 检查字段类型匹配
        expected_type = rules.get('type')
        if expected_type and field.field_type.value != expected_type:
            errors.append(f"字段 {field.name} 类型不匹配，期望: {expected_type}, 实际: {field.field_type.value}")

        return errors

@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
```

## 5.2 智能模板匹配算法

### 5.2.1 多维度匹配策略

智能模板匹配是系统的核心功能之一，通过多维度分析确定最适合的模板。

**匹配维度分析**：

```python
class TemplateMatchingEngine:
    """模板匹配引擎"""

    def __init__(self):
        self.case_type_classifier = CaseTypeClassifier()
        self.content_analyzer = ContentAnalyzer()
        self.similarity_calculator = SimilarityCalculator()
        self.template_repository = TemplateRepository()
        self.matching_weights = {
            'case_type': 0.4,
            'content_similarity': 0.3,
            'structural_match': 0.2,
            'user_preference': 0.1
        }

    async def find_best_template(self, document_content: str, user_context: Dict[str, Any] = None) -> TemplateMatchResult:
        """查找最佳匹配模板"""
        # 1. 案件类型识别
        case_type_result = await self.case_type_classifier.classify(document_content)

        # 2. 获取候选模板
        candidate_templates = await self.get_candidate_templates(case_type_result)

        # 3. 多维度评分
        scored_templates = []
        for template in candidate_templates:
            score = await self.calculate_template_score(
                template, document_content, case_type_result, user_context
            )
            scored_templates.append((template, score))

        # 4. 排序并选择最佳模板
        scored_templates.sort(key=lambda x: x[1]['total_score'], reverse=True)

        if not scored_templates:
            raise NoSuitableTemplateError("未找到合适的模板")

        best_template, best_score = scored_templates[0]

        return TemplateMatchResult(
            template=best_template,
            confidence=best_score['total_score'],
            score_breakdown=best_score,
            alternatives=[t for t, s in scored_templates[1:6]]  # 前5个备选
        )

    async def calculate_template_score(self, template: LegalTemplate, content: str,
                                     case_type_result: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, float]:
        """计算模板匹配得分"""
        scores = {}

        # 案件类型匹配得分
        scores['case_type'] = self.calculate_case_type_score(template, case_type_result)

        # 内容相似度得分
        scores['content_similarity'] = await self.calculate_content_similarity_score(template, content)

        # 结构匹配得分
        scores['structural_match'] = await self.calculate_structural_match_score(template, content)

        # 用户偏好得分
        scores['user_preference'] = self.calculate_user_preference_score(template, user_context)

        # 计算总分
        total_score = sum(
            scores[dimension] * self.matching_weights[dimension]
            for dimension in scores
        )

        scores['total_score'] = total_score
        return scores

    def calculate_case_type_score(self, template: LegalTemplate, case_type_result: Dict[str, Any]) -> float:
        """计算案件类型匹配得分"""
        predicted_case_type = case_type_result['case_type']
        confidence = case_type_result['confidence']

        if predicted_case_type in template.case_types:
            # 精确匹配
            return confidence
        else:
            # 检查相关案件类型
            related_score = self.get_related_case_type_score(predicted_case_type, template.case_types)
            return related_score * confidence * 0.7  # 相关匹配打折

    async def calculate_content_similarity_score(self, template: LegalTemplate, content: str) -> float:
        """计算内容相似度得分"""
        # 提取模板示例内容
        template_examples = await self.get_template_examples(template.id)

        if not template_examples:
            return 0.5  # 默认中等得分

        # 计算与示例的相似度
        similarities = []
        for example in template_examples:
            similarity = await self.similarity_calculator.calculate_similarity(content, example)
            similarities.append(similarity)

        # 返回最高相似度
        return max(similarities) if similarities else 0.5

    async def calculate_structural_match_score(self, template: LegalTemplate, content: str) -> float:
        """计算结构匹配得分"""
        # 分析文档结构
        document_structure = await self.content_analyzer.analyze_structure(content)

        # 分析模板结构
        template_structure = self.extract_template_structure(template)

        # 计算结构相似度
        structure_similarity = self.compare_structures(document_structure, template_structure)

        return structure_similarity

    def calculate_user_preference_score(self, template: LegalTemplate, user_context: Dict[str, Any]) -> float:
        """计算用户偏好得分"""
        if not user_context:
            return 0.5

        score = 0.5

        # 历史使用偏好
        if 'template_usage_history' in user_context:
            history = user_context['template_usage_history']
            if template.id in history:
                usage_count = history[template.id]
                score += min(usage_count * 0.1, 0.3)  # 最多加0.3分

        # 用户角色偏好
        if 'user_role' in user_context:
            role = user_context['user_role']
            if role in template.metadata.get('preferred_roles', []):
                score += 0.2

        # 地区偏好
        if 'region' in user_context:
            region = user_context['region']
            if region in template.metadata.get('applicable_regions', []):
                score += 0.1

        return min(score, 1.0)

@dataclass
class TemplateMatchResult:
    """模板匹配结果"""
    template: LegalTemplate
    confidence: float
    score_breakdown: Dict[str, float]
    alternatives: List[LegalTemplate] = field(default_factory=list)
```

### 5.2.2 语义相似度计算

基于深度学习的语义相似度计算，提高模板匹配的准确性。

**语义分析引擎**：

```python
class SemanticSimilarityCalculator:
    """语义相似度计算器"""

    def __init__(self):
        self.embedding_model = self.load_embedding_model()
        self.legal_corpus = LegalCorpus()
        self.similarity_cache = SimilarityCache()

    def load_embedding_model(self):
        """加载嵌入模型"""
        # 使用专门针对中文法律文本训练的嵌入模型
        from sentence_transformers import SentenceTransformer
        return SentenceTransformer('legal-bert-chinese')

    async def calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的语义相似度"""
        # 检查缓存
        cache_key = self.generate_cache_key(text1, text2)
        cached_result = await self.similarity_cache.get(cache_key)
        if cached_result is not None:
            return cached_result

        # 文本预处理
        processed_text1 = self.preprocess_legal_text(text1)
        processed_text2 = self.preprocess_legal_text(text2)

        # 生成嵌入向量
        embedding1 = self.embedding_model.encode(processed_text1)
        embedding2 = self.embedding_model.encode(processed_text2)

        # 计算余弦相似度
        similarity = self.cosine_similarity(embedding1, embedding2)

        # 缓存结果
        await self.similarity_cache.set(cache_key, similarity)

        return similarity

    def preprocess_legal_text(self, text: str) -> str:
        """法律文本预处理"""
        # 移除多余空白
        text = re.sub(r'\s+', ' ', text.strip())

        # 标准化法律术语
        text = self.legal_corpus.normalize_legal_terms(text)

        # 移除无关信息
        text = self.remove_irrelevant_info(text)

        return text

    def remove_irrelevant_info(self, text: str) -> str:
        """移除无关信息"""
        # 移除日期、时间等可变信息
        text = re.sub(r'\d{4}年\d{1,2}月\d{1,2}日', '[日期]', text)
        text = re.sub(r'\d{1,2}:\d{2}', '[时间]', text)

        # 移除具体金额
        text = re.sub(r'\d+(\.\d+)?元', '[金额]', text)

        # 移除具体姓名（保留角色）
        text = re.sub(r'[原被第三]告[：:]?\s*([张王李赵刘陈杨黄周吴徐孙朱马胡郭林何高梁郑罗宋谢唐韩曹许邓萧冯曾程蔡彭潘袁于董余苏叶吕魏蒋田杜丁沈姜范江傅钟卢汪戴崔任陆廖姚方金邱夏谭韦贾邹石熊孟秦阎薛侯雷白龙段郝孔邵史毛常万顾赖武康贺严尹钱施牛洪龚汤陶黎温莫易樊乔文安殷颜庄章鲁倪庞邢俞翟蓝聂齐向葛高柳洪]\w{1,3})', r'\1[姓名]', text)

        return text

    def cosine_similarity(self, vec1, vec2):
        """计算余弦相似度"""
        import numpy as np
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        return dot_product / (norm1 * norm2)

    def generate_cache_key(self, text1: str, text2: str) -> str:
        """生成缓存键"""
        import hashlib
        combined = f"{text1}|||{text2}"
        return hashlib.md5(combined.encode()).hexdigest()

class LegalCorpus:
    """法律语料库"""

    def __init__(self):
        self.legal_terms = self.load_legal_terms()
        self.synonyms = self.load_legal_synonyms()
        self.abbreviations = self.load_legal_abbreviations()

    def normalize_legal_terms(self, text: str) -> str:
        """标准化法律术语"""
        # 展开缩写
        for abbrev, full_form in self.abbreviations.items():
            text = text.replace(abbrev, full_form)

        # 统一同义词
        for term, standard_term in self.synonyms.items():
            text = text.replace(term, standard_term)

        return text

    def load_legal_terms(self) -> Dict[str, str]:
        """加载法律术语词典"""
        return {
            '民事诉讼': '民事诉讼',
            '刑事诉讼': '刑事诉讼',
            '行政诉讼': '行政诉讼',
            '合同纠纷': '合同纠纷',
            '侵权纠纷': '侵权纠纷',
            # ... 更多法律术语
        }

    def load_legal_synonyms(self) -> Dict[str, str]:
        """加载法律同义词"""
        return {
            '起诉书': '起诉状',
            '申请书': '申请状',
            '答辩书': '答辩状',
            '上诉书': '上诉状',
            # ... 更多同义词
        }

    def load_legal_abbreviations(self) -> Dict[str, str]:
        """加载法律缩写"""
        return {
            '民法典': '《中华人民共和国民法典》',
            '民诉法': '《中华人民共和国民事诉讼法》',
            '合同法': '《中华人民共和国合同法》',
            # ... 更多缩写
        }
```

### 5.2.3 机器学习优化

通过机器学习技术持续优化模板匹配算法。

**学习优化系统**：

```python
class TemplateMatchingOptimizer:
    """模板匹配优化器"""

    def __init__(self):
        self.feedback_collector = FeedbackCollector()
        self.model_trainer = ModelTrainer()
        self.feature_extractor = FeatureExtractor()
        self.performance_evaluator = PerformanceEvaluator()

    async def optimize_matching_algorithm(self):
        """优化匹配算法"""
        # 收集反馈数据
        feedback_data = await self.feedback_collector.collect_recent_feedback()

        # 提取特征
        features = await self.feature_extractor.extract_features(feedback_data)

        # 训练模型
        improved_model = await self.model_trainer.train_model(features)

        # 评估性能
        performance = await self.performance_evaluator.evaluate_model(improved_model)

        # 如果性能提升，则部署新模型
        if performance['accuracy'] > self.get_current_accuracy():
            await self.deploy_improved_model(improved_model)
            return True

        return False

    async def collect_user_feedback(self, match_result: TemplateMatchResult,
                                  user_choice: str, user_rating: float):
        """收集用户反馈"""
        feedback = {
            'timestamp': datetime.utcnow().isoformat(),
            'recommended_template': match_result.template.id,
            'confidence': match_result.confidence,
            'score_breakdown': match_result.score_breakdown,
            'user_choice': user_choice,
            'user_rating': user_rating,
            'alternatives': [t.id for t in match_result.alternatives]
        }

        await self.feedback_collector.store_feedback(feedback)

    async def analyze_matching_patterns(self) -> Dict[str, Any]:
        """分析匹配模式"""
        feedback_data = await self.feedback_collector.get_all_feedback()

        analysis = {
            'accuracy_by_case_type': self.analyze_accuracy_by_case_type(feedback_data),
            'common_mismatches': self.identify_common_mismatches(feedback_data),
            'user_preference_patterns': self.analyze_user_preferences(feedback_data),
            'template_popularity': self.analyze_template_popularity(feedback_data)
        }

        return analysis

    def analyze_accuracy_by_case_type(self, feedback_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """按案件类型分析准确率"""
        accuracy_by_type = {}

        for case_type in self.get_all_case_types():
            type_feedback = [f for f in feedback_data if self.get_case_type_from_feedback(f) == case_type]
            if type_feedback:
                correct_matches = sum(1 for f in type_feedback if f['user_rating'] >= 4.0)
                accuracy = correct_matches / len(type_feedback)
                accuracy_by_type[case_type] = accuracy

        return accuracy_by_type

    def identify_common_mismatches(self, feedback_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """识别常见误匹配"""
        mismatches = []

        # 找出评分低的匹配
        poor_matches = [f for f in feedback_data if f['user_rating'] < 3.0]

        # 分析误匹配模式
        for match in poor_matches:
            mismatch_pattern = {
                'recommended_template': match['recommended_template'],
                'user_choice': match['user_choice'],
                'confidence': match['confidence'],
                'frequency': self.count_similar_mismatches(poor_matches, match)
            }
            mismatches.append(mismatch_pattern)

        # 按频率排序
        mismatches.sort(key=lambda x: x['frequency'], reverse=True)

        return mismatches[:10]  # 返回前10个常见误匹配

class FeatureExtractor:
    """特征提取器"""

    def __init__(self):
        self.text_analyzer = TextAnalyzer()
        self.structural_analyzer = StructuralAnalyzer()

    async def extract_features(self, feedback_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """提取特征"""
        features = {
            'text_features': [],
            'structural_features': [],
            'user_features': [],
            'temporal_features': []
        }

        for feedback in feedback_data:
            # 文本特征
            text_features = await self.extract_text_features(feedback)
            features['text_features'].append(text_features)

            # 结构特征
            structural_features = await self.extract_structural_features(feedback)
            features['structural_features'].append(structural_features)

            # 用户特征
            user_features = self.extract_user_features(feedback)
            features['user_features'].append(user_features)

            # 时间特征
            temporal_features = self.extract_temporal_features(feedback)
            features['temporal_features'].append(temporal_features)

        return features

    async def extract_text_features(self, feedback: Dict[str, Any]) -> Dict[str, float]:
        """提取文本特征"""
        document_text = feedback.get('document_text', '')

        features = {
            'document_length': len(document_text),
            'sentence_count': len(self.text_analyzer.split_sentences(document_text)),
            'legal_term_density': self.text_analyzer.calculate_legal_term_density(document_text),
            'complexity_score': await self.text_analyzer.calculate_complexity_score(document_text),
            'formality_score': self.text_analyzer.calculate_formality_score(document_text)
        }

        return features

    async def extract_structural_features(self, feedback: Dict[str, Any]) -> Dict[str, float]:
        """提取结构特征"""
        document_text = feedback.get('document_text', '')
        structure = await self.structural_analyzer.analyze_structure(document_text)

        features = {
            'section_count': len(structure.get('sections', [])),
            'has_header': 1.0 if structure.get('has_header') else 0.0,
            'has_footer': 1.0 if structure.get('has_footer') else 0.0,
            'paragraph_count': structure.get('paragraph_count', 0),
            'list_count': structure.get('list_count', 0),
            'table_count': structure.get('table_count', 0)
        }

        return features

class ModelTrainer:
    """模型训练器"""

    def __init__(self):
        self.model_type = 'gradient_boosting'
        self.hyperparameters = {
            'n_estimators': 100,
            'learning_rate': 0.1,
            'max_depth': 6
        }

    async def train_model(self, features: Dict[str, Any]):
        """训练模型"""
        from sklearn.ensemble import GradientBoostingClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score, classification_report

        # 准备训练数据
        X, y = self.prepare_training_data(features)

        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # 训练模型
        model = GradientBoostingClassifier(**self.hyperparameters)
        model.fit(X_train, y_train)

        # 评估模型
        y_pred = model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)

        print(f"模型训练完成，准确率: {accuracy:.4f}")
        print(classification_report(y_test, y_pred))

        return model

    def prepare_training_data(self, features: Dict[str, Any]) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        # 合并所有特征
        all_features = []
        labels = []

        for i in range(len(features['text_features'])):
            feature_vector = []

            # 添加文本特征
            text_feat = features['text_features'][i]
            feature_vector.extend(text_feat.values())

            # 添加结构特征
            struct_feat = features['structural_features'][i]
            feature_vector.extend(struct_feat.values())

            # 添加用户特征
            user_feat = features['user_features'][i]
            feature_vector.extend(user_feat.values())

            # 添加时间特征
            temporal_feat = features['temporal_features'][i]
            feature_vector.extend(temporal_feat.values())

            all_features.append(feature_vector)

            # 标签：用户评分是否>=4.0
            rating = features.get('ratings', [0])[i]
            labels.append(1 if rating >= 4.0 else 0)

        return np.array(all_features), np.array(labels)
```

## 5.3 多当事人模板支持

### 5.3.1 动态当事人管理

支持复杂案件中的多当事人场景，包括多个原告、被告和第三人。

**当事人模板系统**：

```python
class MultiPartyTemplateSystem:
    """多当事人模板系统"""

    def __init__(self):
        self.party_manager = PartyManager()
        self.template_generator = DynamicTemplateGenerator()
        self.relationship_analyzer = PartyRelationshipAnalyzer()

    async def generate_multi_party_template(self, parties: List[PartyInfo], case_context: Dict[str, Any]) -> LegalTemplate:
        """生成多当事人模板"""
        # 分析当事人关系
        relationships = await self.relationship_analyzer.analyze_relationships(parties)

        # 确定模板结构
        template_structure = self.determine_template_structure(parties, relationships)

        # 生成动态模板
        template = await self.template_generator.generate_template(
            template_structure, parties, case_context
        )

        return template

    def determine_template_structure(self, parties: List[PartyInfo],
                                   relationships: Dict[str, Any]) -> Dict[str, Any]:
        """确定模板结构"""
        structure = {
            'party_sections': [],
            'relationship_sections': [],
            'special_considerations': []
        }

        # 按角色分组当事人
        parties_by_role = self.group_parties_by_role(parties)

        # 为每个角色创建章节
        for role, role_parties in parties_by_role.items():
            if len(role_parties) == 1:
                # 单一当事人
                section = self.create_single_party_section(role, role_parties[0])
            else:
                # 多个当事人
                section = self.create_multi_party_section(role, role_parties)

            structure['party_sections'].append(section)

        # 处理特殊关系
        if relationships.get('has_joint_liability'):
            structure['special_considerations'].append('joint_liability')

        if relationships.get('has_representative'):
            structure['special_considerations'].append('representative_action')

        return structure

    def group_parties_by_role(self, parties: List[PartyInfo]) -> Dict[str, List[PartyInfo]]:
        """按角色分组当事人"""
        grouped = {}
        for party in parties:
            role = party.party_type
            if role not in grouped:
                grouped[role] = []
            grouped[role].append(party)

        return grouped

    def create_multi_party_section(self, role: str, parties: List[PartyInfo]) -> Dict[str, Any]:
        """创建多当事人章节"""
        section = {
            'name': f'{role}_info_multi',
            'title': f'{role}信息',
            'type': 'multi_party',
            'fields': []
        }

        # 为每个当事人创建字段组
        for i, party in enumerate(parties, 1):
            party_fields = self.create_party_fields(party, f'{role}_{i}')
            section['fields'].extend(party_fields)

        # 添加共同字段
        if len(parties) > 1:
            section['fields'].append(TemplateField(
                name=f'{role}_joint_representation',
                field_type=FieldType.SELECTION,
                label='是否共同委托代理人',
                options=['是', '否'],
                required=False
            ))

        return section

class PartyRelationshipAnalyzer:
    """当事人关系分析器"""

    def __init__(self):
        self.relationship_patterns = self.load_relationship_patterns()

    async def analyze_relationships(self, parties: List[PartyInfo]) -> Dict[str, Any]:
        """分析当事人关系"""
        relationships = {
            'has_joint_liability': False,
            'has_representative': False,
            'has_corporate_group': False,
            'relationship_matrix': {}
        }

        # 检测连带责任
        relationships['has_joint_liability'] = self.detect_joint_liability(parties)

        # 检测代表人诉讼
        relationships['has_representative'] = self.detect_representative_action(parties)

        # 检测企业集团关系
        relationships['has_corporate_group'] = self.detect_corporate_group(parties)

        # 构建关系矩阵
        relationships['relationship_matrix'] = self.build_relationship_matrix(parties)

        return relationships

    def detect_joint_liability(self, parties: List[PartyInfo]) -> bool:
        """检测连带责任"""
        # 检查是否有多个被告
        defendants = [p for p in parties if p.party_type == '被告']
        if len(defendants) <= 1:
            return False

        # 检查是否为常见的连带责任案件类型
        joint_liability_indicators = [
            '担保', '保证', '连带', '共同', '合伙', '夫妻债务'
        ]

        for party in defendants:
            party_info = f"{party.name} {party.address or ''}"
            if any(indicator in party_info for indicator in joint_liability_indicators):
                return True

        return False

    def detect_representative_action(self, parties: List[PartyInfo]) -> bool:
        """检测代表人诉讼"""
        # 检查原告数量
        plaintiffs = [p for p in parties if p.party_type == '原告']
        if len(plaintiffs) < 10:  # 代表人诉讼通常涉及较多当事人
            return False

        # 检查是否有相似的地址或背景
        addresses = [p.address for p in plaintiffs if p.address]
        if len(set(addresses)) < len(addresses) * 0.5:  # 超过一半地址相同
            return True

        return False

    def build_relationship_matrix(self, parties: List[PartyInfo]) -> Dict[str, Dict[str, str]]:
        """构建关系矩阵"""
        matrix = {}

        for i, party1 in enumerate(parties):
            matrix[party1.name] = {}
            for j, party2 in enumerate(parties):
                if i != j:
                    relationship = self.determine_relationship(party1, party2)
                    matrix[party1.name][party2.name] = relationship

        return matrix

    def determine_relationship(self, party1: PartyInfo, party2: PartyInfo) -> str:
        """确定两个当事人之间的关系"""
        # 同一角色
        if party1.party_type == party2.party_type:
            return 'same_role'

        # 对立角色
        opposing_roles = [('原告', '被告'), ('被告', '原告')]
        if (party1.party_type, party2.party_type) in opposing_roles:
            return 'opposing'

        # 第三人关系
        if party1.party_type == '第三人' or party2.party_type == '第三人':
            return 'third_party'

        return 'unknown'

class DynamicTemplateGenerator:
    """动态模板生成器"""

    def __init__(self):
        self.template_builder = TemplateBuilder()
        self.field_generator = FieldGenerator()

    async def generate_template(self, structure: Dict[str, Any], parties: List[PartyInfo],
                              case_context: Dict[str, Any]) -> LegalTemplate:
        """生成动态模板"""
        template = LegalTemplate(
            id=self.generate_template_id(parties, case_context),
            name=f"多当事人{case_context.get('case_type', '案件')}模板",
            template_type=TemplateType.CUSTOM,
            case_types=[case_context.get('case_type', 'general')],
            version="1.0.0",
            description=f"为{len(parties)}个当事人生成的动态模板"
        )

        # 生成标准章节
        template.sections = await self.generate_standard_sections()

        # 添加当事人章节
        for party_section in structure['party_sections']:
            section = await self.build_party_section(party_section, parties)
            template.sections.append(section)

        # 添加特殊考虑章节
        for consideration in structure['special_considerations']:
            section = await self.build_special_section(consideration, parties)
            template.sections.append(section)

        # 排序章节
        template.sections.sort(key=lambda s: s.order)

        return template

    async def generate_standard_sections(self) -> List[TemplateSection]:
        """生成标准章节"""
        sections = []

        # 文书头部
        header_section = TemplateSection(
            name='header',
            title='文书头部',
            order=1,
            fields=[
                TemplateField(
                    name='court_name',
                    field_type=FieldType.TEXT,
                    label='受理法院',
                    required=True
                ),
                TemplateField(
                    name='case_number',
                    field_type=FieldType.TEXT,
                    label='案件编号',
                    required=False
                )
            ]
        )
        sections.append(header_section)

        return sections

    async def build_party_section(self, party_section_config: Dict[str, Any],
                                parties: List[PartyInfo]) -> TemplateSection:
        """构建当事人章节"""
        section = TemplateSection(
            name=party_section_config['name'],
            title=party_section_config['title'],
            order=self.get_section_order(party_section_config['name']),
            fields=party_section_config['fields']
        )

        return section

    def get_section_order(self, section_name: str) -> int:
        """获取章节顺序"""
        order_map = {
            'header': 1,
            '原告_info': 2,
            '原告_info_multi': 2,
            '被告_info': 3,
            '被告_info_multi': 3,
            '第三人_info': 4,
            '第三人_info_multi': 4,
            'claims': 5,
            'facts_and_reasons': 6,
            'evidence': 7,
            'footer': 8
        }

        return order_map.get(section_name, 9)
```

### 5.3.2 角色模板适配

针对不同类型的当事人（自然人、法人、其他组织）提供专门的模板适配。

**角色适配系统**：

```python
class PartyRoleAdapter:
    """当事人角色适配器"""

    def __init__(self):
        self.role_templates = self.load_role_templates()
        self.field_validators = self.load_field_validators()

    def adapt_template_for_party(self, party: PartyInfo, base_template: LegalTemplate) -> LegalTemplate:
        """为特定当事人适配模板"""
        adapted_template = copy.deepcopy(base_template)

        # 根据实体类型调整字段
        if party.entity_type == '自然人':
            adapted_template = self.adapt_for_natural_person(adapted_template, party)
        elif party.entity_type == '法人':
            adapted_template = self.adapt_for_legal_entity(adapted_template, party)
        elif party.entity_type == '其他组织':
            adapted_template = self.adapt_for_other_organization(adapted_template, party)

        # 根据角色调整字段
        adapted_template = self.adapt_for_party_role(adapted_template, party.party_type)

        return adapted_template

    def adapt_for_natural_person(self, template: LegalTemplate, party: PartyInfo) -> LegalTemplate:
        """为自然人适配模板"""
        # 查找当事人信息章节
        for section in template.sections:
            if 'party_info' in section.name or party.party_type in section.name:
                # 添加自然人特有字段
                natural_person_fields = [
                    TemplateField(
                        name=f'{party.party_type}_gender',
                        field_type=FieldType.SELECTION,
                        label='性别',
                        options=['男', '女'],
                        required=False
                    ),
                    TemplateField(
                        name=f'{party.party_type}_age',
                        field_type=FieldType.NUMBER,
                        label='年龄',
                        validation_rules={'min_value': 0, 'max_value': 150},
                        required=False
                    ),
                    TemplateField(
                        name=f'{party.party_type}_id_number',
                        field_type=FieldType.TEXT,
                        label='身份证号',
                        validation_rules={'pattern': r'^\d{17}[\dXx]$|^\d{15}$'},
                        required=True
                    ),
                    TemplateField(
                        name=f'{party.party_type}_occupation',
                        field_type=FieldType.TEXT,
                        label='职业',
                        required=False
                    )
                ]

                # 移除法人特有字段
                section.fields = [f for f in section.fields if not self.is_legal_entity_field(f)]

                # 添加自然人字段
                section.fields.extend(natural_person_fields)

        return template

    def adapt_for_legal_entity(self, template: LegalTemplate, party: PartyInfo) -> LegalTemplate:
        """为法人适配模板"""
        for section in template.sections:
            if 'party_info' in section.name or party.party_type in section.name:
                # 添加法人特有字段
                legal_entity_fields = [
                    TemplateField(
                        name=f'{party.party_type}_credit_code',
                        field_type=FieldType.TEXT,
                        label='统一社会信用代码',
                        validation_rules={'pattern': r'^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$'},
                        required=True
                    ),
                    TemplateField(
                        name=f'{party.party_type}_legal_representative',
                        field_type=FieldType.TEXT,
                        label='法定代表人',
                        required=True
                    ),
                    TemplateField(
                        name=f'{party.party_type}_business_scope',
                        field_type=FieldType.TEXT,
                        label='经营范围',
                        required=False
                    ),
                    TemplateField(
                        name=f'{party.party_type}_registered_capital',
                        field_type=FieldType.AMOUNT,
                        label='注册资本',
                        required=False
                    ),
                    TemplateField(
                        name=f'{party.party_type}_establishment_date',
                        field_type=FieldType.DATE,
                        label='成立日期',
                        required=False
                    )
                ]

                # 移除自然人特有字段
                section.fields = [f for f in section.fields if not self.is_natural_person_field(f)]

                # 添加法人字段
                section.fields.extend(legal_entity_fields)

        return template

    def adapt_for_other_organization(self, template: LegalTemplate, party: PartyInfo) -> LegalTemplate:
        """为其他组织适配模板"""
        for section in template.sections:
            if 'party_info' in section.name or party.party_type in section.name:
                # 添加其他组织特有字段
                other_org_fields = [
                    TemplateField(
                        name=f'{party.party_type}_organization_code',
                        field_type=FieldType.TEXT,
                        label='组织机构代码',
                        required=False
                    ),
                    TemplateField(
                        name=f'{party.party_type}_responsible_person',
                        field_type=FieldType.TEXT,
                        label='负责人',
                        required=True
                    ),
                    TemplateField(
                        name=f'{party.party_type}_organization_type',
                        field_type=FieldType.SELECTION,
                        label='组织类型',
                        options=['个人独资企业', '合伙企业', '农民专业合作社', '社会团体', '民办非企业单位', '其他'],
                        required=True
                    )
                ]

                # 移除不适用字段
                section.fields = [f for f in section.fields
                                if not (self.is_natural_person_field(f) or self.is_legal_entity_field(f))]

                # 添加其他组织字段
                section.fields.extend(other_org_fields)

        return template

    def is_natural_person_field(self, field: TemplateField) -> bool:
        """判断是否为自然人特有字段"""
        natural_person_keywords = ['gender', 'age', 'id_number', 'occupation', 'birth_date']
        return any(keyword in field.name for keyword in natural_person_keywords)

    def is_legal_entity_field(self, field: TemplateField) -> bool:
        """判断是否为法人特有字段"""
        legal_entity_keywords = ['credit_code', 'legal_representative', 'business_scope',
                               'registered_capital', 'establishment_date']
        return any(keyword in field.name for keyword in legal_entity_keywords)

class PartyFieldValidator:
    """当事人字段验证器"""

    def __init__(self):
        self.validation_rules = self.load_validation_rules()

    def validate_party_fields(self, party: PartyInfo, field_values: Dict[str, Any]) -> ValidationResult:
        """验证当事人字段"""
        errors = []
        warnings = []

        # 根据实体类型验证
        if party.entity_type == '自然人':
            errors.extend(self.validate_natural_person_fields(field_values))
        elif party.entity_type == '法人':
            errors.extend(self.validate_legal_entity_fields(field_values))
        elif party.entity_type == '其他组织':
            errors.extend(self.validate_other_organization_fields(field_values))

        # 通用字段验证
        errors.extend(self.validate_common_fields(field_values))

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )

    def validate_natural_person_fields(self, field_values: Dict[str, Any]) -> List[str]:
        """验证自然人字段"""
        errors = []

        # 验证身份证号
        id_number = field_values.get('id_number')
        if id_number and not self.is_valid_id_number(id_number):
            errors.append("身份证号格式不正确")

        # 验证年龄
        age = field_values.get('age')
        if age is not None and (age < 0 or age > 150):
            errors.append("年龄必须在0-150之间")

        return errors

    def validate_legal_entity_fields(self, field_values: Dict[str, Any]) -> List[str]:
        """验证法人字段"""
        errors = []

        # 验证统一社会信用代码
        credit_code = field_values.get('credit_code')
        if credit_code and not self.is_valid_credit_code(credit_code):
            errors.append("统一社会信用代码格式不正确")

        # 验证注册资本
        registered_capital = field_values.get('registered_capital')
        if registered_capital is not None and registered_capital <= 0:
            errors.append("注册资本必须大于0")

        return errors

    def is_valid_id_number(self, id_number: str) -> bool:
        """验证身份证号"""
        import re
        pattern = r'^\d{17}[\dXx]$|^\d{15}$'
        return bool(re.match(pattern, id_number))

    def is_valid_credit_code(self, credit_code: str) -> bool:
        """验证统一社会信用代码"""
        import re
        pattern = r'^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$'
        return bool(re.match(pattern, credit_code))
```

## 5.4 文档生成与格式化

### 5.4.1 智能文档生成引擎

基于模板和提取的信息，智能生成标准化的法律文书。

**文档生成系统**：

```python
class DocumentGenerationEngine:
    """文档生成引擎"""

    def __init__(self):
        self.template_processor = TemplateProcessor()
        self.content_formatter = ContentFormatter()
        self.quality_checker = DocumentQualityChecker()
        self.export_manager = DocumentExportManager()

    async def generate_document(self, template: LegalTemplate, extracted_data: Dict[str, Any],
                              generation_options: Dict[str, Any] = None) -> GeneratedDocument:
        """生成文档"""
        options = generation_options or {}

        # 预处理数据
        processed_data = await self.preprocess_data(extracted_data, template)

        # 填充模板
        filled_template = await self.template_processor.fill_template(template, processed_data)

        # 格式化内容
        formatted_content = await self.content_formatter.format_content(filled_template, options)

        # 质量检查
        quality_result = await self.quality_checker.check_quality(formatted_content)

        # 生成最终文档
        document = GeneratedDocument(
            content=formatted_content,
            template_id=template.id,
            generation_time=datetime.utcnow(),
            quality_score=quality_result.score,
            metadata={
                'template_version': template.version,
                'generation_options': options,
                'data_completeness': self.calculate_data_completeness(processed_data, template)
            }
        )

        # 应用质量改进建议
        if quality_result.suggestions:
            document = await self.apply_quality_improvements(document, quality_result.suggestions)

        return document

    async def preprocess_data(self, raw_data: Dict[str, Any], template: LegalTemplate) -> Dict[str, Any]:
        """预处理数据"""
        processed_data = {}

        # 数据清洗
        cleaned_data = self.clean_data(raw_data)

        # 数据验证
        validation_result = await self.validate_data(cleaned_data, template)
        if not validation_result.is_valid:
            raise DataValidationError(validation_result.errors)

        # 数据转换
        for field in template.get_all_fields():
            field_name = field.name
            if field_name in cleaned_data:
                processed_value = await self.transform_field_value(
                    cleaned_data[field_name], field
                )
                processed_data[field_name] = processed_value
            elif field.required:
                # 尝试从其他字段推导
                inferred_value = await self.infer_field_value(field, cleaned_data)
                if inferred_value is not None:
                    processed_data[field_name] = inferred_value
                else:
                    processed_data[field_name] = field.default_value or ""

        return processed_data

    def clean_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗数据"""
        cleaned_data = {}

        for key, value in raw_data.items():
            if isinstance(value, str):
                # 清理字符串
                cleaned_value = value.strip()
                cleaned_value = re.sub(r'\s+', ' ', cleaned_value)  # 合并多个空格
                cleaned_value = re.sub(r'[^\w\s\u4e00-\u9fff\-\.\(\)（）]', '', cleaned_value)  # 移除特殊字符
                cleaned_data[key] = cleaned_value
            elif isinstance(value, (int, float)):
                cleaned_data[key] = value
            elif isinstance(value, list):
                # 清理列表
                cleaned_list = [self.clean_data({'item': item})['item'] for item in value]
                cleaned_data[key] = cleaned_list
            else:
                cleaned_data[key] = value

        return cleaned_data

    async def transform_field_value(self, value: Any, field: TemplateField) -> Any:
        """转换字段值"""
        if field.field_type == FieldType.DATE:
            return self.normalize_date(value)
        elif field.field_type == FieldType.AMOUNT:
            return self.normalize_amount(value)
        elif field.field_type == FieldType.TEXT:
            return self.normalize_text(value)
        elif field.field_type == FieldType.PARTY_INFO:
            return await self.normalize_party_info(value)
        else:
            return value

    def normalize_date(self, date_value: Any) -> str:
        """标准化日期"""
        if isinstance(date_value, str):
            # 尝试解析各种日期格式
            date_patterns = [
                r'(\d{4})年(\d{1,2})月(\d{1,2})日',
                r'(\d{4})-(\d{1,2})-(\d{1,2})',
                r'(\d{4})/(\d{1,2})/(\d{1,2})',
                r'(\d{4})\.(\d{1,2})\.(\d{1,2})'
            ]

            for pattern in date_patterns:
                match = re.search(pattern, date_value)
                if match:
                    year, month, day = match.groups()
                    return f"{year}年{int(month)}月{int(day)}日"

        return str(date_value)

    def normalize_amount(self, amount_value: Any) -> str:
        """标准化金额"""
        if isinstance(amount_value, (int, float)):
            # 格式化为中文大写金额
            return self.convert_to_chinese_amount(amount_value)
        elif isinstance(amount_value, str):
            # 提取数字
            amount_match = re.search(r'[\d,]+\.?\d*', amount_value.replace(',', ''))
            if amount_match:
                amount = float(amount_match.group())
                return self.convert_to_chinese_amount(amount)

        return str(amount_value)

    def convert_to_chinese_amount(self, amount: float) -> str:
        """转换为中文大写金额"""
        chinese_numbers = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
        chinese_units = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿']

        if amount == 0:
            return "零元整"

        # 分离整数和小数部分
        integer_part = int(amount)
        decimal_part = round((amount - integer_part) * 100)

        # 转换整数部分
        integer_chinese = self.convert_integer_to_chinese(integer_part, chinese_numbers, chinese_units)

        # 转换小数部分
        if decimal_part == 0:
            return f"{integer_chinese}元整"
        else:
            jiao = decimal_part // 10
            fen = decimal_part % 10
            decimal_chinese = ""
            if jiao > 0:
                decimal_chinese += chinese_numbers[jiao] + "角"
            if fen > 0:
                decimal_chinese += chinese_numbers[fen] + "分"

            return f"{integer_chinese}元{decimal_chinese}"

class TemplateProcessor:
    """模板处理器"""

    def __init__(self):
        self.template_engine = Jinja2Environment()
        self.conditional_processor = ConditionalProcessor()

    async def fill_template(self, template: LegalTemplate, data: Dict[str, Any]) -> Dict[str, Any]:
        """填充模板"""
        filled_sections = []

        for section in template.sections:
            filled_section = await self.fill_section(section, data)
            if filled_section:  # 只添加非空章节
                filled_sections.append(filled_section)

        return {
            'template_id': template.id,
            'template_name': template.name,
            'sections': filled_sections,
            'metadata': template.metadata
        }

    async def fill_section(self, section: TemplateSection, data: Dict[str, Any]) -> Dict[str, Any]:
        """填充章节"""
        # 检查条件逻辑
        if section.conditional_logic:
            if not self.conditional_processor.evaluate_condition(section.conditional_logic, data):
                return None  # 跳过不满足条件的章节

        filled_fields = {}
        for field in section.fields:
            field_value = data.get(field.name, field.default_value)
            if field_value is not None:
                filled_fields[field.name] = {
                    'value': field_value,
                    'label': field.label,
                    'type': field.field_type.value
                }

        # 处理内容模板
        content = ""
        if section.content_template:
            content = self.template_engine.render_string(section.content_template, data)

        return {
            'name': section.name,
            'title': section.title,
            'order': section.order,
            'fields': filled_fields,
            'content': content
        }

class ContentFormatter:
    """内容格式化器"""

    def __init__(self):
        self.format_rules = self.load_format_rules()
        self.style_manager = StyleManager()

    async def format_content(self, filled_template: Dict[str, Any], options: Dict[str, Any]) -> str:
        """格式化内容"""
        output_format = options.get('format', 'docx')

        if output_format == 'docx':
            return await self.format_as_docx(filled_template, options)
        elif output_format == 'pdf':
            return await self.format_as_pdf(filled_template, options)
        elif output_format == 'html':
            return await self.format_as_html(filled_template, options)
        else:
            raise UnsupportedFormatError(f"不支持的格式: {output_format}")

    async def format_as_docx(self, filled_template: Dict[str, Any], options: Dict[str, Any]) -> str:
        """格式化为DOCX"""
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH

        doc = Document()

        # 设置页面格式
        section = doc.sections[0]
        section.page_height = Inches(11.69)  # A4高度
        section.page_width = Inches(8.27)    # A4宽度
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)

        # 添加标题
        title = doc.add_heading('民事起诉状', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加章节内容
        for section_data in filled_template['sections']:
            # 添加章节标题
            if section_data['title']:
                heading = doc.add_heading(section_data['title'], level=1)
                heading.alignment = WD_ALIGN_PARAGRAPH.LEFT

            # 添加字段内容
            for field_name, field_data in section_data['fields'].items():
                if field_data['value']:
                    paragraph = doc.add_paragraph()
                    paragraph.add_run(f"{field_data['label']}：").bold = True
                    paragraph.add_run(str(field_data['value']))

            # 添加自定义内容
            if section_data['content']:
                doc.add_paragraph(section_data['content'])

        # 保存文档
        doc_path = f"temp/generated_document_{int(time.time())}.docx"
        doc.save(doc_path)

        return doc_path
```

通过以上全面的智能模板系统与文档处理设计，本系统实现了高度智能化、个性化的法律文书生成能力。系统不仅支持复杂的多当事人场景，还能根据不同的案件类型和当事人特征动态调整模板，确保生成的文档既符合法律规范又满足实际需求。通过机器学习技术的持续优化，系统的匹配准确率和生成质量将不断提升，为法律工作者提供更加智能、高效的文书处理服务。
```
```