#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
批量复选框处理器
负责批量处理文档中的所有复选框
"""
import json
import os
import logging
from typing import Dict, Any, List, Tuple, Optional, Union

# 导入LLM处理器
from llm.processor import LLMProcessor

logger = logging.getLogger(__name__)

class BatchCheckboxProcessor:
    """批量处理文档中的复选框"""

    def __init__(self, llm_api_url: str = None, llm_api_key: str = None, llm_model: str = 'gpt-3.5-turbo', shared_llm_processor=None):
        """
        初始化批量复选框处理器

        Args:
            llm_api_url: LLM API URL（可选，如果提供shared_llm_processor）
            llm_api_key: LLM API Key（可选，如果提供shared_llm_processor）
            llm_model: LLM 模型名称
            shared_llm_processor: 共享的LLM处理器实例
        """
        # 优先使用复选框专用的LLM配置
        self.llm_api_url = llm_api_url or os.getenv('CHECKBOX_LLM_API_URL') or os.getenv('LLM_API_URL')
        self.llm_api_key = llm_api_key or os.getenv('CHECKBOX_LLM_API_KEY') or os.getenv('LLM_API_KEY')
        self.llm_model = llm_model or os.getenv('CHECKBOX_LLM_MODEL') or os.getenv('LLM_MODEL')

        # 获取复选框专用的LLM参数
        checkbox_temp = os.getenv('CHECKBOX_LLM_TEMPERATURE')
        if checkbox_temp:
            self.llm_temperature = float(checkbox_temp)
        else:
            self.llm_temperature = float(os.getenv('LLM_TEMPERATURE', '0.3'))

        checkbox_max_tokens = os.getenv('CHECKBOX_LLM_MAX_TOKENS')
        if checkbox_max_tokens:
            self.llm_max_tokens = int(checkbox_max_tokens)
        else:
            self.llm_max_tokens = int(os.getenv('LLM_MAX_TOKENS', '1000'))

        checkbox_top_p = os.getenv('CHECKBOX_LLM_TOP_P')
        if checkbox_top_p:
            self.llm_top_p = float(checkbox_top_p)
        else:
            self.llm_top_p = float(os.getenv('LLM_TOP_P', '0.9'))

        # 优先使用共享的LLM处理器实例
        if shared_llm_processor:
            print("BatchCheckboxProcessor: 使用共享的LLM处理器实例")
            self.llm_processor = shared_llm_processor
        elif self.llm_api_url and self.llm_api_key:
            print("BatchCheckboxProcessor: 创建新的LLM处理器实例")
            print(f"  - API URL: {self.llm_api_url}")
            print(f"  - Model: {self.llm_model}")
            print(f"  - Temperature: {self.llm_temperature}")
            print(f"  - Max Tokens: {self.llm_max_tokens}")
            print(f"  - Top P: {self.llm_top_p}")

            self.llm_processor = LLMProcessor(
                api_url=self.llm_api_url,
                api_key=self.llm_api_key,
                model=self.llm_model
            )
            print("LLM处理器初始化成功")
        else:
            self.llm_processor = None
            logger.warning("LLM API URL或API Key未配置，LLM处理器将不可用")

    def process_document(self, doc, case_data: Dict[str, Any]) -> Dict[str, List[int]]:
        """
        批量处理整个文档中的所有复选框

        Args:
            doc: Word文档对象
            case_data: 案件数据

        Returns:
            处理结果字典，键为行标识符，值为应勾选的选项索引列表
        """
        if not self.llm_api_url or not self.llm_api_key:
            logger.warning("LLM API未配置，无法处理复选框")
            return {}

        try:
            # 1. 收集文档中所有的复选框行
            checkbox_rows = self._collect_document_checkboxes(doc)

            if not checkbox_rows or len(checkbox_rows) == 0:
                print("文档中未找到复选框行")
                return {}

            print(f"找到 {len(checkbox_rows)} 行包含复选框")

            # 2. 构建批量处理提示
            batch_prompt = self._build_batch_prompt(checkbox_rows, case_data)

            # 3. 调用LLM API
            response = self._call_llm_api(batch_prompt)

            # 4. 解析响应
            try:
                results = json.loads(response)
                print(f"批量处理结果: {results}")
                return results
            except json.JSONDecodeError:
                logger.error(f"无法解析响应为JSON: {response}")
                return {}

        except Exception as e:
            logger.error(f"批量处理文档复选框时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return {}

    def _collect_document_checkboxes(self, doc) -> List[Dict]:
        """
        收集文档中所有包含复选框的行

        Args:
            doc: Word文档对象

        Returns:
            包含复选框的行列表，每行包含行文本和复选框信息
        """
        checkbox_rows = []
        row_id = 1

        # 处理段落中的复选框
        for para_idx, paragraph in enumerate(doc.paragraphs):
            # 检查段落是否包含复选框字符
            if '□' in paragraph.text:
                row = {
                    "id": f"行{row_id}",
                    "type": "paragraph",
                    "index": para_idx,
                    "text": paragraph.text,
                    "checkboxes": []
                }

                # 分析段落中的复选框位置
                checkbox_positions = [i for i, char in enumerate(paragraph.text) if char == '□']

                for pos_idx, pos in enumerate(checkbox_positions):
                    # 提取复选框前后文本
                    text_before = paragraph.text[:pos]
                    text_after = paragraph.text[pos+1:]

                    # 创建复选框信息
                    checkbox = {
                        "position": pos_idx + 1,  # 从1开始计数
                        "text_before": text_before,
                        "text_after": text_after
                    }

                    row["checkboxes"].append(checkbox)

                # 只有包含复选框的行才添加到列表中
                if row["checkboxes"]:
                    checkbox_rows.append(row)
                    row_id += 1

        # 处理表格中的复选框
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                # 获取行文本
                row_text = " ".join([cell.text for cell in row.cells])

                # 检查行是否包含复选框字符
                if '□' in row_text:
                    # 获取第一列文本作为行标识
                    first_column_text = row.cells[0].text if len(row.cells) > 0 else ""

                    table_row = {
                        "id": f"行{row_id}",
                        "type": "table",
                        "table_index": table_idx,
                        "row_index": row_idx,
                        "text": row_text,
                        "first_column": first_column_text,
                        "checkboxes": []
                    }

                    # 遍历单元格
                    for cell_idx, cell in enumerate(row.cells):
                        if '□' in cell.text:
                            # 分析单元格中的复选框位置
                            checkbox_positions = [i for i, char in enumerate(cell.text) if char == '□']

                            for pos_idx, pos in enumerate(checkbox_positions):
                                # 提取复选框前后文本
                                text_before = cell.text[:pos]
                                text_after = cell.text[pos+1:]

                                # 创建复选框信息
                                checkbox = {
                                    "position": pos_idx + 1,  # 从1开始计数
                                    "cell_index": cell_idx,
                                    "text_before": text_before,
                                    "text_after": text_after
                                }

                                table_row["checkboxes"].append(checkbox)

                    # 只有包含复选框的行才添加到列表中
                    if table_row["checkboxes"]:
                        checkbox_rows.append(table_row)
                        row_id += 1

        return checkbox_rows

    def _build_batch_prompt(self, checkbox_rows: List[Dict], case_data: Dict[str, Any]) -> str:
        """
        构建批量处理复选框的提示

        Args:
            checkbox_rows: 包含复选框的行列表
            case_data: 案件数据

        Returns:
            批量处理提示
        """
        # 提取案件信息
        case_type = case_data.get("case_type", "")
        # 兼容中文和英文字段名
        plaintiff_info = case_data.get("plaintiff", case_data.get("原告信息", {}))
        defendant_info = case_data.get("defendant", case_data.get("被告信息", {}))

        # 构建案件信息部分
        case_info = f"""
案件类型: {case_type}
原告信息: {json.dumps(plaintiff_info, ensure_ascii=False)}
被告信息: {json.dumps(defendant_info, ensure_ascii=False)}
        """

        # 添加更多案件详情（如果存在）
        for key, value in case_data.items():
            if key not in ["case_type", "plaintiff", "原告信息", "defendant", "被告信息"]:
                # 跳过过大的数据字段
                if isinstance(value, str) and len(value) > 1000:
                    value = value[:1000] + "...(已截断)"
                elif isinstance(value, dict) or isinstance(value, list):
                    value = str(value)[:1000] + "...(已截断)" if len(str(value)) > 1000 else value
                case_info += f"\n{key}: {value}"

        # 构建需要判断的复选框行部分
        checkbox_rows_info = ""
        for row in checkbox_rows:
            row_id = row["id"]
            row_text = row["text"]
            first_column = row.get("first_column", "")

            # 添加行信息
            if first_column and first_column != row_text:
                checkbox_rows_info += f"\n{row_id}. [{first_column}] {row_text}"
            else:
                checkbox_rows_info += f"\n{row_id}. {row_text}"

        # 完整提示
        prompt = f"""
你是一个法律文书处理助手，现在需要你帮助决定是否勾选文档中的复选框。

请根据以下信息，判断每个复选框是否应该被勾选:

## 案件完整信息（整体上下文）
{case_info}

## 需要判断的复选框行
{checkbox_rows_info}

## 判断指南
1. 你需要基于案件信息判断每一行中哪些复选框应该被勾选
2. 对于每一行，只返回应该勾选的选项序号（从1开始）
3. 如果某行没有应该勾选的选项，返回空列表
4. 注意互斥选项（如"男/女"、"是/否"）不应同时勾选

请以JSON格式返回结果，格式为：
{{
  "行1": [应勾选的选项序号列表],
  "行2": [应勾选的选项序号列表],
  ...
}}

例如：
{{
  "行1": [1],
  "行2": [2]
}}
表示第1行应勾选第1个选项，第2行应勾选第2个选项。

请只返回JSON格式的结果，不要有其他解释。
        """

        return prompt

    def _call_llm_api(self, prompt: str) -> str:
        """
        调用LLM API

        Args:
            prompt: 提示文本

        Returns:
            LLM响应文本
        """
        if not self.llm_processor:
            logger.error("LLM处理器未初始化，无法调用API")
            return "{}"

        try:
            # 使用复选框专用的LLM参数调用API
            messages = [
                {"role": "system", "content": "你是一个法律文书处理助手，擅长分析复选框选择。"},
                {"role": "user", "content": prompt}
            ]

            print(f"BatchCheckboxProcessor: 调用LLM API")
            print(f"  - Temperature: {self.llm_temperature}")
            print(f"  - Max Tokens: {self.llm_max_tokens}")
            print(f"  - Top P: {self.llm_top_p}")

            response = self.llm_processor.call_api(
                messages=messages,
                temperature=self.llm_temperature,
                max_tokens=self.llm_max_tokens,
                top_p=self.llm_top_p
            )
            return response
        except Exception as e:
            logger.error(f"调用LLM API时出错: {str(e)}")
            return "{}"

    def apply_results_to_document(self, doc, results: Dict[str, List[int]]) -> None:
        """
        将批量处理结果应用到文档中

        Args:
            doc: Word文档对象
            results: 批量处理结果
        """
        if not results:
            logger.warning("没有处理结果可应用")
            return

        try:
            # 重新收集文档中的复选框行
            checkbox_rows = self._collect_document_checkboxes(doc)

            # 遍历每一行复选框
            for row in checkbox_rows:
                row_id = row["id"]
                if row_id in results:
                    # 获取应勾选的选项序号
                    checked_positions = results[row_id]

                    # 应用勾选结果
                    if row["type"] == "paragraph":
                        self._apply_to_paragraph(doc, row, checked_positions)
                    elif row["type"] == "table":
                        self._apply_to_table(doc, row, checked_positions)

            print("批量处理结果已应用到文档")

        except Exception as e:
            logger.error(f"应用批量处理结果时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def _apply_to_paragraph(self, doc, row: Dict, checked_positions: List[int]) -> None:
        """
        将勾选结果应用到段落中

        Args:
            doc: Word文档对象
            row: 行信息
            checked_positions: 应勾选的选项序号列表
        """
        try:
            paragraph = doc.paragraphs[row["index"]]
            text = paragraph.text

            # 获取复选框位置
            checkbox_positions = [i for i, char in enumerate(text) if char == '□']

            # 创建新文本，替换应勾选的复选框
            new_text = list(text)
            for pos_idx, pos in enumerate(checkbox_positions):
                position = pos_idx + 1  # 从1开始计数
                if position in checked_positions:
                    # 替换为勾选状态
                    new_text[pos] = '☑'

            # 更新段落文本
            paragraph.text = ''.join(new_text)

        except Exception as e:
            logger.error(f"应用勾选结果到段落时出错: {str(e)}")

    def _apply_to_table(self, doc, row: Dict, checked_positions: List[int]) -> None:
        """
        将勾选结果应用到表格中

        Args:
            doc: Word文档对象
            row: 行信息
            checked_positions: 应勾选的选项序号列表
        """
        try:
            table = doc.tables[row["table_index"]]
            table_row = table.rows[row["row_index"]]

            # 遍历单元格
            for cell_idx, cell in enumerate(table_row.cells):
                if '□' in cell.text:
                    text = cell.text

                    # 获取单元格中的复选框位置
                    checkbox_positions = [i for i, char in enumerate(text) if char == '□']

                    # 创建新文本，替换应勾选的复选框
                    new_text = list(text)
                    for pos_idx, pos in enumerate(checkbox_positions):
                        # 查找对应的复选框在行中的位置
                        for checkbox in row["checkboxes"]:
                            if checkbox.get("cell_index") == cell_idx and checkbox.get("position") in checked_positions:
                                # 替换为勾选状态
                                new_text[pos] = '☑'
                                break

                    # 更新单元格文本
                    cell.text = ''.join(new_text)

        except Exception as e:
            logger.error(f"应用勾选结果到表格时出错: {str(e)}")
