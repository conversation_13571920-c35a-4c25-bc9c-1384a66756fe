#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
复选框处理常量定义
统一管理复选框相关的字符和配置，避免编码问题
"""

# 核心复选框字符集合
# 优先使用编码安全的字符，避免复制粘贴时出现编码问题
CHECKBOX_CHARS = [
    "☐",    # U+2610 - 投票箱（标准未选中复选框，相对安全）
    "☑",    # U+2611 - 带勾的投票箱（标准选中复选框，相对安全）
    #"☒",    # U+2612 - 带X的投票箱（相对安全）
    "■",    # U+25A0 - 黑色正方形（填充的复选框）
    #"○",    # U+25CB - 白色圆圈（更安全的选择）
    #"●",    # U+25CF - 黑色圆圈（更安全的选择）
    #"✓",    # U+2713 - 勾号
    #"✔",    # U+2714 - 粗勾号
    #"×",    # U+00D7 - 乘号（相对安全）
    "□",    # U+25A1 - 白色正方形（可能有编码问题，放在最后）
]

# 选中状态的复选框字符
CHECKED_CHARS = ["☑", "■"]

# 未选中状态的复选框字符
UNCHECKED_CHARS = ["☐", "□"]

# 取消选中状态的复选框字符
CANCELLED_CHARS = ["☐", "□"]

# 默认的复选框字符（使用编码更安全的字符）
DEFAULT_UNCHECKED = "☐"  # 使用投票箱而不是白色正方形
DEFAULT_CHECKED = "☑"

def is_checkbox_char(char: str) -> bool:
    """
    检查字符是否为复选框字符
    
    Args:
        char (str): 要检查的字符
        
    Returns:
        bool: 是否为复选框字符
    """
    return char in CHECKBOX_CHARS

def is_checked_char(char: str) -> bool:
    """
    检查字符是否表示选中状态
    
    Args:
        char (str): 要检查的字符
        
    Returns:
        bool: 是否表示选中状态
    """
    return char in CHECKED_CHARS

def is_unchecked_char(char: str) -> bool:
    """
    检查字符是否表示未选中状态
    
    Args:
        char (str): 要检查的字符
        
    Returns:
        bool: 是否表示未选中状态
    """
    return char in UNCHECKED_CHARS

def normalize_checkbox_char(char: str, checked: bool = False) -> str:
    """
    标准化复选框字符
    
    Args:
        char (str): 原始字符
        checked (bool): 是否应该为选中状态
        
    Returns:
        str: 标准化后的字符
    """
    if not is_checkbox_char(char):
        return char
    
    return DEFAULT_CHECKED if checked else DEFAULT_UNCHECKED

def get_checkbox_state(char: str) -> str:
    """
    获取复选框字符的状态

    Args:
        char (str): 复选框字符

    Returns:
        str: 状态 ('checked', 'unchecked', 'cancelled', 'unknown')
    """
    if is_checked_char(char):
        return 'checked'
    elif is_unchecked_char(char):
        return 'unchecked'
    elif char in CANCELLED_CHARS:
        return 'cancelled'
    else:
        return 'unknown'

def get_safe_checkbox_char(char: str, checked: bool = False) -> str:
    """
    获取编码安全的复选框字符

    Args:
        char (str): 原始字符
        checked (bool): 是否应该为选中状态

    Returns:
        str: 编码安全的复选框字符
    """
    # 问题字符映射表
    PROBLEMATIC_CHARS = {
        "□": "☐",  # 白色正方形 -> 投票箱（更安全）
    }

    # 如果是问题字符，替换为安全字符
    if char in PROBLEMATIC_CHARS:
        safe_char = PROBLEMATIC_CHARS[char]
        print(f"⚠️  替换可能有编码问题的字符: '{char}' -> '{safe_char}'")
        return get_safe_checkbox_char(safe_char, checked)

    # 根据状态返回适当的字符
    if checked:
        return DEFAULT_CHECKED
    else:
        return char if char in CHECKBOX_CHARS else DEFAULT_UNCHECKED
