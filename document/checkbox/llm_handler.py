"""
LLM复选框处理器
负责使用大语言模型进行复选框判断
"""
import json
import os
import logging
import requests
import re
import concurrent.futures
import threading
from typing import Dict, Any, List, Tuple, Optional, Union
from dotenv import load_dotenv
from config import Config

# 导入LLMProcessor
from llm.processor import LLMProcessor
from llm.prompts.checkbox_prompts import get_checkbox_system_prompt, get_single_row_user_prompt, get_combined_user_prompt

# 加载.env文件中的环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class LLMCheckboxHandler:
    """基于大语言模型的复选框处理器"""

    def __init__(self, case_data: Dict[str, Any], config: Dict[str, Any]):
        """
        初始化LLM复选框处理器

        Args:
            case_data: 案件数据
            config: 配置信息，包含LLM API相关配置
        """
        self.case_data = case_data
        self.config = config
        self.last_row_context = None
        self.last_checkboxes_response = None

        # 优先从环境变量获取复选框专用LLM设置，如果没有则使用通用LLM设置，最后从配置中获取
        self.llm_api_url = os.getenv('CHECKBOX_LLM_API_URL') or os.getenv('LLM_API_URL') or config.get('llm_api_url', '')
        self.llm_api_key = os.getenv('CHECKBOX_LLM_API_KEY') or os.getenv('LLM_API_KEY') or config.get('llm_api_key', '')
        self.llm_model = os.getenv('CHECKBOX_LLM_MODEL') or os.getenv('LLM_MODEL') or config.get('llm_model', 'gpt-3.5-turbo')

        # 复选框专用参数处理
        checkbox_temp = os.getenv('CHECKBOX_LLM_TEMPERATURE')
        if checkbox_temp:
            self.llm_temperature = float(checkbox_temp)
        elif os.getenv('LLM_TEMPERATURE'):
            self.llm_temperature = float(os.getenv('LLM_TEMPERATURE'))
        else:
            self.llm_temperature = config.get('llm_temperature', 0.0)

        checkbox_top_p = os.getenv('CHECKBOX_LLM_TOP_P')
        if checkbox_top_p:
            self.llm_top_p = float(checkbox_top_p)
        elif os.getenv('LLM_TOP_P'):
            self.llm_top_p = float(os.getenv('LLM_TOP_P'))
        else:
            self.llm_top_p = config.get('llm_top_p', 0.0)

        checkbox_max_tokens = os.getenv('CHECKBOX_LLM_MAX_TOKENS')
        if checkbox_max_tokens:
            self.llm_max_tokens = int(checkbox_max_tokens)
        elif os.getenv('LLM_MAX_TOKENS'):
            self.llm_max_tokens = int(os.getenv('LLM_MAX_TOKENS'))
        else:
            self.llm_max_tokens = config.get('llm_max_tokens', 100)

        # 优先使用共享的LLM处理器实例，避免重复初始化
        self.llm_processor = config.get('shared_llm_processor')

        if self.llm_processor:
            print("LLMCheckboxHandler: 使用共享的LLM处理器实例")
            # 记录最后一次API调用的响应，用于调试
            self.last_api_response = None
            self.last_checkbox_response = None
        elif self.llm_api_url and self.llm_api_key:
            print(f"LLMCheckboxHandler: 创建新的LLM处理器实例")
            print(f"  - API URL: {self.llm_api_url}")
            print(f"  - Model: {self.llm_model}")
            print(f"  - Temperature: {self.llm_temperature}")
            print(f"  - Max Tokens: {self.llm_max_tokens}")
            print(f"  - Top P: {self.llm_top_p}")

            self.llm_processor = LLMProcessor(
                api_url=self.llm_api_url,
                api_key=self.llm_api_key,
                model=self.llm_model
            )

            # 记录最后一次API调用的响应，用于调试
            self.last_api_response = None
            self.last_checkbox_response = None
        else:
            # 如果没有配置API URL或API Key，则无法初始化LLM处理器
            self.llm_processor = None
            logger.warning("LLM API URL或API Key未配置，LLM处理器将不可用")

        # 检查是否启用复选框合并处理
        self.combine_checkboxes = self._get_combine_checkboxes_setting()

        # 获取复选框合并策略
        self.combine_checkboxes_by = self._get_combine_checkboxes_by_setting()

        # 获取批量处理配置
        self.process_checkbox_batch = self._get_process_checkbox_batch_setting()

    def _get_combine_checkboxes_setting(self) -> bool:
        """
        获取复选框合并处理设置

        Returns:
            bool: 是否启用复选框合并处理
        """
        # 优先从环境变量获取
        env_setting = os.getenv('COMBINE_CHECKBOXES', '').lower()
        if env_setting in ('true', 'yes', '1', 'on'):
            return True
        elif env_setting in ('false', 'no', '0', 'off'):
            return False

        # 从配置中获取
        return self.config.get('combine_checkboxes', False)

    def _get_combine_checkboxes_by_setting(self) -> str:
        """
        获取复选框合并策略设置

        Returns:
            str: 合并策略，'fulltext' 或 'table'
        """
        # 优先从环境变量获取
        env_setting = os.getenv('COMBINE_CHECKBOXES_BY', '').lower()
        if env_setting in ('fulltext', 'table'):
            return env_setting

        # 从配置中获取
        config_setting = self.config.get('combine_checkboxes_by', 'fulltext').lower()
        return config_setting if config_setting in ('fulltext', 'table') else 'fulltext'

    def _get_process_checkbox_batch_setting(self) -> bool:
        """
        获取复选框批量处理设置

        Returns:
            bool: 是否启用批量并发调用LLM API
        """
        # 优先从环境变量获取
        env_setting = os.getenv('PROCESS_CHECKBOX_BATCH', '').lower()
        if env_setting in ('true', 'yes', '1', 'on'):
            return True
        elif env_setting in ('false', 'no', '0', 'off'):
            return False

        # 从配置中获取
        return self.config.get('process_checkbox_batch', False)

    def _remove_thinking_tags(self, content):
        """
        移除LLM响应中的thinking标签

        Args:
            content (str): LLM响应内容

        Returns:
            str: 移除thinking标签后的内容
        """
        try:
            # 检查是否需要保留thinking标签
            try:
                from flask import current_app
                include_thinking = current_app.config.get('INCLUDE_THINKING_TAG', False)
            except:
                # 如果不在Flask环境中，默认为False
                include_thinking = False

            # 如果配置为保留thinking标签，直接返回原内容
            if include_thinking:
                return content

            # 使用正则表达式移除thinking标签及其内容
            # 匹配 <thinking>...</thinking> 和 <think>...</think> 标签（支持多行）
            cleaned_content = re.sub(r'<think(?:ing)?>.*?</think(?:ing)?>', '', content, flags=re.DOTALL | re.IGNORECASE)

            # 清理可能留下的多余空白行
            cleaned_content = re.sub(r'\n\s*\n', '\n', cleaned_content)
            cleaned_content = cleaned_content.strip()

            # 如果内容被清理了，记录日志
            if cleaned_content != content:
                logger.info("已移除LLM响应中的thinking标签")

            return cleaned_content

        except Exception as e:
            logger.warning(f"移除thinking标签时出错: {e}")
            return content

    def process_row_checkboxes(self, row_context: Dict = None, checkboxes_context: List[Dict] = None, paragraph_text: str = None, table_context: Optional[Dict] = None) -> List[bool]:
        """
        使用LLM处理复选框，支持单行处理和多行分组处理

        此方法已简化，不再依赖复杂的复选框上下文，只需要行文本和复选框数量

        Args:
            row_context: 行上下文，包含完整行内容和复选框数量
            checkboxes_context: 复选框上下文列表（可选，为了兼容旧代码）
            paragraph_text: 段落文本（如果没有提供row_context）
            table_context: 表格上下文（可选）

        Returns:
            布尔值列表，表示每个复选框是否应该被勾选
        """
        if not self.llm_api_url or not self.llm_api_key:
            logger.warning("LLM API未配置，无法处理复选框")
            return [False] * (row_context.get('total_checkboxes', 0) if row_context else 0)

        try:
            # 简化的处理逻辑：只需要行上下文或段落文本
            if row_context is not None:
                # 使用行上下文处理
                return self._process_single_row_simplified(row_context, paragraph_text)
            elif paragraph_text:
                # 使用段落文本处理
                return self._process_text_only(paragraph_text, table_context)
        except Exception as e:
            error_msg = f"处理复选框时出错: {str(e)}"
            logger.error(error_msg)
            # 检查是否是LLM API相关错误，如果是则重新抛出以便上层处理
            if "LLM API请求失败" in str(e) or "timeout" in str(e).lower() or "connection" in str(e).lower():
                raise Exception(f"LLM API调用失败: {str(e)}")
            return [False] * (row_context.get('total_checkboxes', 0) if row_context else 0)

    def _process_single_row_simplified(self, row_context: Dict, paragraph_text: str = None) -> List[bool]:
        """简化的单行处理方法，只依赖行上下文和复选框数量"""
        try:
            # 获取关键信息
            row_text = row_context.get('row_text', '')
            first_column = row_context.get('first_column', '')
            full_text = row_context.get('full_text', paragraph_text or row_text)

            # 获取复选框数量
            total_checkboxes = row_context.get('total_checkboxes', 0)

            if total_checkboxes <= 0:
                logger.warning("复选框数量为0，无法处理")
                return []

            print(f"\n处理表格行: 复选框数量={total_checkboxes}")

            # 构建分离的提示并调用LLM
            system_prompt, user_prompt = self._build_prompt_for_row_checkboxes_simplified(row_context)
            response = self._call_llm_api(user_prompt, system_prompt)

            # 解析响应
            results = self._parse_checkboxes_response(response, total_checkboxes)

            # 打印解析结果
            print(f"LLM返回 {len(results)} 个结果，期望的复选框数量: {total_checkboxes}")

            # 处理结果数量不匹配的情况
            if len(results) != total_checkboxes:
                print(f"警告: LLM返回的结果数量({len(results)})与期望的复选框数量({total_checkboxes})不匹配")

                # 如果结果数量大于期望数量，截断结果
                if len(results) > total_checkboxes:
                    results = results[:total_checkboxes]
                # 如果结果数量小于期望数量，扩展结果
                else:
                    results.extend([False] * (total_checkboxes - len(results)))

            print(f"处理结果: {results}")
            return results

        except Exception as e:
            print(f"处理错误: {str(e)}")
            return [False] * row_context.get('total_checkboxes', 0)

    def _build_prompt_for_row_checkboxes_simplified(self, row_context: Dict) -> tuple:
        """构建简化的行处理提示，返回分离的system prompt和user prompt"""
        ocr_original_text = self.config.get('ocr_original_text', '')
        logger.info(f"[整行复选框] OCR原始文本检查: 存在={bool(ocr_original_text)}, 长度={len(ocr_original_text.strip() if ocr_original_text else '')}, 前50个字符={ocr_original_text[:50] if ocr_original_text else ''}")

        # 案件信息部分 - 处理后使用OCR原始文本或结构化数据（互斥）
        if ocr_original_text and len(ocr_original_text.strip()) > 0:
            logger.info("[整行复选框] 使用OCR原始文本作为案件完整信息")
            # 清理OCR文本，处理多余的换行和空白字符
            # 将连续的换行替换为单个换行
            cleaned_ocr_text = re.sub(r'\n{2,}', '\n', ocr_original_text)
            # 将连续的空格替换为单个空格
            cleaned_ocr_text = re.sub(r' {2,}', ' ', cleaned_ocr_text)
            # 去除行首和行尾的空白字符
            cleaned_ocr_text = '\n'.join([line.strip() for line in cleaned_ocr_text.split('\n')])

            # 如果OCR文本过长，只保留前8000个字符
            max_text_length = 8000
            if len(cleaned_ocr_text) > max_text_length:
                case_info = f"""
{cleaned_ocr_text[:max_text_length]}...(省略后续内容)
                """
            else:
                case_info = f"""
{cleaned_ocr_text}
                """
        else:
            # OCR原始文本不存在，跳过案件信息
            logger.info("[整行复选框] OCR原始文本不存在，跳过案件信息")
            case_info = "（无OCR原始文本，请根据复选框选项内容进行判断）"
        # 获取行信息
        row_text = row_context.get('row_text', '')
        first_column = row_context.get('first_column', '')
        # 确保第一列内容正确
        if not first_column and row_text:
            # 尝试从行内容中提取第一列
            parts = row_text.split(maxsplit=1)
            if len(parts) > 0:
                # 如果行内容包含第一列，则分离出来
                first_column = parts[0]
                if len(parts) > 1:
                    row_text = parts[1]
                print(f"从行内容中提取第一列: '{first_column}', 剩余行内容: '{row_text}'")

        total_checkboxes = row_context.get('total_checkboxes', 0)

        # 使用checkbox_prompts中的函数构建分离的提示词
        system_prompt = get_checkbox_system_prompt(case_info)
        user_prompt = get_single_row_user_prompt(first_column, row_text, total_checkboxes)

        return system_prompt, user_prompt

    def _process_text_only(self, text: str, table_context: Optional[Dict] = None) -> List[bool]:
        """只使用文本处理复选框，不依赖复选框上下文"""
        try:
            # 使用统一的复选框字符定义
            from .constants import CHECKBOX_CHARS
            checkbox_chars = CHECKBOX_CHARS
            total_checkboxes = sum(text.count(char) for char in checkbox_chars)

            if total_checkboxes <= 0:
                logger.warning("文本中没有复选框字符，无法处理")
                return []

            # 构建简单的行上下文
            row_context = {
                'row_text': text,
                'total_checkboxes': total_checkboxes
            }

            # 如果有表格上下文，添加到行上下文
            if table_context:
                row_context.update({
                    'first_column': table_context.get('first_column', ''),
                    'full_text': table_context.get('full_text', text)
                })

            # 使用简化的行处理方法
            return self._process_single_row_simplified(row_context)
        except Exception as e:
            logger.error(f"处理文本复选框时出错: {str(e)}")
            return []

    def _call_llm_api(self, prompt: str, system_prompt: str = None) -> str:
        """
        调用LLM API

        Args:
            prompt: 用户提示文本
            system_prompt: 系统提示文本（可选）

        Returns:
            LLM的响应文本
        """
        # 打印完整提示词
        #print("\n" + "="*50)
        #print("发送给LLM的完整提示词:")
        #print("="*50)
        #if system_prompt:
        #    print("=== System Prompt ===")
        #    print(system_prompt)
        #    print("\n=== User Prompt ===")
        #print(prompt)
        #print("="*50 + "\n")

        # 如果有LLM处理器实例，优先使用它
        if self.llm_processor:
            print(self.llm_top_p, self.llm_temperature, self.llm_max_tokens)
            try:
                # 构造消息
                messages = []
                if system_prompt:
                    messages.append({"role": "system", "content": system_prompt})
                messages.append({"role": "user", "content": prompt})

                # 调用LLM处理器的API
                response = self.llm_processor.call_api(
                    messages=messages,
                    temperature=self.llm_temperature,
                    top_p=self.llm_top_p,
                    max_tokens=self.llm_max_tokens
                )

                # 打印LLM的原始响应内容
                print("\n" + "="*50)
                print("LLM的原始响应内容:")
                print("="*50)
                print(response)
                print("="*50 + "\n")

                # 返回响应内容
                return response
            except Exception as e:
                print(f"使用LLM处理器调用API失败: {str(e)}")
                # 检查是否是超时或连接错误，如果是则直接抛出，不再回退
                if "timeout" in str(e).lower() or "connection" in str(e).lower() or "timed out" in str(e).lower():
                    raise Exception(f"LLM API请求失败: {str(e)}")
                # 其他错误才回退到直接调用API

        # 直接调用API的回退方案
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.llm_api_key}"
        }

        # 构造消息列表
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})

        payload = {
            "model": self.llm_model,
            "messages": messages,
            "temperature": self.llm_temperature,
            "top_p": self.llm_top_p,
            "max_tokens": self.llm_max_tokens,
            "stream": False
        }

        # 如果启用了阿里云思维链功能，添加enable_thinking字段
        try:
            from flask import current_app
            enable_ali_thinking = current_app.config.get('LLM_ENABLE_ALI_THINKING', False)
            if enable_ali_thinking:
                payload["enable_thinking"] = True
        except:
            # 如果不在Flask环境中，从环境变量获取
            import os
            enable_ali_thinking = os.environ.get('LLM_ENABLE_ALI_THINKING', 'false').lower() == 'true'
            if enable_ali_thinking:
                payload["enable_thinking"] = True

        try:
            # 打印LLM API调用信息
            api_url = f"{self.llm_api_url.rstrip('/')}/chat/completions"
            logger.info(f"调用LLM API: {api_url}")
            logger.info(f"LLM API参数: model={self.llm_model}, temperature={self.llm_temperature}, max_tokens={self.llm_max_tokens}, top_p={self.llm_top_p}")

            # 打印完整的请求体
            print("\n" + "="*50)
            print("LLM API 请求体:")
            print("="*50)
            print(f"URL: {api_url}")
            print(f"Headers: {headers}")
            print(f"Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
            print("="*50 + "\n")

            response = requests.post(api_url, headers=headers, json=payload)
            response.raise_for_status()

            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                response_content = result["choices"][0]["message"]["content"].strip()

                # 移除thinking标签
                response_content = self._remove_thinking_tags(response_content)

                # 打印LLM的原始响应内容
                print("\n" + "="*50)
                print("LLM的原始响应内容:")
                print("="*50)
                print(response_content)
                print("="*50 + "\n")

                return response_content
            else:
                print(f"LLM API返回的响应格式不正确: {result}")
                raise Exception("LLM API返回的响应格式不正确")

        except requests.exceptions.RequestException as e:
            print(f"LLM API请求失败: {str(e)}")
            raise Exception(f"LLM API请求失败: {str(e)}")

    def _call_llm_api_batch(self, prompts: List[str]) -> List[str]:
        """
        批量调用LLM API

        Args:
            prompts: 提示文本列表

        Returns:
            LLM的响应文本列表
        """
        # 打印完整提示词
        #for i, prompt in enumerate(prompts):
        #    print(f"\n{'='*50}")
        #    print(f"发送给LLM的完整提示词 #{i+1}:")
        #    print(f"{'='*50}")
        #    print(prompt)
        #    print(f"{'='*50}\n")

        # 如果有LLM处理器实例，优先使用它
        if self.llm_processor:
            try:
                # 构造消息
                messages = [{"role": "user", "content": prompt} for prompt in prompts]

                # 调用LLM处理器的API
                responses = self.llm_processor.call_api_batch(
                    messages=messages,
                    temperature=self.llm_temperature,
                    max_tokens=self.llm_max_tokens
                )

                # 返回响应内容
                return responses
            except Exception as e:
                print(f"使用LLM处理器批量调用API失败: {str(e)}")
                # 检查是否是超时或连接错误，如果是则直接抛出，不再回退
                if "timeout" in str(e).lower() or "connection" in str(e).lower() or "timed out" in str(e).lower():
                    raise Exception(f"LLM API请求失败: {str(e)}")
                # 其他错误才回退到直接调用API

        # 直接调用API的回退方案
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.llm_api_key}"
        }

        payloads = []
        for prompt in prompts:
            payload = {
                "model": self.llm_model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": self.llm_temperature,
                "max_tokens": self.llm_max_tokens,
                "stream": False
            }

            # 如果启用了阿里云思维链功能，添加enable_thinking字段
            try:
                from flask import current_app
                enable_ali_thinking = current_app.config.get('LLM_ENABLE_ALI_THINKING', False)
                if enable_ali_thinking:
                    payload["enable_thinking"] = True
            except:
                # 如果不在Flask环境中，从环境变量获取
                import os
                enable_ali_thinking = os.environ.get('LLM_ENABLE_ALI_THINKING', 'false').lower() == 'true'
                if enable_ali_thinking:
                    payload["enable_thinking"] = True

            payloads.append(payload)

        try:
            # 确保API URL是完整的
            api_url = self.llm_api_url
            if not api_url.endswith('/chat/completions'):
                api_url = f"{api_url.rstrip('/')}/chat/completions"

            # 打印LLM API调用信息
            logger.info(f"批量调用LLM API: {api_url}")
            logger.info(f"LLM API参数: model={self.llm_model}, temperature={self.llm_temperature}, max_tokens={self.llm_max_tokens}")
            logger.info(f"批量请求数量: {len(payloads)}")

            # 打印所有请求体
            print("\n" + "="*50)
            print("LLM API 批量请求体:")
            print("="*50)
            print(f"URL: {api_url}")
            print(f"Headers: {headers}")
            for i, payload in enumerate(payloads):
                print(f"\n--- 请求 #{i+1} ---")
                print(json.dumps(payload, ensure_ascii=False, indent=2))
            print("="*50 + "\n")

            # 使用并发方式发送请求
            print(f"🚀 开始并发发送 {len(payloads)} 个LLM API请求")

            responses = [None] * len(payloads)

            def send_single_request(index, payload):
                """发送单个API请求的包装函数"""
                try:
                    print(f"[线程 {threading.current_thread().name}] 开始发送请求 #{index+1}")
                    response = requests.post(api_url, headers=headers, json=payload, timeout=60)
                    response.raise_for_status()

                    result = response.json()
                    if "choices" in result and len(result["choices"]) > 0:
                        response_content = result["choices"][0]["message"]["content"].strip()
                        # 移除thinking标签
                        response_content = self._remove_thinking_tags(response_content)
                        responses[index] = response_content
                        print(f"[线程 {threading.current_thread().name}] 完成请求 #{index+1}")
                        return response_content
                    else:
                        logger.error(f"LLM API返回的响应格式不正确: {result}")
                        responses[index] = ""
                        raise Exception("LLM API返回的响应格式不正确")
                except Exception as e:
                    print(f"[线程 {threading.current_thread().name}] 请求 #{index+1} 失败: {str(e)}")
                    responses[index] = ""
                    raise e

            # 使用ThreadPoolExecutor进行并发调用
            max_workers = min(len(payloads), Config.PROCESS_THREAD)  # 使用配置的最大并发数
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_index = {
                    executor.submit(send_single_request, i, payloads[i]): i
                    for i in range(len(payloads))
                }

                # 等待所有任务完成
                for future in concurrent.futures.as_completed(future_to_index):
                    index = future_to_index[future]
                    try:
                        future.result()  # 获取结果，如果有异常会抛出
                    except Exception as e:
                        print(f"并发请求 #{index+1} 出现异常: {str(e)}")
                        # 继续处理其他请求

            print(f"🚀 并发调用完成，成功响应数: {sum(1 for r in responses if r)}")

            # 过滤掉失败的响应，保持顺序
            valid_responses = [r for r in responses if r]
            responses = valid_responses

        except requests.exceptions.RequestException as e:
            logger.error(f"LLM API请求失败: {str(e)}")
            raise Exception(f"LLM API请求失败: {str(e)}")

        return responses

    def _call_llm_api_concurrent(self, prompts: List[str], system_prompts: List[str] = None) -> List[str]:
        """
        并发调用LLM API

        Args:
            prompts: 提示文本列表
            system_prompts: 系统提示文本列表（可选）

        Returns:
            LLM的响应文本列表
        """
        if not prompts:
            return []

        # 如果没有提供系统提示，创建空列表
        if system_prompts is None:
            system_prompts = [None] * len(prompts)

        # 确保系统提示列表长度与提示列表长度一致
        if len(system_prompts) != len(prompts):
            system_prompts = system_prompts[:len(prompts)] + [None] * (len(prompts) - len(system_prompts))

        print(f"\n{'='*50}")
        print(f"🚀 [BATCH模式] 开始并发调用LLM API，共 {len(prompts)} 个请求")
        print(f"🚀 [BATCH模式] 使用ThreadPoolExecutor进行真正的并发处理")
        print(f"{'='*50}")

        # 使用线程池进行并发调用
        responses = [None] * len(prompts)

        def call_single_api(index, prompt, system_prompt):
            """单个API调用的包装函数"""
            try:
                print(f"🚀 [BATCH模式] [线程 {threading.current_thread().name}] 开始处理请求 #{index+1}")
                response = self._call_llm_api(prompt, system_prompt)
                responses[index] = response
                print(f"🚀 [BATCH模式] [线程 {threading.current_thread().name}] 完成请求 #{index+1}")
                return response
            except Exception as e:
                print(f"❌ [BATCH模式] [线程 {threading.current_thread().name}] 请求 #{index+1} 失败: {str(e)}")
                responses[index] = ""
                raise e

        # 使用ThreadPoolExecutor进行并发调用
        max_workers = min(len(prompts), Config.PROCESS_THREAD)  # 使用配置的最大并发数
        print(f"🚀 [BATCH模式] 创建线程池，最大并发数: {max_workers}")

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_index = {
                executor.submit(call_single_api, i, prompts[i], system_prompts[i]): i
                for i in range(len(prompts))
            }
            print(f"🚀 [BATCH模式] 已提交 {len(future_to_index)} 个并发任务到线程池")

            # 等待所有任务完成
            for future in concurrent.futures.as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    future.result()  # 获取结果，如果有异常会抛出
                except Exception as e:
                    print(f"❌ [BATCH模式] 并发请求 #{index+1} 出现异常: {str(e)}")
                    # 继续处理其他请求

        print(f"🚀 [BATCH模式] 并发调用完成，成功响应数: {sum(1 for r in responses if r)}")
        print(f"🚀 [BATCH模式] ✅ 所有LLM API并发调用完成！")

        # 过滤掉失败的响应
        valid_responses = [r for r in responses if r]
        return valid_responses

    def _parse_checkboxes_response(self, response: str, expected_count: int = None) -> List[bool]:
        """
        解析LLM返回的复选框状态

        Args:
            response: LLM的响应文本
            expected_count: 预期的复选框数量（可选）

        Returns:
            布尔值列表，表示每个复选框是否应该被勾选
        """
        try:
            # 打印原始响应内容
            print("="*50)
            print("LLM的原始响应内容:")
            print("="*50)
            print(response)
            print("="*50)

            # 尝试从响应中提取JSON数组
            # 首先尝试直接解析整个响应
            try:
                results = json.loads(response)
                if isinstance(results, list):
                    # 验证结果是否为布尔值列表
                    if all(isinstance(item, bool) for item in results):
                        print(f"成功解析JSON数组: {results}")
                        return results
            except:
                pass

            # 如果直接解析失败，尝试使用正则表达式提取JSON数组
            pattern = r'\[.*?\]'
            matches = re.findall(pattern, response)

            if matches:
                for match in matches:
                    try:
                        results = json.loads(match)
                        if isinstance(results, list):
                            # 验证结果是否为布尔值列表
                            if all(isinstance(item, bool) for item in results):
                                print(f"通过正则表达式提取JSON数组: {results}")
                                return results
                    except:
                        continue

            # 如果上述方法都失败，尝试查找true/false关键词
            true_pattern = r'true|True|TRUE'
            false_pattern = r'false|False|FALSE'

            true_matches = re.findall(true_pattern, response)
            false_matches = re.findall(false_pattern, response)

            if true_matches or false_matches:
                results = []
                for word in re.findall(r'\b\w+\b', response):
                    if re.match(true_pattern, word):
                        results.append(True)
                    elif re.match(false_pattern, word):
                        results.append(False)

                if results:
                    print(f"通过关键词提取结果: {results}")
                    return results

            # 最后尝试直接查找"true"和"false"字符串
            words = response.split()
            results = []
            for word in words:
                word = word.strip('",.[]()')
                if word.lower() == 'true':
                    results.append(True)
                elif word.lower() == 'false':
                    results.append(False)

            if results:
                print(f"通过分词提取结果: {results}")
                return results

            # 如果所有方法都失败，返回空列表
            print("无法解析响应，返回空列表")
            return []

        except Exception as e:
            print(f"解析复选框响应时出错: {str(e)}")
            return []

    # process_single_checkbox 方法已被删除，其功能已整合到 process_row_checkboxes 方法中

    def process_checkbox(self, checkbox_context: Dict, case_data: Dict[str, Any]) -> bool:
        """
        处理单个复选框，与测试中模拟的方法保持一致

        Args:
            checkbox_context: 复选框上下文
            case_data: 案件数据

        Returns:
            布尔值，表示复选框是否应该被勾选
        """
        # 如果没有LLM处理器，返回False
        if not self.llm_processor:
            logger.warning("LLM处理器未初始化，无法处理复选框")
            return False

        try:
            # 获取完整文本
            full_text = checkbox_context.get('full_text', '')

            # 构建行上下文
            row_context = {
                'row_text': checkbox_context.get('row_content', ''),
                'first_column': checkbox_context.get('first_column_content', ''),
                'paragraph_text': full_text
            }

            # 将单个复选框包装为列表，使用process_row_checkboxes处理
            results = self.process_row_checkboxes(row_context, [checkbox_context])

            # 返回结果，如果有结果则返回第一个，否则返回False
            return results[0] if results else False
        except Exception as e:
            logger.error(f"处理复选框时出错: {str(e)}")
            return False

    def should_check_checkbox(self, checkbox_context: Dict, paragraph_text: str, table_context=None) -> bool:
        """
        决定是否应该勾选复选框，与测试中调用的方法保持一致

        Args:
            checkbox_context: 复选框上下文
            paragraph_text: 段落文本
            table_context: 表格上下文（如果在表格中）

        Returns:
            布尔值，表示复选框是否应该被勾选
        """
        try:
            # 首先尝试使用LLM处理器
            if self.config.get('use_llm_for_checkbox', True) and self.llm_processor:
                # 构建行上下文
                row_context_data = {
                    'row_text': checkbox_context.get('row_content', ''),
                    'first_column': checkbox_context.get('first_column_content', ''),
                    'paragraph_text': paragraph_text
                }

                # 如果有表格上下文，合并到行上下文中
                if table_context:
                    row_context_data['row_text'] = table_context.get('row_text', row_context_data['row_text'])
                    row_context_data['first_column'] = table_context.get('first_column', row_context_data['first_column'])

                # 尝试直接处理单个复选框
                try:
                    # 添加复选框数量信息
                    row_context_data['total_checkboxes'] = 1

                    # 构建分离的提示
                    system_prompt, user_prompt = self._build_prompt_for_row_checkboxes_simplified(row_context_data)

                    # 调用LLM API
                    response = self._call_llm_api(user_prompt, system_prompt)

                    # 处理单个复选框的特殊情况，当LLM直接返回布尔值字符串时
                    if response.strip().lower() in ["true", "false"]:
                        return response.strip().lower() == "true"

                    # 如果不是布尔字符串，尝试正常解析
                    results = self._parse_checkboxes_response(response, 1)
                    return results[0] if results else False

                except Exception as e:
                    logger.error(f"处理单个复选框时出错: {str(e)}")
                    # 如果出错，回退到使用标准方法
                    results = self.process_row_checkboxes(row_context_data, [checkbox_context])
                    return results[0] if results else False
            return False
        except Exception as e:
            logger.error(f"判断复选框是否应该勾选时出错: {str(e)}")
            return False

    def _group_checkboxes_by_row(self, checkboxes_context: List[Dict]) -> Dict:
        """
        改进版的复选框分组方法，按照row_id将复选框分组

        Args:
            checkboxes_context: 复选框上下文列表

        Returns:
            按行分组的复选框字典
        """
        # 初始化分组字典
        row_groups = {}

        # 检查是否有row_id字段
        has_row_id = any('row_id' in ctx for ctx in checkboxes_context if ctx)

        if has_row_id:
            # 按row_id分组
            logger.info("使用row_id分组复选框")
            for i, ctx in enumerate(checkboxes_context):
                row_id = ctx.get('row_id', 'default')
                row_content = ctx.get('row_content', '')
                first_column_content = ctx.get('first_column_content', '')

                if row_id not in row_groups:
                    row_groups[row_id] = {
                        "row_text": row_content,
                        "first_column": first_column_content,
                        "checkboxes": []
                    }

                row_groups[row_id]["checkboxes"].append({
                    "index": i,
                    "context": ctx
                })
        else:
            # 如果没有row_id，则按行内容分组
            logger.info("没有row_id，按行内容分组复选框")
            for i, ctx in enumerate(checkboxes_context):
                row_content = ctx.get('row_content', '')
                first_column_content = ctx.get('first_column_content', '')

                # 使用行内容作为分组键
                group_key = f"{first_column_content}_{row_content}"

                if group_key not in row_groups:
                    row_groups[group_key] = {
                        "row_text": row_content,
                        "first_column": first_column_content,
                        "checkboxes": []
                    }

                row_groups[group_key]["checkboxes"].append({
                    "index": i,
                    "context": ctx
                })

        # 如果没有分组，创建一个默认分组
        if not row_groups:
            logger.info("没有有效的分组信息，创建默认分组")
            # 收集所有行内容和第一列内容
            row_texts = []
            first_columns = []

            for ctx in checkboxes_context:
                row_content = ctx.get('row_content', '')
                first_column_content = ctx.get('first_column_content', '')

                if row_content and row_content not in row_texts:
                    row_texts.append(row_content)

                if first_column_content and first_column_content not in first_columns:
                    first_columns.append(first_column_content)

            # 创建单一分组
            row_groups["default"] = {
                "row_text": " | ".join(row_texts),
                "first_column": " | ".join(first_columns),
                "checkboxes": []
            }

            # 添加所有复选框
            for i, ctx in enumerate(checkboxes_context):
                row_groups["default"]["checkboxes"].append({
                    "index": i,
                    "context": ctx
                })

        return row_groups

    def process_combined_checkboxes(self, checkbox_sections: List[Dict]) -> List[bool]:
        """
        合并处理多个栏目的复选框，根据配置决定是否使用并发调用

        Args:
            checkbox_sections: 复选框栏目列表，每个栏目包含：
                - first_column: 栏目名称
                - row_text: 具体判断选项
                - total_checkboxes: 该栏目的复选框数量
                - section_index: 栏目索引（用于结果分配）

        Returns:
            List[bool]: 所有栏目复选框的判断结果，按栏目顺序排列
        """
        if not self.llm_api_url or not self.llm_api_key:
            logger.warning("LLM API未配置，无法处理复选框")
            total_checkboxes = sum(section.get('total_checkboxes', 0) for section in checkbox_sections)
            return [False] * total_checkboxes

        if not checkbox_sections:
            logger.warning("没有复选框栏目需要处理")
            return []

        try:
            print(f"\n🔄 开始处理 {len(checkbox_sections)} 个复选框栏目")

            # 根据配置决定处理方式
            if self.process_checkbox_batch:
                print("🚀 [BATCH模式] 使用批量并发模式处理复选框栏目")
                print(f"🚀 [BATCH模式] PROCESS_CHECKBOX_BATCH=true 已启用，将并发调用 {len(checkbox_sections)} 个LLM API")
                return self._process_sections_concurrent(checkbox_sections)
            else:
                print("⏳ [顺序模式] 使用顺序模式处理复选框栏目")
                print(f"⏳ [顺序模式] PROCESS_CHECKBOX_BATCH=false，将顺序处理 {len(checkbox_sections)} 个栏目")
                return self._process_sections_sequential(checkbox_sections)

        except Exception as e:
            error_msg = f"处理复选框时出错: {str(e)}"
            logger.error(error_msg)
            # 检查是否是LLM API相关错误，如果是则重新抛出以便上层处理
            if "LLM API请求失败" in str(e) or "timeout" in str(e).lower() or "connection" in str(e).lower():
                raise Exception(f"LLM API调用失败: {str(e)}")
            total_checkboxes = sum(section.get('total_checkboxes', 0) for section in checkbox_sections)
            return [False] * total_checkboxes

    def _process_sections_sequential(self, checkbox_sections: List[Dict]) -> List[bool]:
        """
        顺序处理复选框栏目（原有的合并处理逻辑）

        Args:
            checkbox_sections: 复选框栏目列表

        Returns:
            List[bool]: 所有栏目复选框的判断结果
        """
        print("使用合并模式顺序处理所有栏目")

        # 构建分离的合并提示词
        system_prompt, user_prompt = self._build_combined_prompt(checkbox_sections)

        # 调用LLM API
        response = self._call_llm_api(user_prompt, system_prompt)

        # 计算总的复选框数量
        total_checkboxes = sum(section.get('total_checkboxes', 0) for section in checkbox_sections)

        # 解析响应
        results = self._parse_checkboxes_response(response, total_checkboxes)

        # 验证结果数量
        if len(results) != total_checkboxes:
            logger.warning(f"LLM返回的结果数量({len(results)})与期望的复选框数量({total_checkboxes})不匹配")

            # 调整结果数量
            if len(results) > total_checkboxes:
                results = results[:total_checkboxes]
            else:
                results.extend([False] * (total_checkboxes - len(results)))

        print(f"顺序处理完成，返回 {len(results)} 个结果: {results}")
        return results

    def _process_sections_concurrent(self, checkbox_sections: List[Dict]) -> List[bool]:
        """
        并发处理复选框栏目（每个栏目单独调用API）

        Args:
            checkbox_sections: 复选框栏目列表

        Returns:
            List[bool]: 所有栏目复选框的判断结果
        """
        print("🚀 [BATCH模式] 使用并发模式处理各个栏目")
        print(f"🚀 [BATCH模式] 准备为 {len(checkbox_sections)} 个栏目构建独立的LLM提示词")

        # 为每个栏目构建单独的提示词
        prompts = []
        system_prompts = []

        for i, section in enumerate(checkbox_sections):
            # 为单个栏目构建提示词
            system_prompt, user_prompt = self._build_single_section_prompt(section)
            prompts.append(user_prompt)
            system_prompts.append(system_prompt)
            print(f"🚀 [BATCH模式] 栏目 {i+1}: {section.get('first_column', '未知栏目')} - 构建提示词完成")

        print(f"🚀 [BATCH模式] 所有提示词构建完成，准备并发调用 {len(prompts)} 个LLM API")

        # 并发调用LLM API
        responses = self._call_llm_api_concurrent(prompts, system_prompts)

        # 解析每个响应并合并结果
        all_results = []
        for i, (response, section) in enumerate(zip(responses, checkbox_sections)):
            expected_count = section.get('total_checkboxes', 0)
            section_results = self._parse_checkboxes_response(response, expected_count)

            # 验证结果数量
            if len(section_results) != expected_count:
                logger.warning(f"栏目 {i+1} LLM返回的结果数量({len(section_results)})与期望数量({expected_count})不匹配")

                # 调整结果数量
                if len(section_results) > expected_count:
                    section_results = section_results[:expected_count]
                else:
                    section_results.extend([False] * (expected_count - len(section_results)))

            all_results.extend(section_results)
            print(f"🚀 [BATCH模式] 栏目 {i+1} 处理完成，结果: {section_results}")

        print(f"🚀 [BATCH模式] 并发处理完成，总共返回 {len(all_results)} 个结果: {all_results}")
        print(f"🚀 [BATCH模式] ✅ 批量并发处理成功完成！")
        return all_results

    def _build_combined_prompt(self, checkbox_sections: List[Dict]) -> tuple:
        """
        构建合并处理的提示词，返回分离的system prompt和user prompt

        Args:
            checkbox_sections: 复选框栏目列表

        Returns:
            tuple: (system_prompt, user_prompt)
        """
        # 获取OCR原始文本作为案件完整内容
        ocr_original_text = self.config.get('ocr_original_text', '')
        logger.info(f"[合并复选框] OCR原始文本检查: 存在={bool(ocr_original_text)}, 长度={len(ocr_original_text.strip() if ocr_original_text else '')}")

        # 案件信息部分
        if ocr_original_text and len(ocr_original_text.strip()) > 0:
            logger.info("[合并复选框] 使用OCR原始文本作为案件完整信息")
            # 清理OCR文本
            cleaned_ocr_text = re.sub(r'\n{2,}', '\n', ocr_original_text)
            cleaned_ocr_text = re.sub(r' {2,}', ' ', cleaned_ocr_text)
            cleaned_ocr_text = '\n'.join([line.strip() for line in cleaned_ocr_text.split('\n')])

            # 限制文本长度
            max_text_length = 8000
            if len(cleaned_ocr_text) > max_text_length:
                case_info = f"""
{cleaned_ocr_text[:max_text_length]}...(省略后续内容)
                """
            else:
                case_info = f"""
{cleaned_ocr_text}
                """
        else:
            # 使用结构化案件数据
            case_info = self._format_case_data_for_prompt()

        # 计算总的复选框数量
        total_checkboxes = sum(section.get('total_checkboxes', 0) for section in checkbox_sections)

        # 使用checkbox_prompts中的函数构建分离的提示词
        system_prompt = get_checkbox_system_prompt(case_info)
        user_prompt = get_combined_user_prompt(checkbox_sections, total_checkboxes)

        return system_prompt, user_prompt

    def _build_single_section_prompt(self, section: Dict) -> tuple:
        """
        为单个栏目构建提示词，返回分离的system prompt和user prompt

        Args:
            section: 单个复选框栏目信息

        Returns:
            tuple: (system_prompt, user_prompt)
        """
        # 获取OCR原始文本作为案件完整内容
        ocr_original_text = self.config.get('ocr_original_text', '')
        logger.info(f"[单个栏目] OCR原始文本检查: 存在={bool(ocr_original_text)}, 长度={len(ocr_original_text.strip() if ocr_original_text else '')}")

        # 案件信息部分
        if ocr_original_text and len(ocr_original_text.strip()) > 0:
            logger.info("[单个栏目] 使用OCR原始文本作为案件完整信息")
            # 清理OCR文本
            cleaned_ocr_text = re.sub(r'\n{2,}', '\n', ocr_original_text)
            cleaned_ocr_text = re.sub(r' {2,}', ' ', cleaned_ocr_text)
            cleaned_ocr_text = '\n'.join([line.strip() for line in cleaned_ocr_text.split('\n')])

            # 限制文本长度
            max_text_length = 8000
            if len(cleaned_ocr_text) > max_text_length:
                case_info = f"""
{cleaned_ocr_text[:max_text_length]}...(省略后续内容)
                """
            else:
                case_info = f"""
{cleaned_ocr_text}
                """
        else:
            # 使用结构化案件数据
            case_info = self._format_case_data_for_prompt()

        # 获取栏目信息
        first_column = section.get('first_column', '')
        row_text = section.get('row_text', '')
        total_checkboxes = section.get('total_checkboxes', 0)

        # 使用checkbox_prompts中的函数构建分离的提示词
        system_prompt = get_checkbox_system_prompt(case_info)
        user_prompt = get_single_row_user_prompt(first_column, row_text, total_checkboxes)

        return system_prompt, user_prompt

    def _format_case_data_for_prompt(self) -> str:
        """
        将案件数据格式化为提示词中的案件信息，过滤掉当事人索引字段

        Returns:
            str: 格式化的案件信息
        """
        if not self.case_data:
            return "# 案件完整信息：\n（无案件数据）"

        case_info_parts = []

        # 需要过滤的字段模式（当事人索引格式的字段）
        import re
        party_index_pattern = r'^(原告|被告|第三人)_[^_]+_\d+_'

        # 需要跳过的字段
        skip_fields = {
            '_complete_document_text', '_remaining_placeholders',
            '_intermediate_doc_structure', '_intermediate_doc_path',
            'template_filename', 'file_id'
        }

        # 添加基本信息
        if 'plaintiff' in self.case_data:
            case_info_parts.append(f"原告：{self.case_data['plaintiff']}")
        if 'defendant' in self.case_data:
            case_info_parts.append(f"被告：{self.case_data['defendant']}")
        if 'case_type' in self.case_data or 'template_type' in self.case_data:
            case_type = self.case_data.get('case_type') or self.case_data.get('template_type')
            case_info_parts.append(f"案件类型：{case_type}")

        # 添加其他字段，但过滤掉当事人索引字段和内部字段
        for key, value in self.case_data.items():
            # 跳过已处理的基本字段
            if key in ['plaintiff', 'defendant', 'case_type', 'template_type']:
                continue

            # 跳过内部字段
            if key in skip_fields:
                continue

            # 跳过当事人索引格式的字段
            if re.match(party_index_pattern, key):
                continue

            # 跳过空值
            if not value:
                continue

            # 添加有效字段
            case_info_parts.append(f"{key}：{value}")

        case_info = "# 案件完整信息：\n" + "\n".join(case_info_parts)
        print(f"格式化案件数据，包含 {len(case_info_parts)} 个字段，总长度: {len(case_info)}")
        return case_info