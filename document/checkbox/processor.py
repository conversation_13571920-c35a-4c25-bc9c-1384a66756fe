"""
复选框处理器主模块
负责协调LLM处理器和规则引擎处理器的工作
"""

import logging
import os
from typing import Dict, Any, List, Tuple, Optional
from docx.oxml.shared import OxmlElement
import re

from .llm_handler import LLMCheckboxHandler
from .rule_engine import RuleBasedCheckboxHandler
from .batch_processor import BatchCheckboxProcessor
from .constants import CHECKBOX_CHARS, DEFAULT_CHECKED, DEFAULT_UNCHECKED, get_safe_checkbox_char

logger = logging.getLogger(__name__)


class CheckboxProcessor:
    """复选框处理器，负责决定是否勾选文档中的复选框"""

    def __init__(self, case_data: Dict[str, Any], config: Dict[str, Any]):
        """
        初始化复选框处理器

        Args:
            case_data: 案件数据
            config: 配置信息
        """
        self.case_data = case_data
        self.config = config

        # 初始化LLM处理器
        self.llm_handler = LLMCheckboxHandler(case_data, config)

        # 初始化规则引擎处理器
        self.rule_handler = RuleBasedCheckboxHandler(case_data, config)

        # 初始化批量处理器
        self.batch_processor = None
        if config.get("use_batch_processing", False):
            # 优先使用共享的LLM处理器
            shared_llm_processor = config.get('shared_llm_processor')
            if shared_llm_processor:
                print("CheckboxProcessor: 批量处理器使用共享LLM实例")
                self.batch_processor = BatchCheckboxProcessor(shared_llm_processor=shared_llm_processor)
            else:
                # 回退到传统方式，优先使用复选框专用配置
                llm_api_url = os.environ.get("CHECKBOX_LLM_API_URL") or os.environ.get("LLM_API_URL", "")
                llm_api_key = os.environ.get("CHECKBOX_LLM_API_KEY") or os.environ.get("LLM_API_KEY", "")
                llm_model = os.environ.get("CHECKBOX_LLM_MODEL") or os.environ.get("LLM_MODEL", "gpt-3.5-turbo")
                print("CheckboxProcessor: 批量处理器创建新LLM实例")
                print(f"  - 使用复选框专用API URL: {llm_api_url}")
                print(f"  - 使用复选框专用模型: {llm_model}")
                self.batch_processor = BatchCheckboxProcessor(
                    llm_api_url=llm_api_url, llm_api_key=llm_api_key, llm_model=llm_model
                )

    def process_checkboxes(self, paragraph, table_context=None):
        """
        处理段落中的复选框

        Args:
            paragraph: 包含复选框的段落
            table_context: 表格上下文信息（如果复选框在表格中）
                          当位于表格中时，这应包含整个表格行的内容

        Returns:
            处理后的段落
        """
        # 获取段落文本
        paragraph_text = paragraph.text

        # 获取段落中的所有复选框
        checkboxes = self._get_checkboxes_in_paragraph(paragraph)

        if not checkboxes:
            return paragraph

        # 记录日志
        print("📍 [调用路径] 进入 process_checkboxes (单个段落处理模式)")
        print(f"📍 [调用路径] PROCESS_CHECKBOX_BATCH={self.llm_handler.process_checkbox_batch} (此路径不使用批量处理)")
        print(
            f"处理段落中的复选框，段落文本: {paragraph_text[:100]}{'...' if len(paragraph_text) > 100 else ''}"
        )
        print(f"找到 {len(checkboxes)} 个复选框")

        # 检查是否有表格上下文
        if table_context:
            # 表格处理模式
            if "row_text" in table_context and table_context["row_text"]:
                row_text = table_context["row_text"]
                print(
                    f"正在处理表格行中的复选框: '{row_text[:100]}{'...' if len(row_text) > 100 else ''}'"
                )

                # 检查表格上下文中是否已经有LLM结果队列
                if "llm_results_queue" in table_context and table_context["llm_results_queue"]:
                    print("使用表格上下文中已有的LLM结果队列")
                    # 使用已有的结果队列处理复选框
                    self._process_checkboxes_with_existing_results(
                        checkboxes, paragraph_text, table_context
                    )
                else:
                    # 注意：这种情况应该很少发生，因为我们在_process_document_tables_improved方法中
                    # 已经为整个表格行获取了LLM结果，并保存到了表格上下文中
                    print("警告: 表格上下文中没有LLM结果队列，这可能是因为表格行处理逻辑有问题")
                    print("将为当前段落单独调用LLM获取结果")

                    # 使用基于行的处理方式，并将结果保存到表格上下文中
                    results = self._process_checkboxes_in_same_row(
                        checkboxes, paragraph_text, table_context, return_results=True
                    )

                    # 如果有结果，保存到表格上下文中
                    if results and isinstance(results, list):
                        # 创建一个新的表格上下文，包含LLM结果队列
                        table_context["llm_results_queue"] = list(results)
                        print(f"将LLM结果队列保存到表格上下文中: {results}")

                        # 使用结果队列处理复选框
                        self._process_checkboxes_with_existing_results(
                            checkboxes, paragraph_text, table_context
                        )
            else:
                print("警告: 表格上下文中缺少行文本信息，将使用段落处理模式")
                self._process_checkboxes_individually(
                    checkboxes, paragraph, table_context
                )
        else:
            # 段落处理模式
            print("使用段落处理模式 (非表格上下文)")
            self._process_checkboxes_individually(checkboxes, paragraph)

        return paragraph

    def process_document_checkboxes(self, doc):
        """
        批量处理整个文档中的所有复选框

        Args:
            doc: Word文档对象

        Returns:
            处理后的文档对象
        """
        print("📍 [调用路径] 进入 process_document_checkboxes (文档级批量处理模式)")
        print(f"📍 [调用路径] PROCESS_CHECKBOX_BATCH={self.llm_handler.process_checkbox_batch} (此路径可能使用批量处理)")
        # 新增：在处理前记录所有表格行中的复选框情况
        print("\n========== 文档预检：表格行复选框分布 ==========")
        table_rows_with_checkboxes = {}

        # 构建表格行与run的映射关系
        table_row_runs = {}

        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                # 提取行文本
                first_column = row.cells[0].text.strip() if len(
                    row.cells) > 0 else ""
                other_cells_text = " ".join(
                    [c.text.strip() for c in row.cells[1:] if c.text.strip()]
                )

                row_text = first_column + " " + other_cells_text

                # 生成唯一的行ID，确保多当事人不会冲突
                # 使用表格索引和行索引确保唯一性，同时保留第一列内容用于调试
                row_id = f"table_{table_idx}_row_{row_idx}"
                if first_column:
                    # 添加第一列内容作为描述，但不影响唯一性
                    row_id_display = f"{row_id}_{first_column}"
                else:
                    row_id_display = row_id

                # 收集行中的所有run
                row_runs = []
                for cell_idx, cell in enumerate(row.cells):
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        for run_idx, run in enumerate(paragraph.runs):
                            run_id = id(run)
                            row_runs.append(
                                {
                                    "run": run,
                                    "run_id": run_id,
                                    "cell_idx": cell_idx,
                                    "para_idx": para_idx,
                                    "run_idx": run_idx,
                                    "text": run.text,
                                }
                            )

                # 存储行与run的映射关系
                table_row_runs[row_id] = {
                    "table_idx": table_idx,
                    "row_idx": row_idx,
                    "first_column": first_column,
                    "row_text": other_cells_text,
                    "full_text": row_text,
                    "runs": row_runs,
                }

                # 检查是否包含复选框
                checkbox_count = (
                    row_text.count("□") + row_text.count("☐") +
                    row_text.count("☑")
                )
                if checkbox_count > 0:
                    table_rows_with_checkboxes[row_id] = {
                        "table_idx": table_idx,
                        "row_idx": row_idx,
                        "first_column": first_column,
                        "row_text": other_cells_text,
                        "full_text": row_text,
                        "checkbox_count": checkbox_count,
                    }
                    print(f"发现包含复选框的行 - ID:{row_id} ({first_column})")
                    print(f"  内容: '{row_text}'")
                    print(f"  复选框数量: {checkbox_count}")
                    print(f"  包含 {len(row_runs)} 个run")

                    # 检查是否包含选项组（通过斜杠分隔的模式）
                    slash_pattern = r"[^\s]+□/[^\s]+□"
                    if re.search(slash_pattern, row_text):
                        print(f"  检测到可能的选项组模式")

        print(f"共发现 {len(table_rows_with_checkboxes)} 个包含复选框的表格行")
        print(f"构建了 {len(table_row_runs)} 个表格行与run的映射关系")
        print("========== 文档预检结束 ==========\n")

        # 将表格行与run的映射关系存储为实例变量，以便其他方法使用
        self.table_row_runs = table_row_runs

        # 检查是否启用了批量处理
        use_batch_processing = self.config.get("use_batch_processing", False)

        if use_batch_processing and self.batch_processor:
            try:
                print("使用批量处理器处理文档中的所有复选框")

                # 批量处理文档中的所有复选框
                results = self.batch_processor.process_document(
                    doc, self.case_data)

                # 应用处理结果到文档
                if results:
                    print(f"批量处理完成，应用结果到文档")
                    return doc
            except Exception as e:
                print(f"批量处理失败: {str(e)}")
                import traceback

                print(traceback.format_exc())

        # 如果批量处理失败或未启用，使用逐行处理
        print("使用逐行处理模式处理文档中的复选框")

        # 检查是否启用合并处理
        if self.llm_handler.combine_checkboxes:
            print("启用复选框合并处理模式")
            # 根据合并策略选择处理方法
            if self.llm_handler.combine_checkboxes_by == 'table':
                print("使用按表格合并策略")
                self._process_document_tables_combined_by_table(doc, table_rows_with_checkboxes)
            else:
                print("使用全文合并策略")
                self._process_document_tables_combined(doc, table_rows_with_checkboxes)
        else:
            print("使用传统逐行处理模式")
            # 使用改进的表格行处理方法
            self._process_document_tables_improved(doc, table_rows_with_checkboxes)

        # 处理非表格中的复选框
        for paragraph in doc.paragraphs:
            if "□" in paragraph.text or "☐" in paragraph.text or "☑" in paragraph.text:
                self.process_checkboxes(paragraph)

        return doc

    def _process_document_tables_improved(self, doc, table_rows_with_checkboxes):
        """
        改进的表格处理方法，根据配置决定是否使用批量处理

        Args:
            doc: Word文档对象
            table_rows_with_checkboxes: 包含复选框的表格行信息
        """
        print("\n========== 开始处理表格中的复选框 ==========")

        if not table_rows_with_checkboxes:
            print("没有包含复选框的表格行需要处理")
            return

        # 检查是否启用批量处理
        if self.llm_handler.process_checkbox_batch:
            print("🚀 [BATCH模式] 使用批量并发模式处理表格行")
            print(f"🚀 [BATCH模式] PROCESS_CHECKBOX_BATCH=true，将对 {len(table_rows_with_checkboxes)} 个表格行进行批量并发处理")
            self._process_tables_batch(doc, table_rows_with_checkboxes)
        else:
            print("⏳ [顺序模式] 使用顺序模式处理表格行")
            print(f"⏳ [顺序模式] PROCESS_CHECKBOX_BATCH=false，将对 {len(table_rows_with_checkboxes)} 个表格行进行顺序处理")
            self._process_tables_sequential(doc, table_rows_with_checkboxes)

        print("========== 表格处理完成 ==========\n")

    def _process_tables_sequential(self, doc, table_rows_with_checkboxes):
        """
        顺序处理表格行（原有逻辑）

        Args:
            doc: Word文档对象
            table_rows_with_checkboxes: 包含复选框的表格行信息
        """
        # 遍历所有包含复选框的表格行
        for row_id, row_info in table_rows_with_checkboxes.items():
            table_idx = row_info["table_idx"]
            row_idx = row_info["row_idx"]
            first_column = row_info["first_column"]
            row_text = row_info["row_text"]
            full_text = row_info["full_text"]
            checkbox_count = row_info["checkbox_count"]

            print(f"\n处理表格行 - ID:{row_id}")
            print(f"  表格索引: {table_idx}, 行索引: {row_idx}")
            print(f"  第一列内容: '{first_column}'")
            print(f"  行文本: '{row_text}'")
            print(f"  复选框数量: {checkbox_count}")

            # 获取行中的所有run
            row_runs = []
            if row_id in self.table_row_runs:
                row_runs = self.table_row_runs[row_id]["runs"]
                print(f"  找到 {len(row_runs)} 个run")

            # 收集行中的所有复选框和段落
            all_checkboxes = []
            all_paragraphs = []

            # 从表格中获取行
            if table_idx < len(doc.tables) and row_idx < len(doc.tables[table_idx].rows):
                row = doc.tables[table_idx].rows[row_idx]

                # 遍历行中的所有单元格
                for cell_idx, cell in enumerate(row.cells):
                    # 遍历单元格中的所有段落
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        # 检查段落中是否包含复选框字符
                        checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓", "⬜", "⬛", "▢", "▣", "▪", "▫"]
                        has_checkbox = any(char in paragraph.text for char in checkbox_chars)

                        if has_checkbox:
                            print(f"    在单元格 {cell_idx} 段落 {para_idx} 中找到复选框字符")

                            # 获取段落中的所有复选框
                            checkboxes = self._get_checkboxes_in_paragraph(paragraph)

                            if checkboxes:
                                print(f"    找到 {len(checkboxes)} 个复选框对象")
                                all_checkboxes.extend(checkboxes)
                                all_paragraphs.append(paragraph)

            # 如果没有找到复选框，尝试使用行中的run直接查找
            if not all_checkboxes and row_runs:
                print("  未在段落中找到复选框对象，尝试使用run直接查找")

                # 创建一个临时段落，用于处理run中的复选框
                from docx.text.paragraph import Paragraph
                temp_paragraph = Paragraph(None, None)
                temp_paragraph._p = None  # 清空段落的XML元素
                temp_paragraph._element = None
                temp_paragraph.runs = []

                # 手动构建段落文本
                paragraph_text = ""
                for run_info in row_runs:
                    run = run_info["run"]
                    run_text = run_info["text"]
                    paragraph_text += run_text
                    temp_paragraph.runs.append(run)

                # 设置段落文本
                temp_paragraph.text = paragraph_text

                # 获取段落中的所有复选框
                checkboxes = self._get_checkboxes_in_paragraph_from_runs(
                    temp_paragraph, row_runs
                )

                if checkboxes:
                    print(f"  通过run直接查找到 {len(checkboxes)} 个复选框对象")
                    all_checkboxes.extend(checkboxes)
                    all_paragraphs.append(temp_paragraph)

            # 如果找到了复选框，处理它们
            if all_checkboxes:
                print(f"  共找到 {len(all_checkboxes)} 个复选框对象，开始处理")

                # 计算表格行中的复选框字符数量
                full_row_text = f"{first_column} {row_text}"

                # 使用统一的复选框字符定义
                checkbox_chars = CHECKBOX_CHARS
                checkbox_count = sum(
                    full_row_text.count(char) for char in checkbox_chars
                )

                print(f"  表格行中检测到 {checkbox_count} 个复选框字符")
                print(f"  当前处理的复选框对象数量: {len(all_checkboxes)}")

                # 如果检测到的复选框字符数量与当前处理的复选框对象数量不一致，记录警告
                if checkbox_count != len(all_checkboxes):
                    print(
                        f"  警告: 表格行中的复选框字符数量({checkbox_count})与当前处理的复选框对象数量({len(all_checkboxes)})不一致")

                    # 如果表格行中的复选框字符数量大于当前处理的复选框对象数量，尝试直接从行文本中创建复选框对象
                    if checkbox_count > len(all_checkboxes):
                        print(f"  尝试直接从表格行文本中创建复选框对象")

                        # 创建一个临时段落，用于处理行文本中的复选框
                        from docx.text.paragraph import Paragraph
                        temp_paragraph = Paragraph(None, None)
                        temp_paragraph._p = None
                        temp_paragraph._element = None
                        temp_paragraph.text = full_row_text

                        # 直接从行文本中获取复选框对象
                        direct_checkboxes = self._get_checkboxes_in_paragraph(
                            temp_paragraph)

                        if direct_checkboxes:
                            print(
                                f"  直接从行文本中找到 {len(direct_checkboxes)} 个复选框对象")
                            # 使用直接从行文本中找到的复选框对象
                            all_checkboxes = direct_checkboxes
                            all_paragraphs = [temp_paragraph]
                            print(f"  更新后的复选框对象数量: {len(all_checkboxes)}")

                # 构建表格上下文
                table_context = {
                    'row_id': row_id,
                    'table_idx': table_idx,
                    'row_idx': row_idx,
                    'first_column': first_column,
                    'row_text': row_text,
                    'full_text': full_text,
                    'total_checkboxes': checkbox_count  # 使用检测到的复选框字符数量
                }

                # 首先获取整行的LLM结果，但不应用
                # 使用所有检测到的复选框字符数量，而不是实际的复选框对象数量
                # 创建一个临时复选框列表，长度与检测到的复选框字符数量相同
                temp_checkboxes = []
                for i in range(checkbox_count):
                    temp_checkboxes.append({
                        "position": i,
                        "checkbox_char": "□",
                        "paragraph": None
                    })

                # 调用LLM获取整行的结果
                results = self._process_checkboxes_in_same_row(
                    temp_checkboxes, full_row_text, table_context, return_results=True
                )

                if results and isinstance(results, list):
                    # 保存结果到表格上下文中
                    table_context["llm_results_queue"] = list(results)
                    print(f"  获取到LLM结果队列: {results}")

                    # 为每个段落处理复选框，使用同一个结果队列
                    for i, paragraph in enumerate(all_paragraphs):
                        # 获取段落中的复选框
                        para_checkboxes = self._get_checkboxes_in_paragraph(paragraph)
                        if para_checkboxes:
                            print(f"  处理段落 {i+1}/{len(all_paragraphs)}, 包含 {len(para_checkboxes)} 个复选框")
                            # 使用已有的结果队列处理复选框
                            self._process_checkboxes_with_existing_results(
                                para_checkboxes, paragraph.text, table_context
                            )
                else:
                    print(f"  未获取到有效的LLM结果，跳过处理")
            else:
                print(f"  未找到复选框对象，跳过处理")

    def _process_tables_batch(self, doc, table_rows_with_checkboxes):
        """
        批量并发处理表格行

        Args:
            doc: Word文档对象
            table_rows_with_checkboxes: 包含复选框的表格行信息
        """
        print("🚀 [BATCH模式] 开始批量并发处理表格行")
        print(f"🚀 [BATCH模式] 准备收集 {len(table_rows_with_checkboxes)} 个表格行的信息")

        # 收集所有行的信息，准备批量处理
        row_sections = []
        row_mapping = {}

        for row_id, row_info in table_rows_with_checkboxes.items():
            table_idx = row_info["table_idx"]
            row_idx = row_info["row_idx"]
            first_column = row_info["first_column"]
            row_text = row_info["row_text"]
            full_text = row_info["full_text"]
            checkbox_count = row_info["checkbox_count"]

            # 收集该行的复选框对象
            all_paragraphs = []
            all_checkboxes = []

            # 获取行中的所有run
            row_runs = []
            if row_id in self.table_row_runs:
                row_runs = self.table_row_runs[row_id]["runs"]

            # 从表格中获取行
            if table_idx < len(doc.tables) and row_idx < len(doc.tables[table_idx].rows):
                row = doc.tables[table_idx].rows[row_idx]

                # 遍历行中的所有单元格
                for cell_idx, cell in enumerate(row.cells):
                    # 遍历单元格中的所有段落
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        # 检查段落中是否包含复选框字符
                        checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓", "⬜", "⬛", "▢", "▣", "▪", "▫"]
                        has_checkbox = any(char in paragraph.text for char in checkbox_chars)

                        if has_checkbox:
                            all_paragraphs.append(paragraph)
                            # 获取段落中的复选框对象
                            checkboxes = self._get_checkboxes_in_paragraph(paragraph)
                            all_checkboxes.extend(checkboxes)

                # 如果没有找到复选框，尝试使用行中的run直接查找
                if not all_checkboxes and row_runs:
                    # 创建一个临时段落，用于处理run中的复选框
                    from docx.text.paragraph import Paragraph
                    temp_paragraph = Paragraph(None, None)
                    temp_paragraph._p = None
                    temp_paragraph._element = None
                    temp_paragraph.runs = []

                    # 手动构建段落文本
                    paragraph_text = ""
                    for run_info in row_runs:
                        run = run_info["run"]
                        run_text = run_info["text"]
                        paragraph_text += run_text
                        temp_paragraph.runs.append(run)

                    # 设置段落文本
                    temp_paragraph.text = paragraph_text

                    # 获取段落中的所有复选框
                    checkboxes = self._get_checkboxes_in_paragraph_from_runs(
                        temp_paragraph, row_runs
                    )

                    if checkboxes:
                        all_checkboxes.extend(checkboxes)
                        all_paragraphs.append(temp_paragraph)

                # 如果仍然没有找到复选框对象，但检测到复选框字符，创建模拟对象
                if not all_checkboxes and checkbox_count > 0:
                    full_row_text = f"{first_column} {row_text}"

                    # 找到第一个非空段落
                    temp_paragraph = None
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            if paragraph.text.strip():
                                temp_paragraph = paragraph
                                break
                        if temp_paragraph:
                            break

                    if temp_paragraph:
                        # 从整行文本中创建复选框对象
                        direct_checkboxes = []
                        checkbox_chars = CHECKBOX_CHARS
                        for char in checkbox_chars:
                            positions = [pos for pos, c in enumerate(full_row_text) if c == char]
                            for pos in positions:
                                text_before = full_row_text[:pos] if pos > 0 else ""
                                text_after = full_row_text[pos+1:] if pos < len(full_row_text) - 1 else ""

                                direct_checkbox = {
                                    "paragraph": temp_paragraph,
                                    "position": pos,
                                    "text_before": text_before,
                                    "text_after": text_after,
                                    "full_text": full_row_text,
                                    "checkbox_char": char,
                                }
                                direct_checkboxes.append(direct_checkbox)

                        if direct_checkboxes:
                            all_checkboxes = direct_checkboxes
                            all_paragraphs = [temp_paragraph]

            # 创建行栏目信息
            section_info = {
                'first_column': first_column,
                'row_text': row_text,
                'total_checkboxes': checkbox_count,
                'section_index': len(row_sections),
                'row_id': row_id
            }
            row_sections.append(section_info)

            # 保存行映射信息
            row_mapping[row_id] = {
                'checkboxes': all_checkboxes,
                'paragraphs': all_paragraphs,
                'section_index': len(row_sections) - 1,
                'start_index': sum(s['total_checkboxes'] for s in row_sections[:-1]),
                'table_context': {
                    'row_id': row_id,
                    'table_idx': table_idx,
                    'row_idx': row_idx,
                    'first_column': first_column,
                    'row_text': row_text,
                    'full_text': full_text,
                    'total_checkboxes': checkbox_count
                }
            }

            print(f"🚀 [BATCH模式] 收集行 {row_id}: {first_column} - {checkbox_count} 个复选框")

        # 使用LLM批量处理所有行
        if row_sections:
            try:
                print(f"🚀 [BATCH模式] 开始批量处理 {len(row_sections)} 个表格行...")
                print(f"🚀 [BATCH模式] 调用 LLMCheckboxHandler.process_combined_checkboxes 进行并发处理")
                combined_results = self.llm_handler.process_combined_checkboxes(row_sections)

                if combined_results:
                    print(f"🚀 [BATCH模式] 批量处理完成，返回 {len(combined_results)} 个结果")
                    print(f"🚀 [BATCH模式] ✅ 批量并发处理成功！")

                    # 将结果分配到各行的复选框
                    for row_id, mapping_info in row_mapping.items():
                        checkboxes = mapping_info['checkboxes']
                        paragraphs = mapping_info['paragraphs']
                        section_index = mapping_info['section_index']
                        start_index = mapping_info['start_index']
                        table_context = mapping_info['table_context']

                        if section_index < len(row_sections):
                            section = row_sections[section_index]
                            total_checkboxes = section['total_checkboxes']

                            # 提取该行的结果
                            end_index = start_index + total_checkboxes
                            if end_index <= len(combined_results):
                                row_results = combined_results[start_index:end_index]
                                print(f"行 {row_id} 结果: {row_results}")

                                # 将结果保存到表格上下文中
                                table_context["llm_results_queue"] = list(row_results)

                                # 为每个段落处理复选框
                                for i, paragraph in enumerate(paragraphs):
                                    para_checkboxes = self._get_checkboxes_in_paragraph(paragraph)
                                    if para_checkboxes:
                                        print(f"  处理段落 {i+1}/{len(paragraphs)}, 包含 {len(para_checkboxes)} 个复选框")
                                        self._process_checkboxes_with_existing_results(
                                            para_checkboxes, paragraph.text, table_context
                                        )
                            else:
                                print(f"警告: 行 {row_id} 结果索引范围超出")
                else:
                    print("批量处理未返回有效结果")

            except Exception as e:
                print(f"批量处理失败: {str(e)}")
                # 回退到顺序处理
                print("回退到顺序处理模式")
                self._process_tables_sequential(doc, table_rows_with_checkboxes)

    def _process_document_tables_combined(self, doc, table_rows_with_checkboxes):
        """
        合并处理表格中的复选框，将多个栏目合并到一个LLM请求中

        Args:
            doc: Word文档对象
            table_rows_with_checkboxes: 包含复选框的表格行信息
        """
        print("\n========== 开始合并处理表格中的复选框 ==========")

        if not table_rows_with_checkboxes:
            print("没有包含复选框的表格行需要处理")
            return

        # 收集所有复选框栏目信息
        checkbox_sections = []
        row_checkbox_mapping = {}  # 用于映射结果到具体的复选框对象

        for row_id, row_info in table_rows_with_checkboxes.items():
            table_idx = row_info["table_idx"]
            row_idx = row_info["row_idx"]
            first_column = row_info["first_column"]
            row_text = row_info["row_text"]
            full_text = row_info["full_text"]
            checkbox_count = row_info["checkbox_count"]

            print(f"\n收集栏目信息 - ID:{row_id}")
            print(f"  第一列内容: '{first_column}'")
            print(f"  行文本: '{row_text}'")
            print(f"  复选框数量: {checkbox_count}")

            # 收集该行的复选框对象
            row_checkboxes = []
            if table_idx < len(doc.tables) and row_idx < len(doc.tables[table_idx].rows):
                row = doc.tables[table_idx].rows[row_idx]

                # 遍历行中的所有单元格和段落，收集复选框对象
                for cell_idx, cell in enumerate(row.cells):
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        checkbox_chars = CHECKBOX_CHARS
                        has_checkbox = any(char in paragraph.text for char in checkbox_chars)

                        if has_checkbox:
                            checkboxes = self._get_checkboxes_in_paragraph(paragraph)
                            row_checkboxes.extend(checkboxes)

            # 如果没有找到复选框对象，创建模拟对象
            if not row_checkboxes and checkbox_count > 0:
                print(f"  未找到复选框对象，创建 {checkbox_count} 个模拟对象")
                for i in range(checkbox_count):
                    row_checkboxes.append({
                        "position": i,
                        "checkbox_char": "□",
                        "paragraph": None,
                        "row_id": row_id,
                        "table_idx": table_idx,
                        "row_idx": row_idx
                    })

            # 添加到栏目列表
            section_info = {
                'first_column': first_column,
                'row_text': row_text,
                'total_checkboxes': checkbox_count,
                'section_index': len(checkbox_sections),
                'row_id': row_id
            }
            checkbox_sections.append(section_info)

            # 保存复选框对象映射
            row_checkbox_mapping[row_id] = {
                'checkboxes': row_checkboxes,
                'section_index': len(checkbox_sections) - 1,
                'start_index': sum(s['total_checkboxes'] for s in checkbox_sections[:-1])
            }

            print(f"  已收集 {len(row_checkboxes)} 个复选框对象")

        print(f"\n总共收集到 {len(checkbox_sections)} 个复选框栏目")

        # 调用LLM进行合并处理
        if checkbox_sections:
            try:
                print("开始调用LLM进行合并处理...")
                combined_results = self.llm_handler.process_combined_checkboxes(checkbox_sections)

                if combined_results:
                    print(f"LLM返回合并结果，总共 {len(combined_results)} 个判断结果")

                    # 将结果分配到各个栏目的复选框
                    self._apply_combined_results(combined_results, row_checkbox_mapping, checkbox_sections)
                else:
                    print("LLM未返回有效结果")

            except Exception as e:
                error_msg = f"合并处理失败: {str(e)}"
                print(error_msg)
                import traceback
                print(traceback.format_exc())
                # 检查是否是LLM API相关错误，如果是则重新抛出以便上层处理
                if "LLM API调用失败" in str(e) or "timeout" in str(e).lower() or "connection" in str(e).lower():
                    raise Exception(f"复选框处理中LLM API调用失败: {str(e)}")

        print("========== 合并处理完成 ==========\n")

    def _process_document_tables_combined_by_table(self, doc, table_rows_with_checkboxes):
        """
        按表格合并处理复选框，将同一个表格内的复选框合并到一个LLM请求中

        Args:
            doc: Word文档对象
            table_rows_with_checkboxes: 包含复选框的表格行信息
        """
        print("\n========== 开始按表格合并处理复选框 ==========")

        if not table_rows_with_checkboxes:
            print("没有包含复选框的表格行需要处理")
            return

        # 按表格索引分组行信息
        tables_with_checkboxes = {}
        for row_id, row_info in table_rows_with_checkboxes.items():
            table_idx = row_info["table_idx"]
            if table_idx not in tables_with_checkboxes:
                tables_with_checkboxes[table_idx] = {}
            tables_with_checkboxes[table_idx][row_id] = row_info

        print(f"发现 {len(tables_with_checkboxes)} 个包含复选框的表格")

        # 逐个表格处理
        for table_idx, table_rows in tables_with_checkboxes.items():
            print(f"\n处理表格 {table_idx}，包含 {len(table_rows)} 行复选框")
            self._process_single_table_combined(doc, table_idx, table_rows)

        print("========== 按表格合并处理完成 ==========\n")

    def _process_single_table_combined(self, doc, table_idx, table_rows):
        """
        处理单个表格内的所有复选框

        Args:
            doc: Word文档对象
            table_idx: 表格索引
            table_rows: 该表格内包含复选框的行信息
        """
        print(f"\n--- 开始处理表格 {table_idx} ---")

        # 收集该表格内所有复选框栏目信息
        checkbox_sections = []
        row_checkbox_mapping = {}  # 用于映射结果到具体的复选框对象

        for row_id, row_info in table_rows.items():
            row_idx = row_info["row_idx"]
            first_column = row_info["first_column"]
            row_text = row_info["row_text"]
            full_text = row_info["full_text"]
            checkbox_count = row_info["checkbox_count"]

            print(f"  收集行 {row_idx} 的复选框信息")
            print(f"    栏目: {first_column}")
            print(f"    内容: {row_text[:100]}{'...' if len(row_text) > 100 else ''}")
            print(f"    复选框数量: {checkbox_count}")

            # 收集该行的复选框对象
            row_checkboxes = []
            if table_idx < len(doc.tables) and row_idx < len(doc.tables[table_idx].rows):
                row = doc.tables[table_idx].rows[row_idx]

                # 遍历行中的所有单元格和段落，收集复选框对象
                for cell_idx, cell in enumerate(row.cells):
                    for para_idx, paragraph in enumerate(cell.paragraphs):
                        checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓", "⬜", "⬛", "▢", "▣", "▪", "▫"]
                        has_checkbox = any(char in paragraph.text for char in checkbox_chars)

                        if has_checkbox:
                            checkboxes = self._get_checkboxes_in_paragraph(paragraph)
                            row_checkboxes.extend(checkboxes)

            if row_checkboxes:
                # 创建栏目信息
                section_info = {
                    'first_column': first_column,
                    'row_text': row_text,
                    'total_checkboxes': len(row_checkboxes),
                    'section_index': len(checkbox_sections)  # 当前栏目在列表中的索引
                }

                checkbox_sections.append(section_info)

                # 保存复选框映射信息
                row_checkbox_mapping[row_id] = {
                    'checkboxes': row_checkboxes,
                    'section_index': len(checkbox_sections) - 1,
                    'start_index': sum(section.get('total_checkboxes', 0) for section in checkbox_sections[:-1])
                }

                print(f"    添加到合并处理队列，起始索引: {row_checkbox_mapping[row_id]['start_index']}")

        print(f"  表格 {table_idx} 总共收集到 {len(checkbox_sections)} 个复选框栏目")

        # 调用LLM进行该表格的合并处理
        if checkbox_sections:
            try:
                print(f"  开始调用LLM处理表格 {table_idx} 的复选框...")
                combined_results = self.llm_handler.process_combined_checkboxes(checkbox_sections)

                if combined_results:
                    print(f"  LLM返回表格 {table_idx} 的合并结果，总共 {len(combined_results)} 个判断结果")

                    # 将结果分配到各个栏目的复选框
                    self._apply_combined_results(combined_results, row_checkbox_mapping, checkbox_sections)
                else:
                    print(f"  表格 {table_idx} LLM未返回有效结果")

            except Exception as e:
                error_msg = f"表格 {table_idx} 合并处理失败: {str(e)}"
                print(error_msg)
                import traceback
                print(traceback.format_exc())
                # 检查是否是LLM API相关错误，如果是则重新抛出以便上层处理
                if "LLM API调用失败" in str(e) or "timeout" in str(e).lower() or "connection" in str(e).lower():
                    raise Exception(f"表格 {table_idx} 复选框处理中LLM API调用失败: {str(e)}")

        print(f"--- 表格 {table_idx} 处理完成 ---")

    def _apply_combined_results(self, combined_results, row_checkbox_mapping, checkbox_sections):
        """
        将合并处理的结果应用到各个复选框

        Args:
            combined_results: LLM返回的合并结果
            row_checkbox_mapping: 行复选框映射
            checkbox_sections: 复选框栏目信息
        """
        print("\n开始应用合并处理结果...")

        for row_id, mapping_info in row_checkbox_mapping.items():
            checkboxes = mapping_info['checkboxes']
            section_index = mapping_info['section_index']
            start_index = mapping_info['start_index']

            if section_index < len(checkbox_sections):
                section = checkbox_sections[section_index]
                total_checkboxes = section['total_checkboxes']

                print(f"\n处理栏目: {section['first_column']}")
                print(f"  复选框数量: {total_checkboxes}")
                print(f"  结果起始索引: {start_index}")

                # 提取该栏目的结果
                end_index = start_index + total_checkboxes
                if end_index <= len(combined_results):
                    section_results = combined_results[start_index:end_index]
                    print(f"  栏目结果: {section_results}")

                    # 应用结果到复选框
                    for i, should_check in enumerate(section_results):
                        if i < len(checkboxes):
                            checkbox = checkboxes[i]
                            self._set_checkbox_state(checkbox, should_check)
                            print(f"    复选框 #{i+1} 设置为: {'勾选' if should_check else '不勾选'}")
                        else:
                            print(f"    警告: 复选框索引 {i} 超出范围")
                else:
                    print(f"  警告: 结果索引范围 [{start_index}:{end_index}] 超出合并结果长度 {len(combined_results)}")

        print("合并处理结果应用完成")

    def _get_checkboxes_in_paragraph_from_runs(self, paragraph, row_runs):
        """
        从段落文本中获取复选框对象，不再依赖run对象

        Args:
            paragraph: 段落对象
            row_runs: 行中的run列表（仅用于获取完整文本，不再遍历）

        Returns:
            复选框对象列表
        """
        text_checkboxes = []

        # 获取段落文本
        paragraph_text = paragraph.text if hasattr(paragraph, "text") else ""
        if not paragraph_text and row_runs:
            paragraph_text = "".join([run_info["text"]
                                     for run_info in row_runs])

        print(
            f"从段落文本中查找复选框，段落文本: '{paragraph_text[:50]}{'...' if len(paragraph_text) > 50 else ''}'")

        # 使用统一的复选框字符定义
        checkbox_chars = CHECKBOX_CHARS

        # 直接在段落文本中查找复选框字符
        for char in checkbox_chars:
            positions = [pos for pos, c in enumerate(
                paragraph_text) if c == char]
            for pos in positions:
                # 获取复选框前后的文本
                text_before = paragraph_text[:pos] if pos > 0 else ""
                text_after = paragraph_text[pos +
                                            1:] if pos < len(paragraph_text) - 1 else ""

                # 创建模拟复选框对象
                text_checkbox = {
                    "paragraph": paragraph,  # 保存段落对象，用于后续修改
                    "position": pos,
                    "text_before": text_before,
                    "text_after": text_after,
                    "full_text": paragraph_text,
                    "checkbox_char": char,
                }
                text_checkboxes.append(text_checkbox)
                print(
                    f"找到模拟复选框 - 位置: {pos}, 字符: '{char}', 前文本: '{text_before[:20]}{'...' if len(text_before) > 20 else ''}', 后文本: '{text_after[:20]}{'...' if len(text_after) > 20 else ''}'")

        # 检查整个段落文本中的复选框字符数量
        total_checkbox_chars = sum(paragraph_text.count(char)
                                   for char in checkbox_chars)

        print(
            f"段落文本中检测到 {total_checkbox_chars} 个复选框字符，已识别 {len(text_checkboxes)} 个模拟复选框对象")

        # 最终检查：确保创建的复选框对象数量与文本中的复选框字符数量一致
        final_checkbox_count = len(text_checkboxes)
        if final_checkbox_count != total_checkbox_chars:
            print(
                f"警告: 最终创建的复选框对象数量({final_checkbox_count})与文本中的复选框字符数量({total_checkbox_chars})不一致")

        return text_checkboxes

    def _get_checkboxes_in_paragraph(self, paragraph):
        """获取段落中的所有模拟复选框字符"""
        text_checkboxes = []

        # 记录段落信息
        paragraph_text = paragraph.text

        # 使用统一的复选框字符定义
        checkbox_chars = CHECKBOX_CHARS

        print(
            f"段落文本: '{paragraph_text[:50]}{'...' if len(paragraph_text) > 50 else ''}'"
        )

        # 直接在段落文本中查找复选框字符
        for char in checkbox_chars:
            positions = [pos for pos, c in enumerate(
                paragraph_text) if c == char]
            for pos in positions:
                # 获取复选框前后的文本
                text_before = paragraph_text[:pos] if pos > 0 else ""
                text_after = (
                    paragraph_text[pos +
                                   1:] if pos < len(paragraph_text) - 1 else ""
                )

                # 创建模拟复选框对象
                text_checkbox = {
                    "paragraph": paragraph,  # 保存段落对象，用于后续修改
                    "position": pos,
                    "text_before": text_before,
                    "text_after": text_after,
                    "full_text": paragraph_text,
                    "checkbox_char": char,
                }
                text_checkboxes.append(text_checkbox)
                print(
                    f"找到模拟复选框 - 位置: {pos}, 字符: '{char}', 前文本: '{text_before[:20]}{'...' if len(text_before) > 20 else ''}', 后文本: '{text_after[:20]}{'...' if len(text_after) > 20 else ''}'"
                )

        # 检查整个段落文本中的复选框字符数量
        total_checkbox_chars = sum(
            paragraph_text.count(char) for char in checkbox_chars
        )

        print(
            f"段落文本中检测到 {total_checkbox_chars} 个复选框字符，已识别 {len(text_checkboxes)} 个模拟复选框对象"
        )

        # 最终检查：确保创建的复选框对象数量与文本中的复选框字符数量一致
        final_checkbox_count = len(text_checkboxes)
        if final_checkbox_count != total_checkbox_chars:
            print(
                f"警告: 最终创建的复选框对象数量({final_checkbox_count})与文本中的复选框字符数量({total_checkbox_chars})不一致"
            )

        print(f"在段落中找到 {len(text_checkboxes)} 个模拟复选框字符")
        return text_checkboxes

    def _set_checkbox_state(self, checkbox, should_check):
        """设置复选框的勾选状态

        Args:
            checkbox: 模拟复选框对象
            should_check: 是否应该勾选
        """
        try:
            # 获取段落对象和复选框位置
            paragraph = checkbox.get("paragraph")
            position = checkbox.get("position", -1)
            checkbox_char = checkbox.get("checkbox_char", "□")

            if paragraph and position >= 0:
                # 获取段落文本
                paragraph_text = paragraph.text

                # 检查位置是否有效
                if position < len(paragraph_text):
                    # 检查当前位置是否为复选框字符
                    current_char = paragraph_text[position]
                    print(f"当前位置({position})的字符是: '{current_char}'")

                    # 使用统一的复选框字符定义
                    checkbox_chars = CHECKBOX_CHARS
                    if current_char in checkbox_chars:
                        # 当前位置确实是复选框字符，使用安全字符替换
                        check_symbol = get_safe_checkbox_char(current_char, should_check)

                        # 创建新文本时只替换指定位置的字符
                        new_text_chars = list(paragraph_text)
                        new_text_chars[position] = check_symbol
                        new_text = "".join(new_text_chars)

                        # 记录修改详情
                        print(f"模拟复选框精确替换:")
                        print(f"  原文本: '{paragraph_text}'")
                        print(f"  新文本: '{new_text}'")
                        print(f"  位置: {position}")
                        print(f"  原字符: '{current_char}'")
                        print(f"  新字符: '{check_symbol}'")

                        # 更新段落文本
                        # 注意：直接修改paragraph.text可能不会生效，需要修改段落中的run
                        # 这里我们尝试一种简单的方法：清空所有run，然后添加一个新的run
                        try:
                            # 清空段落中的所有run
                            while len(paragraph.runs) > 0:
                                paragraph._p.remove(paragraph.runs[0]._element)

                            # 添加一个新的run，包含更新后的文本
                            paragraph.add_run(new_text)
                            print(
                                f"复选框状态已更新为: {'已勾选' if should_check else '未勾选'}"
                            )
                        except Exception as e:
                            print(f"更新段落文本时出错: {str(e)}")
                            import traceback

                            print(traceback.format_exc())
                    else:
                        print(f"警告: 位置 {position} 不是复选框字符: '{current_char}'")
                else:
                    print(
                        f"警告: 模拟复选框位置超出范围: {position}, 文本长度: {len(paragraph_text)}"
                    )
            else:
                print(f"警告: 无法获取段落对象或位置无效")
        except Exception as e:
            print(f"设置复选框状态时出错: {str(e)}")
            import traceback

            print(traceback.format_exc())

    def _extract_checkbox_context(self, checkbox, paragraph_text, table_context=None):
        """提取单个复选框的上下文信息

        Args:
            checkbox: 模拟复选框对象
            paragraph_text: 段落文本
            table_context: 表格上下文信息，包含行标识符等

        Returns:
            包含复选框上下文信息的字典，包括：
            - text_before: 复选框前的文本
            - text_after: 复选框后的文本
            - full_text: 完整段落文本
            - first_column_content: 如果在表格中，表示所在行的第一列内容
            - row_content: 如果在表格中，表示所在行的完整内容
            - row_id: 如果在表格中，表示行的唯一标识符
            - checkbox_char: 复选框字符

        注意：对于表格中的复选框，我们提取的是整个表格行的内容，以确保同一行上的所有复选框都能被正确处理。
        """
        # 初始化上下文字典
        context = {"full_text": paragraph_text}

        # 提取模拟复选框的上下文信息
        # 获取复选框前后的文本
        text_before = checkbox.get("text_before", "")
        text_after = checkbox.get("text_after", "")
        checkbox_char = checkbox.get("checkbox_char", "□")

        # 添加到上下文字典
        context["text_before"] = text_before
        context["text_after"] = text_after
        context["checkbox_char"] = checkbox_char
        context["position"] = checkbox.get("position", -1)

        # 添加表格上下文（如果在表格中）
        if table_context:
            context["is_in_table"] = True
            context["first_column_content"] = table_context.get(
                "first_column", "")
            context["row_content"] = table_context.get("row_text", "")
            context["row_id"] = table_context.get("row_id", "")
            context["table_id"] = table_context.get("table_id", "")

            # 如果有表格标题，也包含它
            if table_context.get("table_title"):
                context["table_title"] = table_context.get("table_title", "")

        # 打印提取的上下文信息
        print(f"\n提取复选框上下文:")
        print(f"  字符: '{context.get('checkbox_char', '□')}'")
        print(
            f"  前文本: '{context.get('text_before', '')[:30]}{'...' if len(context.get('text_before', '')) > 30 else ''}'"
        )
        print(
            f"  后文本: '{context.get('text_after', '')[:30]}{'...' if len(context.get('text_after', '')) > 30 else ''}'"
        )

        if "first_column_content" in context:
            print(f"  表格第一列: '{context['first_column_content']}'")
        if "row_content" in context:
            print(
                f"  表格行内容: '{context['row_content'][:50]}{'...' if len(context['row_content']) > 50 else ''}'"
            )

        return context

    def _process_nested_tables(self, parent_table, parent_idx, table_row_info):
        """
        递归处理嵌套表格中的复选框

        Args:
            parent_table: 父表格对象
            parent_idx: 父表格索引
            table_row_info: 存储表格行信息的字典

        Returns:
            更新后的table_row_info
        """
        try:
            # 检查单元格中是否有嵌套表格
            nested_table_idx = 0
            for row_idx, row in enumerate(parent_table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    # 检查单元格中是否有表格
                    # 注意：python-docx不直接支持获取单元格中的表格
                    # 这里使用一种变通方法，检查单元格的XML内容
                    if hasattr(cell, "_element") and cell._element is not None:
                        cell_xml = cell._element.xml
                        if "<w:tbl>" in cell_xml:
                            print(
                                f"发现嵌套表格: 父表格索引 {parent_idx}, 行 {row_idx}, 单元格 {cell_idx}"
                            )

                            # 由于python-docx的限制，我们无法直接访问嵌套表格
                            # 这里仅提示发现了嵌套表格，但无法直接处理
                            # 在实际应用中，可能需要使用更底层的XML处理来解析嵌套表格

                            # 增加嵌套表格计数
                            nested_table_idx += 1

                            # 记录警告
                            print(
                                f"警告: 发现嵌套表格，但由于python-docx的限制，无法直接处理嵌套表格中的复选框"
                            )
                            print(
                                f"建议将嵌套表格拆分为独立表格，以确保所有复选框都能被正确处理"
                            )
        except Exception as e:
            logger.error(f"处理嵌套表格时出错: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())

        return table_row_info

    def _identify_checkbox_sequence(self, run_text, position):
        """
        识别连续选项中的复选框序列，如选项1□/选项2□/选项3□

        Args:
            run_text: run的文本内容
            position: 当前复选框的位置

        Returns:
            所有连续复选框的位置列表和对应的标签
        """
        try:
            # 增加调试日志
            print(f"\n===== 识别复选框序列 =====")
            print(f"原始文本: '{run_text}'")
            print(f"当前复选框位置: {position}")

            # 使用通用的方法检测选项组合
            # 1. 检查是否有斜杠连接的选项组合模式
            slash_pattern = r"□/|☐/|☑/"
            has_slash_pattern = bool(re.search(slash_pattern, run_text))

            # 2. 检测是否存在选项组字符模式（如多个复选框并列）
            checkbox_chars = CHECKBOX_CHARS
            checkbox_positions = [
                i for i, char in enumerate(run_text) if char in checkbox_chars
            ]
            checkbox_count = len(checkbox_positions)

            # 如果有至少两个复选框，并且它们的距离合适，可能是选项组
            has_group_pattern = False
            if checkbox_count >= 2:
                # 计算复选框间的平均距离
                if len(checkbox_positions) >= 2:
                    distances = [
                        checkbox_positions[i + 1] - checkbox_positions[i]
                        for i in range(len(checkbox_positions) - 1)
                    ]
                    avg_distance = sum(distances) / len(distances)
                    # 如果平均距离较小，可能是选项组
                    has_group_pattern = (
                        avg_distance < 30
                    )  # 30是一个经验值，可以根据实际情况调整

            # 组合判断结果
            has_pattern = has_slash_pattern or has_group_pattern

            if has_pattern:
                print(
                    f"检测到选项组合模式: {'斜杠分隔' if has_slash_pattern else ''}{'选项组' if has_group_pattern else ''}"
                )
                print(f"选项组合文本: '{run_text}'")

                if len(checkbox_positions) > 1:
                    print(
                        f"找到 {len(checkbox_positions)} 个复选框字符，位置: {checkbox_positions}"
                    )

                    # 尝试确定当前复选框在序列中的顺序
                    if position in checkbox_positions:
                        idx = checkbox_positions.index(position)
                        print(
                            f"当前复选框在序列中的位置: {idx+1}/{len(checkbox_positions)}"
                        )

                    # 获取每个复选框的标签
                    labels = []
                    for pos in checkbox_positions:
                        # 尝试获取复选框前面的标签
                        label_start = max(0, pos - 20)  # 向前搜索最多20个字符
                        label_text = run_text[label_start:pos]

                        # 获取选项前后的完整上下文
                        context_start = max(0, pos - 30)
                        context_end = min(len(run_text), pos + 30)
                        context = run_text[context_start:context_end]
                        print(f"复选框位置 {pos} 的上下文: '{context}'")

                        # 尝试提取标签（最后一个非空白字符序列）
                        # 先尝试通用的正则提取方法
                        label_match = re.search(r"([^\s/]+)[□☐☑]", label_text)
                        if label_match:
                            label = label_match.group(1)
                            labels.append(label)
                            print(f"通过正则提取标签: {label}")
                        else:
                            # 如果通用方法失败，使用位置关系提取
                            # 查找最后一个非空白字符序列
                            non_space_match = re.search(
                                r"(\S+)\s*$", label_text)
                            if non_space_match:
                                label = non_space_match.group(1)
                                labels.append(label)
                                print(f"提取最后非空白序列作为标签: {label}")
                            else:
                                # 最后的后备方案：使用索引作为标签
                                label = f"选项{len(labels)+1}"
                                labels.append(label)
                                print(f"使用默认索引作为标签: {label}")

                    print(f"识别到的标签: {labels}")

                    # 针对同类型选项序列的智能优化
                    if has_slash_pattern and len(checkbox_positions) >= 2:
                        print("优化斜杠分隔的选项组标签...")

                        # 查找所有斜杠的位置
                        slash_positions = [
                            i for i, char in enumerate(run_text) if char == "/"
                        ]

                        # 优化标签：检查复选框与斜杠的位置关系
                        for i, pos in enumerate(checkbox_positions):
                            if i < len(checkbox_positions) - 1:  # 不是最后一个复选框
                                # 检查这个复选框后面是否有斜杠
                                next_slash = next(
                                    (s for s in slash_positions if s > pos), None
                                )
                                if (
                                    next_slash
                                    and next_slash < checkbox_positions[i + 1]
                                ):
                                    # 如果在下一个复选框之前有斜杠，说明这些是选项组
                                    # 使用复选框前的文本到斜杠作为新标签
                                    pre_text = run_text[max(0, pos - 10): pos]
                                    # 提取最后一个非空白序列
                                    label_match = re.search(
                                        r"(\S+)\s*$", pre_text)
                                    if label_match:
                                        labels[i] = f"前项_{i+1}"
                                        print(
                                            f"优化斜杠前标签: 位置{pos}的标签改为'前项_{i+1}'"
                                        )

                                    # 查看斜杠后的文本作为下一个标签
                                    if i + 1 < len(labels):
                                        post_text = run_text[
                                            next_slash + 1: checkbox_positions[i + 1]
                                        ]
                                        # 提取第一个非空白序列
                                        label_match = re.search(
                                            r"^\s*(\S+)", post_text)
                                        if label_match:
                                            labels[i + 1] = f"后项_{i+1}"
                                            print(
                                                f"优化斜杠后标签: 位置{checkbox_positions[i+1]}的标签改为'后项_{i+1}'"
                                            )

                    # 检查是否为斜杠分隔的选项组
                    if "/" in run_text:
                        print(f"检测到斜杠分隔的选项组！")
                        # 打印选项组合的完整调试信息
                        options_info = []
                        for i, (pos, label) in enumerate(
                            zip(checkbox_positions, labels)
                        ):
                            char_at_pos = run_text[pos] if pos < len(
                                run_text) else "?"
                            option_info = (
                                f"选项{i+1}: {label} 在位置{pos} 字符='{char_at_pos}'"
                            )
                            options_info.append(option_info)
                        print(f"选项组详情: {', '.join(options_info)}")

                    return checkbox_positions, labels
            else:
                print(f"未检测到选项组合模式，这似乎是独立复选框")
        except Exception as e:
            print(f"识别复选框序列时出错: {str(e)}")
            import traceback

            print(traceback.format_exc())

        # 如果没有识别到序列，或者发生错误，返回空列表
        return [], []

    def _process_checkboxes_with_existing_results(self, checkboxes, paragraph_text, table_context):
        """
        使用表格上下文中已有的LLM结果队列处理复选框

        Args:
            checkboxes: 复选框对象列表
            paragraph_text: 段落文本
            table_context: 表格上下文，包含LLM结果队列
        """
        if not checkboxes or len(checkboxes) == 0:
            print("没有复选框需要处理")
            return

        # 获取结果队列
        results_queue = table_context.get("llm_results_queue", [])

        if not results_queue:
            print("警告: 表格上下文中的结果队列为空，无法处理复选框")
            return

        print(f"\n========== 使用已有结果队列处理复选框 ==========")
        print(f"复选框数量: {len(checkboxes)}")
        print(f"结果队列长度: {len(results_queue)}")

        # 确保复选框按位置排序
        checkboxes.sort(key=lambda x: x.get("position", -1))

        # 处理所有复选框
        for i, checkbox in enumerate(checkboxes):
            # 从队列中消耗一个值
            should_check = False  # 默认值
            if results_queue:
                should_check = results_queue.pop(0)  # 从队列头部弹出值
            else:
                print(f"警告: 结果队列已空，但还有复选框需要处理，使用默认值False")

            # 获取复选框信息
            position = checkbox.get("position", -1)
            char = checkbox.get("checkbox_char", "□")

            # 获取上下文
            paragraph = checkbox.get("paragraph")
            para_text = paragraph.text if paragraph else ""
            context_start = max(0, position - 10)
            context_end = min(len(para_text), position + 10)
            context = para_text[context_start:context_end] if position >= 0 and position < len(para_text) else ""

            # 记录详细日志
            result_str = "勾选" if should_check else "不勾选"
            print(f"\n处理复选框 {i+1}/{len(checkboxes)}: {result_str}")
            print(f"  位置: {position}")
            print(f"  字符: '{char}'")
            print(f"  上下文: '{context}'")
            print(f"  剩余队列: {results_queue}")

            # 设置复选框状态
            self._set_checkbox_state(checkbox, should_check)

        # 更新表格上下文中的结果队列
        table_context["llm_results_queue"] = results_queue

        # 检查是否还有未消耗的真值
        if results_queue:
            print(f"警告: 所有复选框都已处理，但结果队列中还有 {len(results_queue)} 个未消耗的值: {results_queue}")

    def _process_checkboxes_in_same_row(
        self, checkboxes, paragraph_text, table_context=None, return_results=False
    ):
        """
        处理同一行中的所有复选框

        根据配置决定是否使用LLM处理器或规则引擎处理器
        特别注意：对于表格中的复选框，我们使用表格行的完整内容作为上下文，以确保同一行上的所有复选框都能被正确处理。

        Args:
            checkboxes: 复选框对象列表
            paragraph_text: 段落文本
            table_context: 表格上下文
            return_results: 是否返回LLM结果，而不是直接应用

        Returns:
            如果return_results为True，返回LLM结果列表；否则返回None
        """
        # 检查是否没有复选框需要处理
        if not checkboxes or len(checkboxes) == 0:
            print("没有复选框需要处理")
            return [] if return_results else None

        # 获取行文本和第一列内容以检查当事人类型
        row_text = ""
        first_column = ""

        if table_context:
            row_text = table_context.get("row_text", "")
            first_column = table_context.get("first_column", "")
            full_row_text = f"{first_column} {row_text}"
        else:
            full_row_text = paragraph_text

        # 检查需要跳过的复选框，比如法人当事人相关的复选框
        skip_patterns = [
            '原告(法人、非法人组织)', '原告（法人、非法人组织）',
            '被告(法人、非法人组织)', '被告（法人、非法人组织）',
            '第三人(法人、非法人组织)', '第三人（法人、非法人组织）',
            '委托代理人', '委托诉讼代理人', '是否申请财产保全'
        ]

        # 检查第一列或整行文本是否包含法人当事人标识
        is_skip = False
        if first_column and any(pattern in first_column for pattern in skip_patterns):
            is_skip = True
        elif any(pattern in full_row_text for pattern in skip_patterns):
            is_skip = True

        if is_skip:
            print(f"检测到跳过复选框，自动处理: {full_row_text[:100]}")
            # 如果是跳过相关的复选框，直接返回所有False
            if return_results:
                return [False] * len(checkboxes)
            else:
                # 直接将所有复选框设置为不勾选
                for checkbox in checkboxes:
                    self._set_checkbox_state(checkbox, False)
                return None

        # 检查是否启用了LLM处理
        llm_processed = False
        use_llm = self.config.get("use_llm_for_checkbox", False)

        if use_llm:
            try:
                print(f"尝试使用LLM处理同一行的 {len(checkboxes)} 个模拟复选框对象")

                # 简化：直接使用表格行文本作为上下文
                # 计算行文本中的复选框字符数量
                checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓", "⬜", "⬛", "▢", "▣", "▪", "▫"]
                char_positions = []

                # 查找所有复选框字符的位置
                for char in checkbox_chars:
                    positions = [pos for pos, c in enumerate(full_row_text) if c == char]
                    for pos in positions:
                        char_positions.append((pos, char))

                # 按位置排序
                char_positions.sort(key=lambda x: x[0])

                # 计算实际的复选框字符数量
                actual_checkbox_count = len(char_positions)

                print(f"行文本中检测到 {actual_checkbox_count} 个复选框字符，当前处理的复选框对象数量: {len(checkboxes)}")

                # 构建简化的行上下文
                row_context = {
                    "row_text": row_text,
                    "first_column": first_column,
                    "full_text": full_row_text,
                    "total_checkboxes": actual_checkbox_count  # 使用实际的复选框字符数量
                }

                print(f"使用简化的行上下文: {row_context}")
                print(f"将请求LLM返回 {actual_checkbox_count} 个结果，对应 {actual_checkbox_count} 个复选框字符")

                # 调用LLM处理器获取行级别复选框的状态
                results = self.llm_handler.process_row_checkboxes(
                    row_context,  # 使用简化的行上下文
                    None,  # 不再传递复选框上下文
                    full_row_text,  # 传递完整的行文本
                    None  # 不传递表格上下文
                )

                if not results or len(results) == 0:
                    print("LLM返回的结果为空")
                    return [] if return_results else None

                # 如果只需要返回结果，不应用
                if return_results:
                    print(f"返回LLM结果: {results}")
                    return results

                # 创建真值队列 - 新增代码
                original_results = list(results)  # 保存原始结果用于调试
                results_queue = list(results)     # 使用列表模拟队列

                print(f"LLM返回了 {len(results)} 个结果: {original_results}")
                print(f"创建真值队列，初始长度: {len(results_queue)}")

                # 确保复选框按位置排序 - 新增代码
                checkboxes.sort(key=lambda x: x.get("position", -1))

                # 应用结果到所有复选框对象 - 使用队列消耗方式
                print("\n========== 应用LLM结果（队列消耗方式）==========")

                # 处理所有复选框 - 新的队列消耗方式
                for i, checkbox in enumerate(checkboxes):
                    # 从队列中消耗一个值
                    should_check = False  # 默认值
                    if results_queue:
                        should_check = results_queue.pop(0)  # 从队列头部弹出值
                    else:
                        print(f"警告: 真值队列已空，但还有复选框需要处理，使用默认值False")

                    # 获取复选框信息
                    position = checkbox.get("position", -1)
                    char = checkbox.get("checkbox_char", "□")

                    # 获取上下文
                    paragraph = checkbox.get("paragraph")
                    para_text = paragraph.text if paragraph else ""
                    context_start = max(0, position - 10)
                    context_end = min(len(para_text), position + 10)
                    context = para_text[context_start:context_end] if position >= 0 and position < len(para_text) else ""

                    # 记录详细日志
                    result_str = "勾选" if should_check else "不勾选"
                    print(f"\n处理复选框 {i+1}/{len(checkboxes)}: {result_str}")
                    print(f"  位置: {position}")
                    print(f"  字符: '{char}'")
                    print(f"  上下文: '{context}'")
                    print(f"  剩余队列: {results_queue}")

                    # 设置复选框状态
                    self._set_checkbox_state(checkbox, should_check)

                # 检查是否还有未消耗的真值 - 新增代码
                if results_queue:
                    print(f"警告: 所有复选框都已处理，但真值队列中还有 {len(results_queue)} 个未消耗的值: {results_queue}")

                llm_processed = True

            except Exception as e:
                import traceback
                print(f"LLM处理复选框时出错: {str(e)}")
                print(traceback.format_exc())

        # 如果LLM处理失败或未启用，尝试使用规则引擎
        use_rule_engine = self.config.get('use_rule_engine_for_checkbox', False)
        if not llm_processed and use_rule_engine:
            print("LLM处理失败或未启用，尝试使用规则引擎")
            # 使用规则引擎处理复选框
            rule_results = []

            # 如果只需要返回结果，收集规则引擎的结果
            if return_results:
                for i, checkbox in enumerate(checkboxes):
                    # 简化：不再提取复杂的上下文
                    should_check = self.rule_handler.should_check_checkbox(
                        {"position": i},  # 只传递位置信息
                        paragraph_text,
                        table_context
                    )
                    rule_results.append(should_check)
                print(f"规则引擎返回结果: {rule_results}")
                return rule_results
            else:
                # 直接应用规则引擎结果
                for i, checkbox in enumerate(checkboxes):
                    # 简化：不再提取复杂的上下文
                    should_check = self.rule_handler.should_check_checkbox(
                        {"position": i},  # 只传递位置信息
                        paragraph_text,
                        table_context
                    )
                    self._set_checkbox_state(checkbox, should_check)
                    print(f"规则引擎设置复选框 #{i+1} 状态为: {should_check}")
        elif not llm_processed and not use_rule_engine:
            logger.warning("LLM处理失败且规则引擎未启用，复选框将保持原始状态")

        if return_results:
            return []  # 如果LLM处理失败，返回空列表
        return None

    def _process_checkboxes_individually(self, checkboxes, paragraph, table_context=None):
        """
        逐个处理复选框

        Args:
            checkboxes: 复选框对象列表
            paragraph: 段落对象
            table_context: 表格上下文信息（如果复选框在表格中）
        """
        if not checkboxes or len(checkboxes) == 0:
            print("没有复选框需要处理")
            return

        paragraph_text = paragraph.text
        print(f"逐个处理 {len(checkboxes)} 个复选框")

        # 获取相关文本用于检查当事人类型
        row_text = ""
        first_column = ""

        if table_context:
            row_text = table_context.get("row_text", "")
            first_column = table_context.get("first_column", "")
            full_text = f"{first_column} {row_text}"
        else:
            full_text = paragraph_text

        # 检查是否是法人当事人相关的复选框
        skip_patterns = [
            '原告(法人、非法人组织)', '原告（法人、非法人组织）',
            '被告(法人、非法人组织)', '被告（法人、非法人组织）',
            '第三人(法人、非法人组织)', '第三人（法人、非法人组织）'
        ]

        # 检查文本是否包含法人当事人标识
        if any(pattern in full_text for pattern in skip_patterns) or \
           (first_column and any(pattern in first_column for pattern in skip_patterns)):
            print(f"检测到法人当事人复选框，自动全部不勾选: {full_text[:100]}")
            # 直接将所有复选框设置为不勾选
            for checkbox in checkboxes:
                self._set_checkbox_state(checkbox, False)
            print("法人当事人复选框已全部设置为不勾选")
            return

        # 检查是否启用了LLM处理
        use_llm = self.config.get('use_llm_for_checkbox', False)

        for i, checkbox in enumerate(checkboxes):
            print(f"\n处理复选框 #{i+1}/{len(checkboxes)}")

            # 决定是否勾选
            should_check = False

            if use_llm:
                try:
                    print("使用LLM处理单个复选框")
                    # 简化：只传递位置信息
                    checkbox_context = {"position": i}

                    # 构建简化的行上下文
                    row_context = None
                    if table_context:
                        row_context = {
                            'row_text': table_context.get('row_text', ''),
                            'first_column': table_context.get('first_column', ''),
                            'total_checkboxes': 1  # 只处理一个复选框
                        }

                    # 调用LLM处理器
                    results = self.llm_handler.process_row_checkboxes(
                        row_context,
                        None,  # 不再传递复选框上下文
                        paragraph_text,
                        table_context
                    )

                    if results and len(results) > 0:
                        should_check = results[0]
                        print(f"LLM决定: {'勾选' if should_check else '不勾选'}")
                    else:
                        print("LLM返回空结果，将使用规则引擎")

                except Exception as e:
                    print(f"LLM处理复选框时出错: {str(e)}")
                    import traceback
                    print(traceback.format_exc())

            # 如果LLM处理失败或未启用，尝试使用规则引擎
            use_rule_engine = self.config.get('use_rule_engine_for_checkbox', False)
            if (not use_llm or not should_check) and use_rule_engine:
                print("使用规则引擎处理复选框")
                # 简化：只传递位置信息
                should_check = self.rule_handler.should_check_checkbox(
                    {"position": i},
                    paragraph_text,
                    table_context
                )
                print(f"规则引擎决定: {'勾选' if should_check else '不勾选'}")

            # 设置复选框状态
            self._set_checkbox_state(checkbox, should_check)
            print(f"复选框 #{i+1} 状态已设置为: {'已勾选' if should_check else '未勾选'}")

        print("\n所有复选框处理完成")
