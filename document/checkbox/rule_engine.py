"""
规则引擎复选框处理器
负责使用基于规则的方法进行复选框判断
"""
import logging
import re
from typing import Dict, Any, List, Tuple, Optional, Set

logger = logging.getLogger(__name__)

class RuleBasedCheckboxHandler:
    """基于规则的复选框处理器"""
    
    def __init__(self, case_data: Dict[str, Any], config: Dict[str, Any]):
        """
        初始化规则引擎复选框处理器
        
        Args:
            case_data: 案件数据
            config: 配置信息
        """
        self.case_data = case_data
        self.config = config
        
        # 获取案件类型
        self.case_type = case_data.get('case_type', '')
        
        # 初始化关键词匹配器
        self._init_keyword_matchers()
    
    def should_check_checkbox(self, checkbox_context: Dict, paragraph_text: str, table_context: Optional[Dict] = None) -> bool:
        """
        判断是否应该勾选复选框
        
        Args:
            checkbox_context: 复选框上下文
            paragraph_text: 段落文本
            table_context: 表格上下文（如果在表格中）
        
        Returns:
            布尔值，表示是否应该勾选复选框
        """
        # 提取复选框前后文本
        text_before = checkbox_context.get('text_before', '')
        text_after = checkbox_context.get('text_after', '')
        
        # 记录日志
        print(f"判断复选框: 前文本='{text_before}', 后文本='{text_after}'")
        
        # 检查是否是互斥选项
        if self._is_part_of_mutually_exclusive_group(text_before, text_after, paragraph_text):
            print("检测到互斥选项组")
            
            # 处理互斥选项
            return self._handle_mutually_exclusive_option(text_before, text_after, paragraph_text)
        
        # 基于案件类型的特殊处理
        if self.case_type:
            case_type_result = self._case_type_specific_rules(text_before, text_after, table_context)
            if case_type_result is not None:
                print(f"基于案件类型'{self.case_type}'的规则结果: {case_type_result}")
                return case_type_result
        
        # 检查表格第一列关键词
        if table_context and 'first_column' in table_context:
            first_column_result = self._check_first_column_keywords(
                table_context['first_column'], 
                text_after
            )
            if first_column_result:
                print(f"基于表格第一列的匹配结果: {first_column_result}")
                return first_column_result
        
        # 通用关键词匹配
        keyword_match_result = self._keyword_match(text_after)
        if keyword_match_result is not None:
            print(f"关键词匹配结果: {keyword_match_result}")
            return keyword_match_result
        
        # 默认不勾选
        print("无法确定是否勾选，默认不勾选")
        return False
    
    def _init_keyword_matchers(self):
        """初始化关键词匹配器"""
        # 性别关键词
        self.male_keywords = {'男', '男性', '先生'}
        self.female_keywords = {'女', '女性', '女士', '小姐', '太太'}
        
        # 是否关键词
        self.yes_keywords = {'是', '有', '是的', '同意', '愿意'}
        self.no_keywords = {'否', '无', '没有', '不是', '不同意', '不愿意'}
        
        # 案件类型特定关键词
        self.loan_dispute_keywords = {
            '借款合同': 2,
            '借贷': 2,
            '借款': 2,
            '贷款': 2,
            '欠款': 1,
            '债务': 1,
            '还款': 1
        }
        
        self.divorce_dispute_keywords = {
            '离婚': 2,
            '婚姻': 2,
            '夫妻': 2,
            '配偶': 2,
            '抚养': 1,
            '财产分割': 1,
            '子女': 1
        }
        
        self.labor_dispute_keywords = {
            '劳动合同': 2,
            '工资': 2,
            '劳动关系': 2,
            '解雇': 2,
            '辞职': 1,
            '加班': 1,
            '社保': 1
        }
        
        self.traffic_accident_keywords = {
            '交通事故': 2,
            '车祸': 2,
            '肇事': 2,
            '伤害': 1,
            '赔偿': 1,
            '医疗费': 1
        }
    
    def _is_part_of_mutually_exclusive_group(self, text_before: str, text_after: str, paragraph_text: str) -> bool:
        """
        判断复选框是否属于互斥选项组
        
        Args:
            text_before: 复选框前文本
            text_after: 复选框后文本
            paragraph_text: 段落完整文本
        
        Returns:
            布尔值，表示是否属于互斥选项组
        """
        # 检查是否是性别选项
        if any(keyword in text_after for keyword in self.male_keywords) and \
           any(keyword in paragraph_text for keyword in self.female_keywords):
            return True
        
        if any(keyword in text_after for keyword in self.female_keywords) and \
           any(keyword in paragraph_text for keyword in self.male_keywords):
            return True
        
        # 检查是否是是/否选项
        if any(keyword in text_after for keyword in self.yes_keywords) and \
           any(keyword in paragraph_text for keyword in self.no_keywords):
            return True
        
        if any(keyword in text_after for keyword in self.no_keywords) and \
           any(keyword in paragraph_text for keyword in self.yes_keywords):
            return True
        
        return False
    
    def _handle_mutually_exclusive_option(self, text_before: str, text_after: str, paragraph_text: str) -> bool:
        """
        处理互斥选项
        
        Args:
            text_before: 复选框前文本
            text_after: 复选框后文本
            paragraph_text: 段落完整文本
        
        Returns:
            布尔值，表示是否应该勾选该选项
        """
        # 处理性别选项
        if any(keyword in text_after for keyword in self.male_keywords):
            # 检查原告性别
            plaintiff_gender = self._get_plaintiff_gender()
            if plaintiff_gender == 'male':
                return True
            elif plaintiff_gender == 'female':
                return False
        
        if any(keyword in text_after for keyword in self.female_keywords):
            # 检查原告性别
            plaintiff_gender = self._get_plaintiff_gender()
            if plaintiff_gender == 'female':
                return True
            elif plaintiff_gender == 'male':
                return False
        
        # 处理是/否选项
        # 这里需要根据具体业务逻辑实现
        # 由于是互斥选项，默认不勾选
        return False
    
    def _get_plaintiff_gender(self) -> Optional[str]:
        """
        获取原告性别
        
        Returns:
            'male', 'female' 或 None
        """
        # 尝试从案件数据中获取原告性别
        plaintiff = self.case_data.get('plaintiff', {})
        
        # 尝试中文格式
        gender = plaintiff.get('性别')
        if gender:
            if any(keyword in gender for keyword in self.male_keywords):
                return 'male'
            elif any(keyword in gender for keyword in self.female_keywords):
                return 'female'
        
        # 尝试英文格式
        gender = plaintiff.get('gender')
        if gender:
            if gender.lower() in ('male', 'm', 'man'):
                return 'male'
            elif gender.lower() in ('female', 'f', 'woman'):
                return 'female'
        
        return None
    
    def _case_type_specific_rules(self, text_before: str, text_after: str, table_context: Optional[Dict] = None) -> Optional[bool]:
        """
        基于案件类型的特殊规则
        
        Args:
            text_before: 复选框前文本
            text_after: 复选框后文本
            table_context: 表格上下文
        
        Returns:
            布尔值或None（表示无法确定）
        """
        # 借贷纠纷特殊处理
        if self.case_type in ('借贷纠纷', '民间借贷纠纷', '借款合同纠纷'):
            return self._loan_dispute_rules(text_before, text_after, table_context)
        
        # 离婚纠纷特殊处理
        elif self.case_type in ('离婚纠纷', '婚姻家庭纠纷'):
            return self._divorce_dispute_rules(text_before, text_after, table_context)
        
        # 劳动争议特殊处理
        elif self.case_type in ('劳动争议', '劳动合同纠纷', '工伤赔偿纠纷'):
            return self._labor_dispute_rules(text_before, text_after, table_context)
        
        # 交通事故特殊处理
        elif self.case_type in ('交通事故', '机动车交通事故责任纠纷'):
            return self._traffic_accident_rules(text_before, text_after, table_context)
        
        return None
    
    def _loan_dispute_rules(self, text_before: str, text_after: str, table_context: Optional[Dict] = None) -> Optional[bool]:
        """借贷纠纷特殊规则"""
        # 在这里实现借贷纠纷特有的规则
        # 示例规则
        if '借款合同' in text_after:
            return True
        
        if '民间借贷' in text_after:
            return True
        
        return None
    
    def _divorce_dispute_rules(self, text_before: str, text_after: str, table_context: Optional[Dict] = None) -> Optional[bool]:
        """离婚纠纷特殊规则"""
        # 在这里实现离婚纠纷特有的规则
        # 示例规则
        if '离婚' in text_after:
            return True
        
        if '子女抚养' in text_after:
            # 检查是否有子女
            plaintiff = self.case_data.get('plaintiff', {})
            if plaintiff.get('子女') or plaintiff.get('children'):
                return True
        
        return None
    
    def _labor_dispute_rules(self, text_before: str, text_after: str, table_context: Optional[Dict] = None) -> Optional[bool]:
        """劳动争议特殊规则"""
        # 在这里实现劳动争议特有的规则
        # 示例规则
        if '劳动合同' in text_after:
            return True
        
        if '工资' in text_after and '拖欠' in (text_before + text_after):
            return True
        
        return None
    
    def _traffic_accident_rules(self, text_before: str, text_after: str, table_context: Optional[Dict] = None) -> Optional[bool]:
        """交通事故特殊规则"""
        # 在这里实现交通事故特有的规则
        # 示例规则
        if '交通事故' in text_after:
            return True
        
        if '人身伤害' in text_after:
            return True
        
        return None
    
    def _check_first_column_keywords(self, first_column: str, text_after: str) -> Optional[bool]:
        """
        检查表格第一列关键词
        
        Args:
            first_column: 表格第一列文本
            text_after: 复选框后文本
        
        Returns:
            布尔值或None（表示无法确定）
        """
        # 如果第一列为空，则无法判断
        if not first_column:
            return None
        
        # 根据案件类型选择关键词集
        keywords = {}
        if self.case_type in ('借贷纠纷', '民间借贷纠纷', '借款合同纠纷'):
            keywords = self.loan_dispute_keywords
        elif self.case_type in ('离婚纠纷', '婚姻家庭纠纷'):
            keywords = self.divorce_dispute_keywords
        elif self.case_type in ('劳动争议', '劳动合同纠纷', '工伤赔偿纠纷'):
            keywords = self.labor_dispute_keywords
        elif self.case_type in ('交通事故', '机动车交通事故责任纠纷'):
            keywords = self.traffic_accident_keywords
        
        # 检查第一列是否包含关键词
        for keyword, weight in keywords.items():
            if keyword in first_column:
                # 如果第一列包含关键词，并且复选框后文本也包含相关关键词，则勾选
                if keyword in text_after or any(k in text_after for k in keywords):
                    return True
                
                # 如果关键词权重为2（高确定性），则直接勾选
                if weight >= 2:
                    return True
        
        return None
    
    def _keyword_match(self, text: str) -> Optional[bool]:
        """
        关键词匹配
        
        Args:
            text: 要匹配的文本
        
        Returns:
            布尔值或None（表示无法确定）
        """
        # 根据案件类型选择关键词集
        keywords = {}
        if self.case_type in ('借贷纠纷', '民间借贷纠纷', '借款合同纠纷'):
            keywords = self.loan_dispute_keywords
        elif self.case_type in ('离婚纠纷', '婚姻家庭纠纷'):
            keywords = self.divorce_dispute_keywords
        elif self.case_type in ('劳动争议', '劳动合同纠纷', '工伤赔偿纠纷'):
            keywords = self.labor_dispute_keywords
        elif self.case_type in ('交通事故', '机动车交通事故责任纠纷'):
            keywords = self.traffic_accident_keywords
        
        # 检查文本是否包含关键词
        for keyword, weight in keywords.items():
            if keyword in text:
                # 如果关键词权重为2（高确定性），则直接勾选
                if weight >= 2:
                    return True
                # 如果关键词权重为1（中等确定性），也返回 True
                elif weight == 1:
                    print(f"关键词 '{keyword}' 权重为1，也将勾选复选框")
                    return True
        
        return None
