"""
基于规则的复选框处理器
根据预定义的规则决定是否勾选复选框
"""
import logging
import re
from typing import Dict, Any, List, Optional

# 配置日志记录器
logger = logging.getLogger(__name__)


class RuleBasedCheckboxHandler:
    """基于规则的复选框处理器"""
    
    def __init__(self, case_data: Dict[str, Any], config: Dict[str, Any]):
        """
        初始化基于规则的复选框处理器
        
        Args:
            case_data: 案件数据
            config: 配置信息
        """
        self.case_data = case_data
        self.config = config
        
        # 初始化规则引擎
        self._init_rules()
    
    def _init_rules(self):
        """初始化规则引擎"""
        # 根据案件类型加载不同的规则集
        case_type = self.case_data.get('case_type', 'default')
        
        # 记录规则引擎初始化
        print(f"初始化规则引擎，案件类型: {case_type}")
        
        # 这里可以根据不同的案件类型加载不同的规则集
        # 目前使用通用规则集
        self.rules = self._get_common_rules()
    
    def _get_common_rules(self) -> List[Dict[str, Any]]:
        """获取通用规则集"""
        # 通用规则集
        return [
            # 年龄相关规则
            {
                'pattern': r'原告年龄超过(\d+)岁',
                'field_path': ['原告信息', '年龄'],
                'condition': lambda value, match: value > int(match.group(1))
            },
            {
                'pattern': r'被告年龄超过(\d+)岁',
                'field_path': ['被告信息', '年龄'],
                'condition': lambda value, match: value > int(match.group(1))
            },
            # 金额相关规则
            {
                'pattern': r'金额超过(\d+)元',
                'field_path': ['case_details', 'claim_amount'],
                'condition': lambda value, match: value > int(match.group(1))
            },
            {
                'pattern': r'索赔金额超过(\d+)元',
                'field_path': ['case_details', 'claim_amount'],
                'condition': lambda value, match: value > int(match.group(1))
            },
            # 性别相关规则
            {
                'pattern': r'原告[是为]男性',
                'field_path': ['原告信息', '性别'],
                'condition': lambda value, match: value == '男'
            },
            {
                'pattern': r'原告[是为]女性',
                'field_path': ['原告信息', '性别'],
                'condition': lambda value, match: value == '女'
            },
            {
                'pattern': r'被告[是为]男性',
                'field_path': ['被告信息', '性别'],
                'condition': lambda value, match: value == '男'
            },
            {
                'pattern': r'被告[是为]女性',
                'field_path': ['被告信息', '性别'],
                'condition': lambda value, match: value == '女'
            },
            # 关键词匹配规则
            {
                'pattern': r'借贷纠纷',
                'field_path': ['case_type'],
                'condition': lambda value, match: value == '借贷纠纷'
            },
            {
                'pattern': r'民间借贷',
                'field_path': ['checkbox_keywords', '民间借贷'],
                'condition': lambda value, match: value is True
            },
            {
                'pattern': r'本金',
                'field_path': ['checkbox_keywords', '本金'],
                'condition': lambda value, match: value is True
            },
            {
                'pattern': r'本金争议',
                'field_path': ['checkbox_keywords', '本金'],
                'condition': lambda value, match: value is True
            },
            {
                'pattern': r'利息',
                'field_path': ['checkbox_keywords', '利息'],
                'condition': lambda value, match: value is True
            },
            {
                'pattern': r'利息争议',
                'field_path': ['checkbox_keywords', '利息'],
                'condition': lambda value, match: value is True
            }
        ]
    
    def should_check_checkbox(self, checkbox_context: Dict[str, Any], paragraph_text: str, table_context: Optional[Dict[str, Any]] = None) -> bool:
        """
        根据规则决定是否勾选复选框
        
        Args:
            checkbox_context: 复选框上下文信息
            paragraph_text: 段落文本
            table_context: 表格上下文信息（如果复选框在表格中）
            
        Returns:
            是否应该勾选复选框
        """
        # 获取复选框前后文本
        text_before = checkbox_context.get('text_before', '')
        text_after = checkbox_context.get('text_after', '')
        full_text = checkbox_context.get('full_text', '')
        
        # 组合文本进行规则匹配
        combined_text = text_before + text_after
        
        # 记录规则匹配开始
        print(f"开始规则匹配，文本: '{combined_text[:100]}...'")
        print(f"\n开始规则匹配，文本: '{combined_text[:100]}...'")
        
        # 遍历所有规则进行匹配
        for rule in self.rules:
            pattern = rule['pattern']
            field_path = rule['field_path']
            condition_func = rule['condition']
            
            # 尝试匹配规则模式
            match = re.search(pattern, combined_text)
            if match:
                # 获取字段值
                value = self._get_field_value(field_path)
                
                # 如果字段不存在，跳过此规则
                if value is None:
                    print(f"字段路径不存在: {'.'.join(field_path)}")
                    print(f"  字段路径不存在: {'.'.join(field_path)}")
                    continue
                
                # 应用条件函数
                should_check = condition_func(value, match)
                
                # 记录规则匹配结果
                print(f"规则匹配: 模式='{pattern}', 字段='{'.'.join(field_path)}', 值={value}, 结果={should_check}")
                print(f"  规则匹配: 模式='{pattern}', 字段='{'.'.join(field_path)}', 值={value}, 结果={should_check}")
                
                return should_check
        
        # 如果没有规则匹配，检查是否在checkbox_keywords中有对应的关键词
        if 'checkbox_keywords' in self.case_data:
            # 提取复选框内容的关键词
            keywords = self.case_data.get('checkbox_keywords', {})
            for keyword, value in keywords.items():
                if keyword in combined_text and value is True:
                    print(f"通过checkbox_keywords匹配到关键词: {keyword}, 应勾选")
                    print(f"  通过checkbox_keywords匹配到关键词: {keyword}, 应勾选")
                    return True
        
        # 如果没有规则匹配，默认不勾选
        print("没有规则匹配，默认不勾选")
        print("  没有规则匹配，默认不勾选")
        return False
    
    def _get_field_value(self, field_path: List[str]) -> Any:
        """
        根据字段路径获取案件数据中的字段值
        
        Args:
            field_path: 字段路径列表
            
        Returns:
            字段值，如果字段不存在则返回None
        """
        value = self.case_data
        try:
            for key in field_path:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return None
