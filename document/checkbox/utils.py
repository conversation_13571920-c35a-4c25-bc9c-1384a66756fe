"""
复选框处理工具类
提供一些通用的辅助函数
"""
import logging
import re
from typing import Dict, Any, List, Tuple, Optional, Set

logger = logging.getLogger(__name__)

class CheckboxUtils:
    """复选框处理工具类"""
    
    @staticmethod
    def extract_checkbox_context(paragraph_text: str, checkbox_index: int, all_checkboxes_indices: List[int]) -> Tuple[str, str]:
        """
        提取复选框前后文本
        
        Args:
            paragraph_text: 段落文本
            checkbox_index: 当前复选框在文本中的索引位置
            all_checkboxes_indices: 所有复选框在文本中的索引位置列表
        
        Returns:
            (text_before, text_after) 复选框前后文本的元组
        """
        # 找出当前复选框前面的复选框位置
        prev_checkbox_index = -1
        for idx in all_checkboxes_indices:
            if idx < checkbox_index:
                prev_checkbox_index = max(prev_checkbox_index, idx)
        
        # 找出当前复选框后面的复选框位置
        next_checkbox_index = float('inf')
        for idx in all_checkboxes_indices:
            if idx > checkbox_index:
                next_checkbox_index = min(next_checkbox_index, idx)
        
        if next_checkbox_index == float('inf'):
            next_checkbox_index = len(paragraph_text)
        
        # 提取前后文本
        text_before = paragraph_text[prev_checkbox_index+1:checkbox_index].strip() if prev_checkbox_index >= 0 else paragraph_text[:checkbox_index].strip()
        text_after = paragraph_text[checkbox_index+1:next_checkbox_index].strip()
        
        return text_before, text_after
    
    @staticmethod
    def detect_mutually_exclusive_options(checkboxes_context: List[Dict]) -> List[Set[int]]:
        """
        检测互斥选项组
        
        Args:
            checkboxes_context: 复选框上下文列表
        
        Returns:
            互斥选项组列表，每个组是一个包含复选框索引的集合
        """
        # 性别关键词
        male_keywords = {'男', '男性', '先生'}
        female_keywords = {'女', '女性', '女士', '小姐', '太太'}
        
        # 是否关键词
        yes_keywords = {'是', '有', '是的', '同意', '愿意'}
        no_keywords = {'否', '无', '没有', '不是', '不同意', '不愿意'}
        
        # 互斥选项组
        mutually_exclusive_groups = []
        
        # 检测性别互斥组
        male_indices = set()
        female_indices = set()
        for i, ctx in enumerate(checkboxes_context):
            text_after = ctx.get('text_after', '')
            if any(keyword in text_after for keyword in male_keywords):
                male_indices.add(i)
            elif any(keyword in text_after for keyword in female_keywords):
                female_indices.add(i)
        
        if male_indices and female_indices:
            mutually_exclusive_groups.append(male_indices.union(female_indices))
        
        # 检测是否互斥组
        yes_indices = set()
        no_indices = set()
        for i, ctx in enumerate(checkboxes_context):
            text_after = ctx.get('text_after', '')
            if any(keyword in text_after for keyword in yes_keywords):
                yes_indices.add(i)
            elif any(keyword in text_after for keyword in no_keywords):
                no_indices.add(i)
        
        if yes_indices and no_indices:
            mutually_exclusive_groups.append(yes_indices.union(no_indices))
        
        return mutually_exclusive_groups
    
    @staticmethod
    def extract_table_context(table, row_index: int, cell_index: int) -> Dict[str, str]:
        """
        提取表格上下文
        
        Args:
            table: 表格对象
            row_index: 行索引
            cell_index: 单元格索引
        
        Returns:
            表格上下文字典
        """
        context = {}
        
        # 提取表格第一列
        if row_index < len(table.rows) and len(table.rows[row_index].cells) > 0:
            context['first_column'] = table.rows[row_index].cells[0].text.strip()
        
        # 提取当前行文本
        if row_index < len(table.rows):
            row_text = ""
            for cell in table.rows[row_index].cells:
                row_text += cell.text.strip() + " "
            context['row_text'] = row_text.strip()
        
        # 提取表格标题（如果存在）
        if row_index > 0 and len(table.rows) > 0 and len(table.rows[0].cells) > 0:
            context['table_title'] = table.rows[0].cells[0].text.strip()
        
        return context
    
    @staticmethod
    def get_gender_from_case_data(case_data: Dict[str, Any], party_type: str = 'plaintiff') -> Optional[str]:
        """
        从案件数据中获取性别信息
        
        Args:
            case_data: 案件数据
            party_type: 当事人类型，'plaintiff'（原告）或'defendant'（被告）
        
        Returns:
            'male', 'female' 或 None
        """
        party_data = case_data.get(party_type, {})
        
        # 尝试中文格式
        gender = party_data.get('性别')
        if gender:
            if any(keyword in gender for keyword in ('男', '男性', '先生')):
                return 'male'
            elif any(keyword in gender for keyword in ('女', '女性', '女士', '小姐', '太太')):
                return 'female'
        
        # 尝试英文格式
        gender = party_data.get('gender')
        if gender:
            if gender.lower() in ('male', 'm', 'man'):
                return 'male'
            elif gender.lower() in ('female', 'f', 'woman'):
                return 'female'
        
        return None
