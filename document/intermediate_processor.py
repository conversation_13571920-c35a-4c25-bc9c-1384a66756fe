"""
中间文档处理器
用于生成包含当事人信息的中间文档，并从中提取剩余的占位符
"""

import os
import re
import logging
from docx import Document
from document.party_template_processor import PartyTemplateProcessor

logger = logging.getLogger(__name__)


class IntermediateDocumentProcessor:
    """
    中间文档处理器
    
    主要功能：
    1. 基于当事人信息生成中间文档
    2. 从中间文档中提取剩余的占位符
    3. 提供中间文档的文本内容用于LLM分析
    """
    
    def __init__(self, template_path, debug=False):
        """
        初始化中间文档处理器
        
        Args:
            template_path (str): 模板文件路径
            debug (bool): 是否开启调试模式
        """
        self.template_path = template_path
        self.debug = debug
        self.logger = logging.getLogger('document.intermediate.processor')
        
        if not os.path.exists(template_path):
            raise ValueError(f"模板文件不存在: {template_path}")
    
    def create_intermediate_document(self, party_info):
        """
        创建包含当事人信息的中间文档
        
        Args:
            party_info (dict): 当事人信息字典
            
        Returns:
            tuple: (中间文档对象, 剩余占位符列表)
        """
        self.logger.info("开始创建中间文档...")
        
        # 加载原始模板
        doc = Document(self.template_path)
        
        # 处理当事人模板，填充当事人信息
        # 通用模板始终在根模板目录中
        from config import Config
        templates_root_dir = Config.TEMPLATES_DIR
        party_processor = PartyTemplateProcessor(templates_dir=templates_root_dir, debug=self.debug)
        
        self.logger.info("填充当事人信息到中间文档")
        party_processor.process_party_templates(doc, party_info)
        
        # 从中间文档提取剩余的占位符
        remaining_placeholders = self._extract_remaining_placeholders(doc)
        
        self.logger.info(f"中间文档生成完成，剩余占位符数量: {len(remaining_placeholders)}")
        if self.debug:
            self.logger.debug(f"剩余占位符: {remaining_placeholders}")
        
        return doc, remaining_placeholders
    
    def _extract_remaining_placeholders(self, doc):
        """
        从中间文档中提取剩余的占位符
        
        Args:
            doc (Document): 中间文档对象
            
        Returns:
            list: 剩余占位符列表
        """
        placeholders = set()
        
        # 检查段落中的占位符
        for para in doc.paragraphs:
            matches = re.findall(r'{{(.*?)}}', para.text)
            if matches:
                placeholders.update(matches)
                if self.debug:
                    self.logger.debug(f"段落中找到占位符: {matches}")
        
        # 检查表格中的占位符
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for para in cell.paragraphs:
                        matches = re.findall(r'{{(.*?)}}', para.text)
                        if matches:
                            placeholders.update(matches)
                            if self.debug:
                                self.logger.debug(f"表格中找到占位符: {matches}")
        
        # 清理占位符，移除插入点标记
        clean_placeholders = []
        for placeholder in placeholders:
            placeholder = placeholder.strip()
            # 跳过插入点标记
            if not placeholder.endswith('插入点'):
                clean_placeholders.append(placeholder)
            elif self.debug:
                self.logger.debug(f"跳过插入点标记: {placeholder}")
        
        return sorted(clean_placeholders)
    
    def get_document_text(self, doc):
        """
        获取文档的文本内容
        
        Args:
            doc (Document): 文档对象
            
        Returns:
            str: 文档文本内容
        """
        text_parts = []
        
        # 获取段落文本
        for para in doc.paragraphs:
            if para.text.strip():
                text_parts.append(para.text.strip())
        
        # 获取表格文本
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    cell_text = []
                    for para in cell.paragraphs:
                        if para.text.strip():
                            cell_text.append(para.text.strip())
                    if cell_text:
                        row_text.append(' '.join(cell_text))
                if row_text:
                    text_parts.append(' | '.join(row_text))
        
        return '\n'.join(text_parts)
    
    def get_document_structure_info(self, doc):
        """
        获取文档结构信息，用于LLM分析
        
        Args:
            doc (Document): 文档对象
            
        Returns:
            dict: 文档结构信息
        """
        structure_info = {
            'paragraph_count': len(doc.paragraphs),
            'table_count': len(doc.tables),
            'filled_sections': [],
            'empty_sections': []
        }
        
        # 分析已填充和空白的部分
        for i, para in enumerate(doc.paragraphs):
            if para.text.strip():
                if '{{' in para.text and '}}' in para.text:
                    structure_info['empty_sections'].append(f"段落{i+1}: {para.text[:50]}...")
                else:
                    structure_info['filled_sections'].append(f"段落{i+1}: {para.text[:50]}...")
        
        return structure_info
