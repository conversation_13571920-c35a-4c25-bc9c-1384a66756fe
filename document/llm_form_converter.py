"""
LLM数据转换器
将LLM处理结果转换为Web表单可用的数据格式
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


class LLMFormConverter:
    """
    LLM数据转换器
    
    功能：
    1. 将LLM处理结果(formatted_text)转换为表单数据格式
    2. 处理当事人信息的格式转换
    3. 处理占位符数据的格式转换
    4. 处理复选框数据的格式转换
    """
    
    def __init__(self, debug: bool = False):
        """
        初始化转换器
        
        Args:
            debug (bool): 是否开启调试模式
        """
        self.debug = debug
        self.logger = logging.getLogger('document.llm_form_converter')
        
    def convert_to_form_data(self, formatted_text: Dict[str, Any], template_path: str) -> Dict[str, Any]:
        """
        将LLM处理结果转换为表单数据格式
        
        Args:
            formatted_text (dict): LLM处理结果，包含占位符分析+当事人信息+模板类型
            template_path (str): 模板文件路径
            
        Returns:
            dict: 转换后的表单数据格式
        """
        self.logger.info("开始转换LLM数据为表单数据格式")
        
        if not isinstance(formatted_text, dict):
            self.logger.error("formatted_text不是字典类型，无法转换")
            return {}
        
        # 初始化表单数据
        form_data = {}
        
        # 1. 转换当事人信息
        party_data = self._convert_party_info(formatted_text)
        form_data.update(party_data)
        
        # 2. 转换占位符数据
        placeholder_data = self._convert_placeholder_data(formatted_text)
        form_data.update(placeholder_data)
        
        # 3. 转换复选框数据
        checkbox_data = self._convert_checkbox_data(formatted_text)
        form_data.update(checkbox_data)
        
        # 4. 添加元数据
        form_data.update({
            '_template_path': template_path,
            '_template_type': formatted_text.get('template_type', ''),
            '_conversion_source': 'llm_processing'
        })
        
        self.logger.info(f"LLM数据转换完成，生成表单字段数量: {len(form_data)}")
        
        if self.debug:
            self.logger.debug(f"转换后的表单数据: {json.dumps(form_data, ensure_ascii=False, indent=2)}")
        
        return form_data
    
    def _convert_party_info(self, formatted_text: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换当事人信息
        
        Args:
            formatted_text (dict): LLM处理结果
            
        Returns:
            dict: 转换后的当事人数据
        """
        party_data = {}
        
        # 当事人信息的键名映射
        party_keys = [
            "原告（自然人）列表",
            "原告（法人、非法人组织）列表", 
            "被告（自然人）列表",
            "被告（法人、非法人组织）列表",
            "第三人（自然人）列表",
            "第三人（法人、非法人组织）列表"
        ]
        
        for key in party_keys:
            if key in formatted_text:
                party_list = formatted_text[key]
                if isinstance(party_list, list) and party_list:
                    # 将当事人列表转换为表单字段
                    for i, party in enumerate(party_list):
                        if isinstance(party, dict):
                            for field_name, field_value in party.items():
                                # 生成表单字段名：原告自然人1_姓名
                                form_field_name = f"{key.replace('（', '').replace('）', '').replace('列表', '')}{i+1}_{field_name}"
                                party_data[form_field_name] = str(field_value) if field_value else ""
        
        self.logger.info(f"转换当事人信息，生成字段数量: {len(party_data)}")
        return party_data
    
    def _convert_placeholder_data(self, formatted_text: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换占位符数据
        
        Args:
            formatted_text (dict): LLM处理结果
            
        Returns:
            dict: 转换后的占位符数据
        """
        placeholder_data = {}
        
        # 排除特殊键名，只处理占位符数据
        exclude_keys = {
            "原告（自然人）列表", "原告（法人、非法人组织）列表",
            "被告（自然人）列表", "被告（法人、非法人组织）列表", 
            "第三人（自然人）列表", "第三人（法人、非法人组织）列表",
            "template_type", "checkbox_keywords", "_template_path",
            "_template_type", "_conversion_source"
        }
        
        for key, value in formatted_text.items():
            if key not in exclude_keys and not key.startswith('_'):
                # 直接使用占位符名称作为表单字段名
                placeholder_data[key] = str(value) if value else ""
        
        self.logger.info(f"转换占位符数据，生成字段数量: {len(placeholder_data)}")
        return placeholder_data
    
    def _convert_checkbox_data(self, formatted_text: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换复选框数据
        
        Args:
            formatted_text (dict): LLM处理结果
            
        Returns:
            dict: 转换后的复选框数据
        """
        checkbox_data = {}
        
        # 处理复选框关键词
        checkbox_keywords = formatted_text.get('checkbox_keywords', {})
        if isinstance(checkbox_keywords, dict):
            for keyword, is_checked in checkbox_keywords.items():
                # 复选框字段名格式：checkbox_关键词
                checkbox_field_name = f"checkbox_{keyword}"
                checkbox_data[checkbox_field_name] = bool(is_checked)
        
        self.logger.info(f"转换复选框数据，生成字段数量: {len(checkbox_data)}")
        return checkbox_data
    
    def convert_form_data_to_llm_format(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        将表单数据转换回LLM格式（用于最终文档生成）
        
        Args:
            form_data (dict): 表单数据
            
        Returns:
            dict: LLM格式的数据
        """
        self.logger.info("开始转换表单数据为LLM格式")
        
        llm_data = {}
        
        # 1. 重建当事人信息
        party_info = self._rebuild_party_info(form_data)
        llm_data.update(party_info)
        
        # 2. 重建占位符数据
        placeholder_data = self._rebuild_placeholder_data(form_data)
        llm_data.update(placeholder_data)
        
        # 3. 重建复选框数据
        checkbox_data = self._rebuild_checkbox_data(form_data)
        llm_data.update(checkbox_data)
        
        # 4. 保留元数据
        llm_data['template_type'] = form_data.get('_template_type', '')
        
        self.logger.info(f"表单数据转换为LLM格式完成，字段数量: {len(llm_data)}")
        return llm_data
    
    def _rebuild_party_info(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """重建当事人信息"""
        party_info = {
            "原告（自然人）列表": [],
            "原告（法人、非法人组织）列表": [],
            "被告（自然人）列表": [],
            "被告（法人、非法人组织）列表": [],
            "第三人（自然人）列表": [],
            "第三人（法人、非法人组织）列表": []
        }

        print(f"📍 [当事人重建] 开始重建当事人信息，表单数据字段数: {len(form_data)}")

        # 解析当事人字段 - 支持多种格式
        party_fields = {}
        for field_name, field_value in form_data.items():
            # 支持格式1: 原告_自然人__1_姓名
            # 支持格式2: 原告自然人1_姓名
            # 支持格式3: 原告（自然人）列表
            if any(party_type in field_name for party_type in ['原告', '被告', '第三人']):
                if ('自然人' in field_name or '法人' in field_name or '列表' in field_name):
                    party_fields[field_name] = field_value
                    print(f"📍 [当事人重建] 找到当事人字段: {field_name} = {field_value}")

        print(f"📍 [当事人重建] 找到 {len(party_fields)} 个当事人相关字段")

        # 首先处理列表格式的当事人信息
        for field_name, field_value in party_fields.items():
            if '列表' in field_name and isinstance(field_value, list):
                party_info[field_name] = field_value
                print(f"📍 [当事人重建] 直接使用列表格式: {field_name} ({len(field_value)} 个当事人)")

        # 然后处理索引格式的当事人字段
        for field_name, field_value in party_fields.items():
            # 修复：处理所有非列表字段，包括空值字段（直接编辑模式需要）
            if '列表' not in field_name:
                # 解析字段名 - 支持多种格式
                import re

                # 格式1: 原告_自然人__1_姓名
                pattern1 = r'^(原告|被告|第三人)_(自然人|法人、非法人组织)_+(\d+)_(.+)$'
                match1 = re.match(pattern1, field_name)

                # 格式2: 原告自然人1_姓名 或 原告法人、非法人组织1_姓名
                pattern2 = r'^(原告|被告|第三人)(自然人|法人、非法人组织)(\d+)_(.+)$'
                match2 = re.match(pattern2, field_name)

                if match1:
                    party_type, entity_type, index_str, field_type = match1.groups()
                    index = int(index_str) - 1
                    print(f"📍 [当事人重建] 解析格式1: {party_type} {entity_type} {index+1} {field_type}")
                elif match2:
                    party_type, entity_type, index_str, field_type = match2.groups()
                    index = int(index_str) - 1
                    print(f"📍 [当事人重建] 解析格式2: {party_type} {entity_type} {index+1} {field_type}")
                else:
                    continue

                # 确定当事人列表类型
                if entity_type in ['自然人']:
                    list_key = f"{party_type}（自然人）列表"
                elif entity_type in ['法人', '法人、非法人组织']:
                    list_key = f"{party_type}（法人、非法人组织）列表"
                else:
                    continue

                # 确保列表有足够的元素
                while len(party_info[list_key]) <= index:
                    party_info[list_key].append({})

                # 设置字段值（包括空值）
                party_info[list_key][index][field_type] = field_value if field_value is not None else ""
                print(f"📍 [当事人重建] 设置 {list_key}[{index}][{field_type}] = {field_value}")

        # 输出重建结果
        for list_key, party_list in party_info.items():
            if party_list:
                print(f"📍 [当事人重建] {list_key}: {len(party_list)} 个当事人")
                for i, party in enumerate(party_list):
                    print(f"  当事人{i+1}: {party}")

        return party_info
    
    def _rebuild_placeholder_data(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """重建占位符数据"""
        placeholder_data = {}

        print(f"📍 [占位符重建] 开始重建占位符数据")

        # 排除特殊字段
        exclude_prefixes = ['checkbox_', '_']
        exclude_keywords = ['列表']

        for field_name, field_value in form_data.items():
            # 跳过特殊字段
            if any(field_name.startswith(prefix) for prefix in exclude_prefixes):
                continue
            if any(keyword in field_name for keyword in exclude_keywords):
                continue

            # 包含有值的字段
            if field_value is not None and field_value != '':
                placeholder_data[field_name] = field_value
                print(f"📍 [占位符重建] 添加占位符: {field_name} = {field_value}")

        print(f"📍 [占位符重建] 重建完成，返回 {len(placeholder_data)} 个占位符")
        return placeholder_data
    
    def _rebuild_checkbox_data(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """重建复选框数据"""
        checkbox_data = {}

        print(f"📍 [复选框重建] 开始重建复选框数据")

        # 收集所有复选框字段
        checkbox_fields = {}
        for field_name, field_value in form_data.items():
            if field_name.startswith('checkbox_'):
                checkbox_fields[field_name] = field_value
                print(f"📍 [复选框重建] 找到复选框字段: {field_name} = {field_value}")

        print(f"📍 [复选框重建] 找到 {len(checkbox_fields)} 个复选框字段")

        if checkbox_fields:
            # 直接保留原始的checkbox字段格式，供DocumentGenerator使用
            checkbox_data.update(checkbox_fields)

            # 同时创建checkbox_keywords格式（向后兼容）
            checkbox_keywords = {}
            for field_name, field_value in checkbox_fields.items():
                keyword = field_name.replace('checkbox_', '')
                checkbox_keywords[keyword] = bool(field_value)

            if checkbox_keywords:
                checkbox_data['checkbox_keywords'] = checkbox_keywords
                print(f"📍 [复选框重建] 生成checkbox_keywords: {checkbox_keywords}")

        print(f"📍 [复选框重建] 重建完成，返回 {len(checkbox_data)} 个字段")
        return checkbox_data
