"""
并行处理器
用于并行执行当事人提取和占位符提取，提升处理性能
"""

import threading
import time
import logging
from typing import Dict, List, Tuple, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

logger = logging.getLogger(__name__)


class ParallelProcessor:
    """
    并行处理器，用于同时执行当事人提取和占位符提取
    """
    
    def __init__(self, debug=False):
        """
        初始化并行处理器
        
        Args:
            debug (bool): 是否开启调试模式
        """
        self.debug = debug
        self.logger = logging.getLogger('document.parallel.processor')
    
    def extract_party_and_placeholders_parallel(
        self,
        text: str,
        template_path: str,
        party_processor_config: Dict[str, Any],
        llm_processor_config: Dict[str, Any],
        selected_template_type: str,
        classification_mode: str = "auto",
        flask_app = None
    ) -> <PERSON><PERSON>[Dict[str, Any], Dict[str, Any]]:
        """
        并行执行当事人提取和占位符内容提取（两个LLM调用）

        Args:
            text (str): 需要分析的原始文本
            template_path (str): 模板文件路径
            party_processor_config (dict): 当事人处理器配置
            llm_processor_config (dict): LLM处理器配置
            selected_template_type (str): 选定的模板类型
            classification_mode (str): 分类模式
            flask_app: Flask应用实例，用于在子线程中创建应用上下文

        Returns:
            tuple: (当事人信息字典, 格式化文本字典)
        """
        self.logger.info("开始并行处理当事人提取和占位符内容提取...")
        start_time = time.time()

        # 获取Flask应用实例
        if flask_app is None:
            try:
                from flask import current_app
                flask_app = current_app._get_current_object()
            except:
                self.logger.warning("无法获取Flask应用上下文，将使用默认配置")
                flask_app = None

        # 使用ThreadPoolExecutor进行并行处理
        with ThreadPoolExecutor(max_workers=2, thread_name_prefix="ParallelProcessor") as executor:
            # 提交当事人提取任务
            party_future = executor.submit(
                self._extract_party_info_task,
                text,
                template_path,
                party_processor_config,
                flask_app
            )

            # 提交占位符内容提取任务
            placeholder_content_future = executor.submit(
                self._extract_placeholder_content_task,
                text,
                selected_template_type,
                classification_mode,
                llm_processor_config,
                flask_app
            )

            # 等待两个任务完成
            party_info = None
            formatted_text = None

            for future in as_completed([party_future, placeholder_content_future]):
                try:
                    if future == party_future:
                        party_info = future.result()
                        self.logger.info("当事人提取任务完成")
                    elif future == placeholder_content_future:
                        formatted_text = future.result()
                        self.logger.info("占位符内容提取任务完成")
                except Exception as e:
                    self.logger.error(f"并行任务执行失败: {str(e)}")
                    if future == party_future:
                        party_info = self._get_empty_party_info()
                    elif future == placeholder_content_future:
                        formatted_text = {"error": str(e)}

        end_time = time.time()
        processing_time = end_time - start_time

        self.logger.info(f"并行处理完成，总耗时: {processing_time:.2f}秒")

        # 确保返回值不为None
        if party_info is None:
            party_info = self._get_empty_party_info()
        if formatted_text is None:
            formatted_text = {"error": "占位符内容提取失败"}

        # 合并当事人信息到格式化文本，并转换为带索引的占位符格式
        if isinstance(formatted_text, dict) and "error" not in formatted_text:
            # 转换当事人信息为带索引的占位符格式
            converted_party_info = self._convert_party_info_to_indexed_placeholders(party_info)
            formatted_text.update(converted_party_info)
            formatted_text['template_type'] = selected_template_type
            self.logger.info("当事人信息已转换并合并到格式化文本中")

        return party_info, formatted_text
    
    def _extract_party_info_task(self, text: str, template_path: str, config: Dict[str, Any], flask_app=None) -> Dict[str, Any]:
        """
        当事人提取任务（在独立线程中执行）

        Args:
            text (str): 需要分析的文本
            template_path (str): 模板文件路径
            config (dict): 当事人处理器配置
            flask_app: Flask应用实例

        Returns:
            dict: 当事人信息字典
        """
        def _do_extraction():
            try:
                self.logger.info("开始当事人提取任务...")

                # 导入当事人处理器
                from llm.party_processor import PartyInfoProcessor

                # 创建当事人处理器实例
                party_processor = PartyInfoProcessor(
                    api_url=config['api_url'],
                    api_key=config['api_key'],
                    model=config['model'],
                    debug=config.get('debug', False)
                )

                # 提取当事人信息
                party_info = party_processor.extract_party_info(text, template_path)

                # 验证当事人信息
                if not party_processor.validate_party_info(party_info):
                    self.logger.warning("当事人信息验证失败，将使用空的当事人信息")
                    party_info = self._get_empty_party_info()

                self.logger.info("当事人提取任务成功完成")
                return party_info

            except Exception as e:
                self.logger.error(f"当事人提取任务失败: {str(e)}")
                return self._get_empty_party_info()

        # 如果有Flask应用实例，在应用上下文中执行
        if flask_app:
            with flask_app.app_context():
                return _do_extraction()
        else:
            return _do_extraction()
    
    def _extract_placeholder_content_task(
        self,
        text: str,
        template_type: str,
        classification_mode: str,
        llm_processor_config: Dict[str, Any],
        flask_app=None
    ) -> Dict[str, Any]:
        """
        占位符内容提取任务（在独立线程中执行）

        Args:
            text (str): 需要分析的文本
            template_type (str): 模板类型
            classification_mode (str): 分类模式
            llm_processor_config (dict): LLM处理器配置
            flask_app: Flask应用实例

        Returns:
            dict: 格式化文本字典
        """
        def _do_extraction():
            try:
                self.logger.info("开始占位符内容提取任务...")

                # 导入LLM处理器
                from llm.processor import LLMProcessor

                # 创建LLM处理器实例
                llm_processor = LLMProcessor(
                    api_url=llm_processor_config['api_url'],
                    api_key=llm_processor_config['api_key'],
                    model=llm_processor_config['model'],
                    classification_model=llm_processor_config.get('classification_model'),
                    classification_api_url=llm_processor_config.get('classification_api_url'),
                    classification_api_key=llm_processor_config.get('classification_api_key')
                )

                # 处理文本，提取占位符内容
                formatted_text = llm_processor.process_text(text, template_type, classification_mode)

                self.logger.info("占位符内容提取任务成功完成")
                return formatted_text

            except Exception as e:
                self.logger.error(f"占位符内容提取任务失败: {str(e)}")
                return {"error": str(e)}

        # 如果有Flask应用实例，在应用上下文中执行
        if flask_app:
            with flask_app.app_context():
                return _do_extraction()
        else:
            return _do_extraction()

    def _extract_template_placeholders_task(self, template_path: str) -> List[str]:
        """
        模板占位符提取任务（在独立线程中执行）

        Args:
            template_path (str): 模板文件路径

        Returns:
            list: 占位符列表
        """
        try:
            self.logger.info("开始模板占位符提取任务...")

            # 导入文档生成器
            from document.generator import DocumentGenerator

            # 创建文档生成器实例
            doc_generator = DocumentGenerator(template_path)

            # 提取占位符
            doc_generator.update_placeholders()

            # 获取占位符列表
            import os
            template_name = os.path.basename(template_path)
            placeholders = doc_generator.placeholders.get(template_name, [])

            self.logger.info(f"模板占位符提取任务成功完成，找到 {len(placeholders)} 个占位符")
            return placeholders

        except Exception as e:
            self.logger.error(f"模板占位符提取任务失败: {str(e)}")
            return []
    
    def _convert_party_info_to_indexed_placeholders(self, party_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        将当事人信息转换为带索引的占位符格式

        Args:
            party_info (dict): 原始当事人信息字典

        Returns:
            dict: 转换后的占位符字典
        """
        converted_info = {}

        # 定义当事人类型映射
        party_type_mapping = {
            "原告（自然人）列表": "原告_自然人_",
            "原告（法人、非法人组织）列表": "原告_法人_非法人组织_",
            "被告（自然人）列表": "被告_自然人_",
            "被告（法人、非法人组织）列表": "被告_法人_非法人组织_",
            "第三人（自然人）列表": "第三人_自然人_",
            "第三人（法人、非法人组织）列表": "第三人_法人_非法人组织_"
        }

        for list_key, party_list in party_info.items():
            if list_key in party_type_mapping and isinstance(party_list, list):
                party_type_prefix = party_type_mapping[list_key]

                # 为每个当事人生成带索引的占位符
                for i, party_data in enumerate(party_list, 1):
                    if isinstance(party_data, dict):
                        for field_name, field_value in party_data.items():
                            # 生成带索引的占位符名称
                            # 例如：原告_自然人__1_姓名
                            placeholder_key = f"{party_type_prefix}_{i}_{field_name}"
                            converted_info[placeholder_key] = field_value

                            self.logger.debug(f"转换占位符: {field_name} -> {placeholder_key} = {field_value}")

        self.logger.info(f"当事人信息转换完成，生成 {len(converted_info)} 个带索引的占位符")
        return converted_info

    def _get_empty_party_info(self) -> Dict[str, Any]:
        """
        获取空的当事人信息结构

        Returns:
            dict: 空的当事人信息字典
        """
        return {
            "原告（自然人）列表": [],
            "原告（法人、非法人组织）列表": [],
            "被告（自然人）列表": [],
            "被告（法人、非法人组织）列表": [],
            "第三人（自然人）列表": [],
            "第三人（法人、非法人组织）列表": []
        }
