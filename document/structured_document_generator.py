"""
结构化文档生成器
保持Word文档原始结构的同时进行内容替换
"""

import os
import re
import logging
from docx import Document
from typing import Dict, List, Any
from document.web_form_analyzer import TemplateAnalyzer

logger = logging.getLogger(__name__)

class StructuredDocumentGenerator:
    """结构化文档生成器，保持Word文档的原始结构"""
    
    def __init__(self, template_path: str):
        """
        初始化结构化文档生成器
        
        Args:
            template_path (str): Word模板文件路径
        """
        self.template_path = template_path
        self.analyzer = TemplateAnalyzer(template_path)
        self.doc = Document(template_path)
    
    def generate_document(self, form_data: Dict[str, Any], output_path: str = None) -> str:
        """
        基于表单数据生成Word文档，保持原始结构
        
        Args:
            form_data (dict): 表单数据
            output_path (str): 输出文件路径
            
        Returns:
            str: 生成的文档路径
        """
        try:
            print(f"开始生成结构化文档，数据字段: {len(form_data)}")
            
            # 处理占位符替换
            self._replace_placeholders(form_data)
            
            # 处理复选框
            self._process_checkboxes(form_data)
            
            # 生成输出路径
            if not output_path:
                template_name = os.path.basename(self.template_path)
                output_name = template_name.replace('.docx', '_generated.docx')
                output_path = os.path.join(os.path.dirname(self.template_path), 'generated', output_name)
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存文档
            self.doc.save(output_path)
            print(f"文档生成成功: {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"生成文档时出错: {str(e)}")
            raise
    
    def _replace_placeholders(self, form_data: Dict[str, Any]):
        """替换文档中的占位符，保持格式"""
        print("开始替换占位符...")
        
        # 在段落中替换占位符
        for para in self.doc.paragraphs:
            self._replace_paragraph_placeholders(para, form_data)
        
        # 在表格中替换占位符
        for table in self.doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for para in cell.paragraphs:
                        self._replace_paragraph_placeholders(para, form_data)
        
        print("占位符替换完成")
    
    def _replace_paragraph_placeholders(self, paragraph, form_data: Dict[str, Any]):
        """在段落中替换占位符，保持格式"""
        if not paragraph.text:
            return
        
        # 查找占位符
        placeholder_pattern = r'\{\{([^}]+)\}\}'
        matches = re.findall(placeholder_pattern, paragraph.text)
        
        if not matches:
            return
        
        # 逐个替换占位符
        for match in matches:
            placeholder = match.strip()
            if placeholder in form_data:
                replacement_value = str(form_data[placeholder]) if form_data[placeholder] is not None else ''
                
                # 在段落中查找并替换占位符，保持格式
                self._replace_text_in_paragraph(paragraph, f'{{{{{placeholder}}}}}', replacement_value)
                
                print(f"替换占位符: {placeholder} -> {replacement_value}")
    
    def _replace_text_in_paragraph(self, paragraph, search_text: str, replacement_text: str):
        """在段落中替换文本，保持格式"""
        # 获取段落的所有run
        runs = paragraph.runs
        
        # 构建完整的段落文本
        full_text = ''.join(run.text for run in runs)
        
        if search_text not in full_text:
            return
        
        # 找到替换位置
        start_pos = full_text.find(search_text)
        end_pos = start_pos + len(search_text)
        
        # 清空所有run的文本
        for run in runs:
            run.text = ''
        
        # 重新分配文本
        current_pos = 0
        replacement_done = False
        
        for run in runs:
            if current_pos >= len(full_text):
                break
            
            # 计算这个run应该包含的原始文本长度
            original_run_text = paragraph._element.xpath('.//w:t')[runs.index(run)].text if runs.index(run) < len(paragraph._element.xpath('.//w:t')) else ''
            run_length = len(original_run_text)
            
            if current_pos + run_length <= start_pos:
                # 在替换位置之前
                run.text = full_text[current_pos:current_pos + run_length]
            elif current_pos >= end_pos:
                # 在替换位置之后
                run.text = full_text[current_pos:current_pos + run_length]
            elif current_pos < start_pos and current_pos + run_length > start_pos:
                # 跨越替换开始位置
                if not replacement_done:
                    run.text = full_text[current_pos:start_pos] + replacement_text
                    replacement_done = True
                else:
                    run.text = full_text[current_pos:start_pos]
            elif current_pos >= start_pos and current_pos < end_pos:
                # 在替换范围内
                if not replacement_done:
                    run.text = replacement_text
                    replacement_done = True
                # 其他在范围内的run保持空白
            
            current_pos += run_length
        
        # 如果还没有完成替换，在第一个run中完成
        if not replacement_done and runs:
            runs[0].text = full_text.replace(search_text, replacement_text)
    
    def _process_checkboxes(self, form_data: Dict[str, Any]):
        """处理复选框，保持原始位置和格式"""
        print("开始处理复选框...")
        
        checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓"]
        
        # 处理段落中的复选框
        for para in self.doc.paragraphs:
            self._process_paragraph_checkboxes(para, form_data, checkbox_chars)
        
        # 处理表格中的复选框
        for table in self.doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for para in cell.paragraphs:
                        self._process_paragraph_checkboxes(para, form_data, checkbox_chars)
        
        print("复选框处理完成")
    
    def _process_paragraph_checkboxes(self, paragraph, form_data: Dict[str, Any], checkbox_chars: List[str]):
        """处理段落中的复选框"""
        if not paragraph.text:
            return
        
        # 检查是否包含复选框字符
        has_checkbox = any(char in paragraph.text for char in checkbox_chars)
        if not has_checkbox:
            return
        
        # 查找对应的复选框数据
        for i, checkbox_info in enumerate(self.analyzer.checkboxes):
            checkbox_name = f'checkbox_{i}'
            is_checked = form_data.get(checkbox_name, False)
            
            # 转换布尔值
            if isinstance(is_checked, str):
                is_checked = is_checked.lower() in ['true', '1', 'on', 'yes']
            
            # 检查是否是当前段落的复选框
            if self._is_matching_checkbox(paragraph.text, checkbox_info['text']):
                # 替换复选框字符
                new_text = paragraph.text
                for char in checkbox_chars:
                    if char in new_text:
                        if is_checked:
                            # 选中状态：使用实心或勾选符号
                            new_text = new_text.replace(char, '☑', 1)  # 只替换第一个
                        else:
                            # 未选中状态：使用空心方框
                            new_text = new_text.replace(char, '☐', 1)  # 只替换第一个
                        break
                
                # 更新段落文本
                if new_text != paragraph.text:
                    # 保持格式的文本替换
                    self._replace_text_in_paragraph(paragraph, paragraph.text, new_text)
                    print(f"更新复选框: {checkbox_info['location']} -> {'选中' if is_checked else '未选中'}")
                
                break
    
    def _is_matching_checkbox(self, paragraph_text: str, checkbox_text: str) -> bool:
        """判断段落文本是否匹配复选框"""
        # 移除复选框字符进行比较
        checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓"]
        
        clean_para_text = paragraph_text
        clean_checkbox_text = checkbox_text
        
        for char in checkbox_chars:
            clean_para_text = clean_para_text.replace(char, '').strip()
            clean_checkbox_text = clean_checkbox_text.replace(char, '').strip()
        
        # 检查文本相似度
        return clean_para_text == clean_checkbox_text or clean_checkbox_text in clean_para_text
    
    def generate_preview_html(self, form_data: Dict[str, Any]) -> str:
        """
        生成文档预览HTML，显示填充后的效果
        
        Args:
            form_data (dict): 表单数据
            
        Returns:
            str: 预览HTML内容
        """
        try:
            html_parts = []
            html_parts.append('<div class="document-preview">')
            html_parts.append('<h2>文档预览</h2>')
            
            # 按文档结构生成预览
            for element in self.analyzer.document_structure:
                if element['type'] == 'paragraph':
                    html_parts.append(self._generate_paragraph_preview(element, form_data))
                elif element['type'] == 'table':
                    html_parts.append(self._generate_table_preview(element, form_data))
            
            html_parts.append('</div>')
            
            # 添加预览样式
            preview_html = f'''
<style>
.document-preview {{
    font-family: "Microsoft YaHei", "SimSun", serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: white;
    line-height: 1.6;
}}

.preview-title {{
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
}}

.preview-paragraph {{
    margin: 10px 0;
    text-indent: 2em;
}}

.preview-paragraph.title {{
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    margin: 20px 0;
    text-indent: 0;
}}

.preview-table {{
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
}}

.preview-table td, .preview-table th {{
    border: 1px solid #000;
    padding: 8px;
    text-align: left;
    vertical-align: top;
}}

.filled-placeholder {{
    background: #e8f5e8;
    color: #2d5a2d;
    font-weight: 500;
}}

.checkbox-checked {{
    color: #28a745;
    font-weight: bold;
}}

.checkbox-unchecked {{
    color: #6c757d;
}}
</style>
{chr(10).join(html_parts)}'''
            
            return preview_html
            
        except Exception as e:
            logger.error(f"生成预览时出错: {str(e)}")
            return f'<div class="error">预览生成失败: {str(e)}</div>'
    
    def _generate_paragraph_preview(self, element: Dict[str, Any], form_data: Dict[str, Any]) -> str:
        """生成段落预览HTML"""
        text = element['text']
        
        # 替换占位符
        placeholder_pattern = r'\{\{([^}]+)\}\}'
        def replace_placeholder(match):
            placeholder = match.group(1).strip()
            if placeholder in form_data and form_data[placeholder]:
                return f'<span class="filled-placeholder">{form_data[placeholder]}</span>'
            else:
                return f'<span class="unfilled-placeholder">{{{{ {placeholder} }}}}</span>'
        
        text = re.sub(placeholder_pattern, replace_placeholder, text)
        
        # 处理复选框
        checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓"]
        for char in checkbox_chars:
            if char in text:
                # 查找对应的复选框状态
                is_checked = self._find_checkbox_status(element['text'], form_data)
                if is_checked:
                    text = text.replace(char, '<span class="checkbox-checked">☑</span>', 1)
                else:
                    text = text.replace(char, '<span class="checkbox-unchecked">☐</span>', 1)
        
        # 确定段落样式
        css_class = 'title' if element['is_title'] else ''
        
        return f'<div class="preview-paragraph {css_class}">{text}</div>'
    
    def _generate_table_preview(self, element: Dict[str, Any], form_data: Dict[str, Any]) -> str:
        """生成表格预览HTML，正确处理合并单元格"""
        html_parts = []
        html_parts.append('<table class="preview-table">')

        for row_info in element['structure']:
            html_parts.append('  <tr>')
            for cell_info in row_info['cells']:
                cell_text = cell_info['text']

                # 替换占位符
                placeholder_pattern = r'\{\{([^}]+)\}\}'
                def replace_placeholder(match):
                    placeholder = match.group(1).strip()
                    if placeholder in form_data and form_data[placeholder]:
                        return f'<span class="filled-placeholder">{form_data[placeholder]}</span>'
                    else:
                        return f'<span class="unfilled-placeholder">{{{{ {placeholder} }}}}</span>'

                cell_text = re.sub(placeholder_pattern, replace_placeholder, cell_text)

                # 处理复选框
                checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓"]
                for char in checkbox_chars:
                    if char in cell_text:
                        is_checked = self._find_checkbox_status(cell_info['text'], form_data)
                        if is_checked:
                            cell_text = cell_text.replace(char, '<span class="checkbox-checked">☑</span>', 1)
                        else:
                            cell_text = cell_text.replace(char, '<span class="checkbox-unchecked">☐</span>', 1)

                # 检查是否是合并单元格
                colspan = cell_info.get('colspan', 1)
                if colspan > 1:
                    html_parts.append(f'    <td colspan="{colspan}">{cell_text}</td>')
                else:
                    html_parts.append(f'    <td>{cell_text}</td>')
            html_parts.append('  </tr>')

        html_parts.append('</table>')

        return '\n'.join(html_parts)
    
    def _find_checkbox_status(self, text: str, form_data: Dict[str, Any]) -> bool:
        """查找复选框的选中状态"""
        for i, checkbox_info in enumerate(self.analyzer.checkboxes):
            if self._is_matching_checkbox(text, checkbox_info['text']):
                checkbox_name = f'checkbox_{i}'
                is_checked = form_data.get(checkbox_name, False)
                if isinstance(is_checked, str):
                    is_checked = is_checked.lower() in ['true', '1', 'on', 'yes']
                return bool(is_checked)
        return False
