"""
Word模板解析器
用于分析Word模板中的占位符、复选框和文档结构，生成Web表单配置
"""

import os
import re
import json
import logging
from docx import Document
from typing import Dict, List, Tuple, Any

logger = logging.getLogger(__name__)

class TemplateAnalyzer:
    """Word模板分析器，用于解析模板结构并生成表单配置"""
    
    def __init__(self, template_path: str):
        """
        初始化模板分析器
        
        Args:
            template_path (str): Word模板文件路径
        """
        self.template_path = template_path
        self.template_name = os.path.basename(template_path)
        self.doc = None
        self.placeholders = []
        self.checkboxes = []
        self.document_structure = []
        
        if os.path.exists(template_path):
            self.doc = Document(template_path)
            self._analyze_template()
        else:
            raise FileNotFoundError(f"模板文件不存在: {template_path}")
    
    def _analyze_template(self):
        """分析模板内容"""
        print(f"开始分析模板: {self.template_name}")
        
        # 分析占位符
        self._extract_placeholders()
        
        # 分析复选框
        self._extract_checkboxes()
        
        # 分析文档结构
        self._analyze_document_structure()
        
        print(f"分析完成 - 占位符: {len(self.placeholders)}, 复选框: {len(self.checkboxes)}")
    
    def _extract_placeholders(self):
        """提取模板中的所有占位符"""
        placeholder_pattern = r'\{\{([^}]+)\}\}'
        found_placeholders = set()
        
        # 从段落中提取
        for para in self.doc.paragraphs:
            matches = re.findall(placeholder_pattern, para.text)
            for match in matches:
                clean_placeholder = match.strip()
                if clean_placeholder and not self._is_insertion_marker(clean_placeholder):
                    found_placeholders.add(clean_placeholder)
        
        # 从表格中提取
        for table in self.doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for para in cell.paragraphs:
                        matches = re.findall(placeholder_pattern, para.text)
                        for match in matches:
                            clean_placeholder = match.strip()
                            if clean_placeholder and not self._is_insertion_marker(clean_placeholder):
                                found_placeholders.add(clean_placeholder)
        
        self.placeholders = sorted(list(found_placeholders))
        print(f"找到占位符: {self.placeholders}")
    
    def _is_insertion_marker(self, placeholder: str) -> bool:
        """判断是否为插入点标记"""
        insertion_markers = [
            '原告（自然人）插入点', '原告（法人、非法人组织）插入点',
            '被告（自然人）插入点', '被告（法人、非法人组织）插入点',
            '第三人（自然人）插入点', '第三人（法人、非法人组织）插入点'
        ]
        return placeholder in insertion_markers
    
    def _extract_checkboxes(self):
        """提取模板中的复选框，识别每个独立的复选框选项"""
        checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓"]
        checkbox_items = []

        # 从段落中提取复选框
        for para_idx, para in enumerate(self.doc.paragraphs):
            para_checkboxes = self._extract_checkboxes_from_text(
                para.text,
                'paragraph',
                para_idx,
                f'段落_{para_idx}'
            )
            checkbox_items.extend(para_checkboxes)

        # 从表格中提取复选框
        for table_idx, table in enumerate(self.doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    for para_idx, para in enumerate(cell.paragraphs):
                        cell_checkboxes = self._extract_checkboxes_from_text(
                            para.text,
                            'table_cell',
                            para_idx,
                            f'表格_{table_idx}_行_{row_idx}_列_{cell_idx}',
                            {
                                'table_index': table_idx,
                                'row_index': row_idx,
                                'cell_index': cell_idx,
                                'paragraph_index': para_idx
                            }
                        )
                        checkbox_items.extend(cell_checkboxes)

        self.checkboxes = checkbox_items
        print(f"找到复选框: {len(checkbox_items)} 个")

        # 打印详细的复选框信息用于调试
        for i, checkbox in enumerate(checkbox_items):
            print(f"  {i+1}. {checkbox['location']}: {checkbox['text']}")

    def _extract_checkboxes_from_text(self, text: str, element_type: str, element_index: int, location: str, extra_info: dict = None) -> List[Dict[str, Any]]:
        """从文本中提取所有复选框选项"""
        if not text or not text.strip():
            return []

        checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓"]
        checkbox_items = []

        # 检查是否包含复选框字符
        if not any(char in text for char in checkbox_chars):
            return []

        # 使用正则表达式找到所有复选框及其相关文本
        # 匹配模式：文本内容 + 复选框字符 或 复选框字符 + 文本内容
        patterns = [
            # 模式1: 文本□ (复选框在文本后面)
            r'([^□☐☑■✓✔✗✘×☒☓/\n]+)([□☐☑■✓✔✗✘×☒☓])',
            # 模式2: □文本 (复选框在文本前面)
            r'([□☐☑■✓✔✗✘×☒☓])([^□☐☑■✓✔✗✘×☒☓/\n]+)',
        ]

        found_checkboxes = []

        # 尝试不同的匹配模式
        for pattern in patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                if pattern.startswith('([^'):  # 文本在前的模式
                    checkbox_text = match.group(1).strip()
                    checkbox_char = match.group(2)
                else:  # 复选框在前的模式
                    checkbox_char = match.group(1)
                    checkbox_text = match.group(2).strip()

                if checkbox_text and len(checkbox_text) > 0:
                    # 清理文本，移除多余的符号
                    cleaned_text = self._clean_checkbox_text(checkbox_text)
                    if cleaned_text:
                        found_checkboxes.append({
                            'text': cleaned_text,
                            'char': checkbox_char,
                            'position': match.start(),
                            'full_match': match.group(0)
                        })

        # 如果正则匹配失败，尝试简单的分割方法
        if not found_checkboxes:
            found_checkboxes = self._extract_checkboxes_by_splitting(text)

        # 转换为标准格式
        for i, checkbox in enumerate(found_checkboxes):
            checkbox_item = {
                'type': element_type,
                'index': element_index,
                'text': f"{checkbox['char']}{checkbox['text']}",
                'clean_text': checkbox['text'],
                'checkbox_char': checkbox['char'],
                'location': f"{location}_复选框_{i+1}",
                'position': checkbox.get('position', 0),
                'original_text': text
            }

            # 添加额外信息
            if extra_info:
                checkbox_item.update(extra_info)

            checkbox_items.append(checkbox_item)

        return checkbox_items

    def _extract_checkboxes_by_splitting(self, text: str) -> List[Dict[str, Any]]:
        """通过分割方法提取复选框（备用方法）"""
        checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓"]
        found_checkboxes = []

        # 按常见分隔符分割
        separators = ['/', '、', '，', ',', '；', ';', '\n']
        parts = [text]

        for sep in separators:
            new_parts = []
            for part in parts:
                new_parts.extend(part.split(sep))
            parts = new_parts

        # 检查每个部分是否包含复选框
        for part in parts:
            part = part.strip()
            if not part:
                continue

            for char in checkbox_chars:
                if char in part:
                    # 提取复选框文本
                    clean_text = part.replace(char, '').strip()
                    # 移除括号内容（如果有的话）
                    clean_text = re.sub(r'\([^)]*\)', '', clean_text).strip()

                    if clean_text and len(clean_text) > 0:
                        found_checkboxes.append({
                            'text': clean_text,
                            'char': char,
                            'position': text.find(part),
                            'full_match': part
                        })
                    break

        return found_checkboxes

    def _clean_checkbox_text(self, text: str) -> str:
        """清理复选框文本"""
        if not text:
            return ""

        # 移除前后空白
        text = text.strip()

        # 移除常见的前缀符号
        text = re.sub(r'^[/\\、，,；;：:]+', '', text)
        text = re.sub(r'[/\\、，,；;：:]+$', '', text)

        # 移除多余的空白
        text = re.sub(r'\s+', ' ', text)

        # 移除空的括号
        text = re.sub(r'\(\s*\)', '', text)

        return text.strip()
    
    def _analyze_document_structure(self):
        """分析文档结构，保持原始Word文档的完整结构"""
        structure = []

        # 获取所有文档元素的顺序（段落和表格混合）
        document_elements = []

        # 遍历文档的所有元素
        for element in self.doc.element.body:
            if element.tag.endswith('p'):  # 段落
                # 找到对应的段落对象
                for idx, para in enumerate(self.doc.paragraphs):
                    if para._element == element:
                        document_elements.append(('paragraph', idx, para))
                        break
            elif element.tag.endswith('tbl'):  # 表格
                # 找到对应的表格对象
                for idx, table in enumerate(self.doc.tables):
                    if table._element == element:
                        document_elements.append(('table', idx, table))
                        break

        # 按文档顺序分析结构
        for element_type, element_idx, element_obj in document_elements:
            if element_type == 'paragraph':
                para = element_obj
                if para.text.strip():
                    # 判断是否为标题
                    is_title = self._is_title_paragraph(para)

                    structure.append({
                        'type': 'paragraph',
                        'index': element_idx,
                        'document_order': len(structure),
                        'text': para.text.strip(),
                        'is_title': is_title,
                        'has_placeholders': bool(re.search(r'\{\{.*?\}\}', para.text)),
                        'has_checkboxes': any(char in para.text for char in ["□", "☐", "☑"]),
                        'style': para.style.name if para.style else 'Normal',
                        'alignment': str(para.alignment) if para.alignment else 'LEFT'
                    })

            elif element_type == 'table':
                table = element_obj
                table_info = {
                    'type': 'table',
                    'index': element_idx,
                    'document_order': len(structure),
                    'rows': len(table.rows),
                    'columns': len(table.columns) if table.rows else 0,
                    'cells': [],
                    'structure': []  # 保存表格的详细结构
                }

                # 分析表格结构，检测合并单元格
                for row_idx, row in enumerate(table.rows):
                    row_info = {
                        'row_index': row_idx,
                        'cells': []
                    }

                    processed_cell_ids = set()  # 记录当前行已处理的单元格ID

                    for cell_idx, cell in enumerate(row.cells):
                        # 检查是否是合并单元格的重复部分
                        cell_id = id(cell)

                        # 如果这个单元格ID已经处理过，说明是合并单元格的重复部分，跳过
                        if cell_id in processed_cell_ids:
                            continue

                        processed_cell_ids.add(cell_id)

                        cell_paragraphs = []
                        cell_text_parts = []

                        for para in cell.paragraphs:
                            para_text = para.text.strip()
                            if para_text:
                                cell_paragraphs.append({
                                    'text': para_text,
                                    'style': para.style.name if para.style else 'Normal',
                                    'has_placeholders': bool(re.search(r'\{\{.*?\}\}', para_text)),
                                    'has_checkboxes': any(char in para_text for char in ["□", "☐", "☑"])
                                })
                                cell_text_parts.append(para_text)

                        cell_text = ' '.join(cell_text_parts)

                        # 检查单元格是否跨列（colspan）
                        colspan = 1
                        try:
                            cell_xml = cell._element.xml
                            if 'gridSpan' in cell_xml:
                                import re as re_module
                                span_match = re_module.search(r'gridSpan w:val="(\d+)"', cell_xml)
                                if span_match:
                                    colspan = int(span_match.group(1))
                        except:
                            pass

                        cell_info = {
                            'row': row_idx,
                            'column': cell_idx,
                            'text': cell_text,
                            'paragraphs': cell_paragraphs,
                            'has_placeholders': bool(re.search(r'\{\{.*?\}\}', cell_text)),
                            'has_checkboxes': any(char in cell_text for char in ["□", "☐", "☑"]),
                            'colspan': colspan,
                            'is_merged': colspan > 1
                        }

                        row_info['cells'].append(cell_info)

                        if cell_text:
                            table_info['cells'].append(cell_info)

                    table_info['structure'].append(row_info)

                structure.append(table_info)

        self.document_structure = structure
    
    def _is_title_paragraph(self, para) -> bool:
        """判断段落是否为标题"""
        text = para.text.strip()
        
        # 常见标题模式
        title_patterns = [
            r'^起诉状$',
            r'^民事起诉状$',
            r'^原告[：:]',
            r'^被告[：:]',
            r'^第三人[：:]',
            r'^诉讼请求[：:]',
            r'^事实[与和]理由[：:]',
            r'^证据[：:]',
            r'^此致$',
            r'^.*人民法院$'
        ]
        
        return any(re.match(pattern, text) for pattern in title_patterns)
    
    def generate_form_config(self) -> Dict[str, Any]:
        """生成Web表单配置"""
        config = {
            'template_name': self.template_name,
            'template_path': self.template_path,
            'form_sections': [],
            'placeholders': self.placeholders,
            'checkboxes': self.checkboxes
        }
        
        # 生成表单分组
        sections = self._generate_form_sections()
        config['form_sections'] = sections
        
        return config
    
    def _generate_form_sections(self) -> List[Dict[str, Any]]:
        """生成表单分组"""
        sections = []
        
        # 当事人信息分组
        party_section = self._create_party_section()
        if party_section:
            sections.append(party_section)
        
        # 案件信息分组
        case_section = self._create_case_section()
        if case_section:
            sections.append(case_section)
        
        # 复选框分组
        checkbox_section = self._create_checkbox_section()
        if checkbox_section:
            sections.append(checkbox_section)
        
        # 其他字段分组
        other_section = self._create_other_section()
        if other_section:
            sections.append(other_section)
        
        return sections
    
    def _create_party_section(self) -> Dict[str, Any]:
        """创建当事人信息分组"""
        party_fields = []
        
        for placeholder in self.placeholders:
            if any(keyword in placeholder for keyword in ['原告', '被告', '第三人', '姓名', '性别', '出生', '身份证', '住址', '联系']):
                field_type = self._infer_field_type(placeholder)
                party_fields.append({
                    'name': placeholder,
                    'label': placeholder,
                    'type': field_type,
                    'required': '姓名' in placeholder or '名称' in placeholder,
                    'placeholder': f'请输入{placeholder}'
                })
        
        if party_fields:
            return {
                'title': '当事人信息',
                'type': 'party_info',
                'fields': party_fields
            }
        return None

    def _create_case_section(self) -> Dict[str, Any]:
        """创建案件信息分组"""
        case_fields = []

        for placeholder in self.placeholders:
            if any(keyword in placeholder for keyword in ['案由', '诉讼请求', '事实', '理由', '金额', '日期', '法院']):
                field_type = self._infer_field_type(placeholder)
                case_fields.append({
                    'name': placeholder,
                    'label': placeholder,
                    'type': field_type,
                    'required': '案由' in placeholder,
                    'placeholder': f'请输入{placeholder}'
                })

        if case_fields:
            return {
                'title': '案件信息',
                'type': 'case_info',
                'fields': case_fields
            }
        return None

    def _create_checkbox_section(self) -> Dict[str, Any]:
        """创建复选框分组"""
        if not self.checkboxes:
            return None

        checkbox_fields = []
        for idx, checkbox in enumerate(self.checkboxes):
            # 提取复选框文本（去除复选框符号）
            text = checkbox['text']
            for char in ["□", "☐", "☑", "■", "✓", "✔"]:
                text = text.replace(char, '').strip()

            if text:
                checkbox_fields.append({
                    'name': f'checkbox_{idx}',
                    'label': text,
                    'type': 'checkbox',
                    'location': checkbox['location'],
                    'original_text': checkbox['text']
                })

        if checkbox_fields:
            return {
                'title': '选择项',
                'type': 'checkboxes',
                'fields': checkbox_fields
            }
        return None

    def _create_other_section(self) -> Dict[str, Any]:
        """创建其他字段分组"""
        other_fields = []

        # 已处理的字段
        processed_keywords = ['原告', '被告', '第三人', '姓名', '性别', '出生', '身份证', '住址', '联系',
                            '案由', '诉讼请求', '事实', '理由', '金额', '日期', '法院']

        for placeholder in self.placeholders:
            if not any(keyword in placeholder for keyword in processed_keywords):
                field_type = self._infer_field_type(placeholder)
                other_fields.append({
                    'name': placeholder,
                    'label': placeholder,
                    'type': field_type,
                    'required': False,
                    'placeholder': f'请输入{placeholder}'
                })

        if other_fields:
            return {
                'title': '其他信息',
                'type': 'other_info',
                'fields': other_fields
            }
        return None

    def _infer_field_type(self, placeholder: str) -> str:
        """推断字段类型"""
        placeholder_lower = placeholder.lower()

        if '日期' in placeholder or '时间' in placeholder:
            return 'date'
        elif '性别' in placeholder:
            return 'select'
        elif '金额' in placeholder or '数额' in placeholder:
            return 'number'
        elif '电话' in placeholder or '手机' in placeholder:
            return 'tel'
        elif '邮箱' in placeholder or 'email' in placeholder:
            return 'email'
        elif '事实' in placeholder or '理由' in placeholder or '请求' in placeholder:
            return 'textarea'
        else:
            return 'text'

    def save_config(self, output_path: str):
        """保存表单配置到文件"""
        config = self.generate_form_config()

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        print(f"表单配置已保存到: {output_path}")
        return config
