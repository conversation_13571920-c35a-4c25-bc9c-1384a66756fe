"""
Web表单生成器
基于模板分析结果生成HTML表单和相关的JavaScript代码
"""

import re
from typing import Dict, Any
from document.web_form_analyzer import TemplateAnalyzer

class WebFormGenerator:
    """Web表单生成器"""
    
    def __init__(self, template_path: str):
        """
        初始化表单生成器
        
        Args:
            template_path (str): Word模板文件路径
        """
        self.template_path = template_path
        self.analyzer = TemplateAnalyzer(template_path)
        self.form_config = self.analyzer.generate_form_config()
    
    def generate_html_form(self) -> str:
        """生成HTML表单，保持Word文档的原始结构"""
        html_parts = []

        # 表单开始
        html_parts.append('<form id="documentForm" class="document-form">')

        # 生成按文档结构排列的编辑区域
        html_parts.append(self._generate_structured_form())

        # 表单按钮
        html_parts.append(self._generate_form_buttons())

        # 表单结束
        html_parts.append('</form>')

        return '\n'.join(html_parts)

    def generate_wysiwyg_edit_form(self, llm_data: dict) -> str:
        """
        生成所见即所得的编辑表单HTML，复用现有的Word样式

        Args:
            llm_data (dict): 来自LLM转换器的表单数据

        Returns:
            str: 所见即所得的编辑表单HTML
        """
        # 重置全局CHECKBOX计数器，确保与CHECKBOX提取器的顺序一致
        self._global_checkbox_counter = 0
        print("📍 [表单CHECKBOX] 重置全局CHECKBOX计数器")

        html_parts = []

        # 表单开始，使用现有的document-form样式
        html_parts.append('<form id="documentForm" class="document-form">')

        # 检查是否为空白表单（直接编辑模式）
        is_blank_form = not llm_data or len(llm_data) == 0

        # 添加编辑提示（使用现有的form-title样式）
        if is_blank_form:
            html_parts.append('''
            <div class="form-title">
                📝 模板编辑 - 空白模板，请填写相关信息
            </div>
            ''')
        else:
            html_parts.append('''
            <div class="form-title">
                📝 文档编辑 - AI分析结果，可直接编辑
            </div>
            ''')

        # 生成按文档结构排列的编辑区域，使用预填充数据和现有样式
        html_parts.append(self._generate_structured_form_with_data(llm_data))

        # 编辑专用按钮（使用现有的form-actions样式）
        html_parts.append(self._generate_wysiwyg_edit_buttons())

        # 表单结束
        html_parts.append('</form>')

        return '\n'.join(html_parts)

    def _generate_structured_form_with_data(self, form_data: dict) -> str:
        """生成带有预填充数据的结构化表单，复用现有的Word文档样式"""
        html_parts = []

        # 使用现有的document-content样式包装整个文档内容
        html_parts.append('<div class="document-content">')

        # 生成文档结构，复用现有的_generate_structured_form逻辑
        structured_form = self._generate_structured_form()

        # 将预填充数据注入到结构化表单中
        filled_form = self._inject_data_into_form(structured_form, form_data)
        html_parts.append(filled_form)

        html_parts.append('</div>')

        return '\n'.join(html_parts)

    def _inject_data_into_form(self, form_html: str, form_data: dict) -> str:
        """将预填充数据注入到现有的表单HTML中，支持完整的预览数据"""
        import re

        # 检查是否包含完整的文档内容
        if '_complete_document_text' in form_data:
            # 使用完整的文档内容重新生成表单
            return self._generate_form_from_complete_content(form_data)

        # 原有的简单数据注入逻辑
        return self._inject_simple_data(form_html, form_data)

    def _inject_simple_data(self, form_html: str, form_data: dict) -> str:
        """简单的数据注入逻辑（原有逻辑）"""
        import re

        print(f"开始注入数据到表单，表单HTML长度: {len(form_html)}, 数据字段数量: {len(form_data)}")

        # 创建字段名称映射，解决LLM转换器生成的字段名与模板字段名不匹配的问题
        field_mapping = {
            # 当事人信息映射
            '原告姓名': '原告自然人1_姓名',
            '被告姓名': '被告自然人1_姓名',
            '原告性别': '原告自然人1_性别',
            '被告性别': '被告自然人1_性别',
            '原告民族': '原告自然人1_民族',
            '被告民族': '被告自然人1_民族',
            '原告出生日期': '原告自然人1_出生日期',
            '被告出生日期': '被告自然人1_出生日期',
            '原告证件号码': '原告自然人1_证件号码',
            '被告证件号码': '被告自然人1_证件号码',
            '原告住址': '原告自然人1_经常居住地',
            '被告住址': '被告自然人1_经常居住地',
            '原告联系电话': '原告自然人1_联系电话',
            '被告联系电话': '被告自然人1_联系电话',
        }

        # 应用字段映射，创建增强的表单数据
        enhanced_form_data = form_data.copy()
        for template_field, data_field in field_mapping.items():
            if data_field in form_data and form_data[data_field]:
                enhanced_form_data[template_field] = form_data[data_field]
                print(f"  字段映射: {template_field} <- {data_field}: {form_data[data_field][:30]}...")

        # 显示一些关键数据用于调试
        key_fields = ['原告姓名', '被告姓名', '离婚理由', '诉讼的事实和理由']
        for field in key_fields:
            if field in enhanced_form_data and enhanced_form_data[field]:
                print(f"  最终数据字段 {field}: {enhanced_form_data[field][:50]}...")

        # 使用增强的表单数据进行注入
        form_data = enhanced_form_data

        # 注入textarea数据
        def replace_textarea(match):
            full_tag = match.group(0)
            name = match.group(1)
            value = form_data.get(name, '')
            print(f"  替换textarea {name}: {'有值' if value else '空值'}")

            # 保持原有的所有属性，只替换内容
            # 找到></textarea>的位置，在其间插入值
            tag_end = full_tag.find('>')
            if tag_end != -1:
                opening_tag = full_tag[:tag_end + 1]
                return f'{opening_tag}{value}</textarea>'
            else:
                return full_tag

        # 匹配textarea标签并替换内容（更宽松的匹配）
        original_html = form_html
        form_html = re.sub(
            r'<textarea[^>]*name="([^"]+)"[^>]*></textarea>',
            replace_textarea,
            form_html
        )

        if form_html != original_html:
            print(f"  ✅ textarea替换完成")
        else:
            print(f"  ⚠️  没有找到textarea标签进行替换")

        # 注入input数据
        def replace_input(match):
            full_tag = match.group(0)
            name = match.group(1)
            value = form_data.get(name, '')
            print(f"  替换input {name}: {'有值' if value else '空值'}")

            # 保持原有的所有属性，只替换或添加value
            if 'value=' in full_tag:
                # 替换现有的value
                new_tag = re.sub(r'value="[^"]*"', f'value="{value}"', full_tag)
            else:
                # 添加value属性
                new_tag = full_tag.replace('>', f' value="{value}">')
            return new_tag

        # 匹配input标签并替换value（更宽松的匹配）
        original_html = form_html
        form_html = re.sub(
            r'<input[^>]*type="text"[^>]*name="([^"]+)"[^>]*>',
            replace_input,
            form_html
        )

        if form_html != original_html:
            print(f"  ✅ input替换完成")
        else:
            print(f"  ⚠️  没有找到input标签进行替换")

        # 注入checkbox数据 - 使用智能匹配
        def replace_checkbox(match):
            full_tag = match.group(0)
            name = match.group(1)

            # 首先尝试直接匹配
            is_checked = form_data.get(name, False)

            # 如果直接匹配失败，尝试智能匹配
            if not is_checked:
                is_checked = self._smart_match_checkbox(name, form_data, form_html)

            print(f"  替换checkbox {name}: {'选中' if is_checked else '未选中'}")

            # 保持原有的所有属性，只添加或移除checked
            if is_checked:
                if 'checked' not in full_tag:
                    new_tag = full_tag.replace('>', ' checked>')
                else:
                    new_tag = full_tag
            else:
                # 移除checked属性
                new_tag = re.sub(r'\s*checked\s*', ' ', full_tag)
            return new_tag

        # 匹配checkbox标签并设置checked状态（更宽松的匹配）
        original_html = form_html
        form_html = re.sub(
            r'<input[^>]*type="checkbox"[^>]*name="([^"]+)"[^>]*>',
            replace_checkbox,
            form_html
        )

        if form_html != original_html:
            print(f"  ✅ checkbox替换完成")
        else:
            print(f"  ⚠️  没有找到checkbox标签进行替换")

        print(f"数据注入完成，最终HTML长度: {len(form_html)}")
        return form_html

    def _smart_match_checkbox(self, checkbox_name: str, form_data: dict, form_html: str) -> bool:
        """
        智能匹配checkbox状态
        根据checkbox周围的文本内容和LLM的checkbox_keywords进行匹配
        """
        import re

        # 获取checkbox_keywords数据
        checkbox_keywords = {}
        for key, value in form_data.items():
            if key.startswith('checkbox_') and not key.startswith('checkbox_-'):
                # 提取关键词（去掉checkbox_前缀）
                keyword = key.replace('checkbox_', '')
                checkbox_keywords[keyword] = value

        if not checkbox_keywords:
            return False

        # 在HTML中查找这个checkbox周围的文本
        checkbox_pattern = f'name="{re.escape(checkbox_name)}"'
        match = re.search(checkbox_pattern, form_html)

        if not match:
            return False

        # 获取checkbox前后的文本内容（前后各100个字符）
        start_pos = max(0, match.start() - 100)
        end_pos = min(len(form_html), match.end() + 100)
        context = form_html[start_pos:end_pos]

        # 清理HTML标签，只保留文本内容
        text_content = re.sub(r'<[^>]+>', '', context)
        text_content = re.sub(r'\s+', ' ', text_content).strip()

        print(f"    checkbox {checkbox_name} 上下文: {text_content[:80]}...")

        # 检查是否包含任何checkbox关键词
        for keyword, is_checked in checkbox_keywords.items():
            if keyword in text_content:
                print(f"    匹配到关键词 '{keyword}': {'选中' if is_checked else '未选中'}")
                return bool(is_checked)

        # 尝试更宽松的匹配（部分关键词匹配）
        for keyword, is_checked in checkbox_keywords.items():
            # 将关键词分解为单词
            keyword_words = keyword.split()
            if len(keyword_words) > 1:
                # 检查是否包含关键词中的任何单词
                for word in keyword_words:
                    if len(word) > 1 and word in text_content:
                        print(f"    部分匹配到关键词 '{word}' (来自 '{keyword}'): {'选中' if is_checked else '未选中'}")
                        return bool(is_checked)

        # 尝试基于案件类型的智能匹配
        if self._is_divorce_case_checkbox(text_content, checkbox_keywords):
            print(f"    基于离婚案件类型匹配: 选中")
            return True

        return False

    def _is_divorce_case_checkbox(self, text_content: str, checkbox_keywords: dict) -> bool:
        """
        基于离婚案件类型的智能checkbox匹配
        """
        # 检查是否是离婚案件
        has_divorce_keywords = any(keyword in checkbox_keywords for keyword in ['离婚纠纷', '婚姻家庭', '离婚'])

        if not has_divorce_keywords:
            return False

        # 定义离婚案件中应该选中的checkbox模式
        divorce_patterns = [
            # 子女抚养相关
            ('有此问题', ['子女', '抚养']),
            ('有财产', ['财产', '夫妻']),
            ('有债务', ['债务', '夫妻']),
            # 电子送达
            ('是', ['电子送达', '送达']),
        ]

        for pattern_text, context_keywords in divorce_patterns:
            if pattern_text in text_content:
                # 检查上下文是否包含相关关键词
                if any(keyword in text_content for keyword in context_keywords):
                    print(f"    离婚案件模式匹配: '{pattern_text}' + {context_keywords}")
                    return True

        return False

    def _generate_form_from_complete_content(self, form_data: dict) -> str:
        """从完整的文档内容生成表单，保持Word文档结构"""
        import re
        from docx import Document
        import tempfile
        import os

        try:
            # 检查是否有中间文档路径
            intermediate_doc_path = form_data.get('_intermediate_doc_path')

            if intermediate_doc_path and os.path.exists(intermediate_doc_path):
                print(f"使用中间文档生成表单: {intermediate_doc_path}")
                return self._generate_form_from_intermediate_doc(intermediate_doc_path, form_data)
            else:
                print("中间文档不存在，尝试重新生成...")
                return self._generate_form_from_recreated_doc(form_data)

        except Exception as e:
            print(f"从完整内容生成表单时出错: {str(e)}")
            import traceback
            print(f"错误详情: {traceback.format_exc()}")
            # 如果出错，回退到简单的数据注入
            return self._inject_simple_data('', form_data)

    def _generate_form_from_intermediate_doc(self, doc_path: str, form_data: dict) -> str:
        """从中间文档生成表单，保持完整的Word结构"""
        from docx import Document

        try:
            # 加载中间文档
            doc = Document(doc_path)
            print(f"成功加载中间文档，段落数: {len(doc.paragraphs)}, 表格数: {len(doc.tables)}")

            # 创建临时的文档分析器
            from document.web_form_analyzer import TemplateAnalyzer
            temp_analyzer = TemplateAnalyzer(doc_path)

            # 使用现有的结构化表单生成逻辑
            html_parts = []
            html_parts.append('<div class="document-content">')

            # 按文档原始结构顺序生成表单
            for element in temp_analyzer.document_structure:
                if element['type'] == 'paragraph':
                    element_html = self._generate_paragraph_form_with_data(element, form_data)
                    html_parts.append(element_html)
                elif element['type'] == 'table':
                    element_html = self._generate_table_form_with_data(element, form_data)
                    html_parts.append(element_html)

            html_parts.append('</div>')

            return '\n'.join(html_parts)

        except Exception as e:
            print(f"从中间文档生成表单时出错: {str(e)}")
            raise

    def _generate_form_from_recreated_doc(self, form_data: dict) -> str:
        """重新创建中间文档并生成表单"""
        try:
            print("重新创建中间文档...")

            # 从预览数据中提取当事人信息
            party_info = {}
            for key, value in form_data.items():
                if any(party_type in key for party_type in ['原告', '被告', '第三人']) and '列表' in key:
                    party_info[key] = value

            print(f"提取到当事人信息字段: {list(party_info.keys())}")

            # 重新创建中间文档
            from document.intermediate_processor import IntermediateDocumentProcessor
            intermediate_processor = IntermediateDocumentProcessor(
                template_path=self.template_path,
                debug=True
            )

            intermediate_doc, remaining_placeholders = intermediate_processor.create_intermediate_document(party_info)

            # 保存临时中间文档
            import tempfile
            import os
            temp_dir = tempfile.mkdtemp()
            temp_doc_path = os.path.join(temp_dir, 'temp_intermediate.docx')
            intermediate_doc.save(temp_doc_path)

            print(f"临时中间文档已保存: {temp_doc_path}")

            # 使用临时中间文档生成表单
            return self._generate_form_from_intermediate_doc(temp_doc_path, form_data)

        except Exception as e:
            print(f"重新创建中间文档时出错: {str(e)}")
            raise

    def _generate_paragraph_form_with_data(self, element: dict, form_data: dict) -> str:
        """生成带有预填充数据的段落表单"""
        text = element['text']
        is_title = element.get('is_title', False)

        # 处理占位符，将其转换为输入框
        placeholder_pattern = r'\{\{([^}]+)\}\}'

        def replace_placeholder(match):
            placeholder = match.group(1).strip()
            field_id = f"field_{placeholder.replace(' ', '_').replace('（', '_').replace('）', '_')}"

            # 获取预填充值，支持当事人索引格式
            value = self._get_placeholder_value(placeholder, form_data)

            # 统一使用多行输入框，自动调整高度
            return f'<textarea name="{placeholder}" id="{field_id}" class="inline-textarea auto-resize" placeholder="请输入{placeholder}" rows="1">{value}</textarea>'

        # 替换占位符为输入控件
        form_text = re.sub(placeholder_pattern, replace_placeholder, text)

        # 处理复选框
        form_text = self._replace_checkboxes_in_text_with_data(form_text, text, form_data)

        # 确定段落样式
        css_class = 'document-title' if is_title else 'document-paragraph'

        return f'    <div class="{css_class}">{form_text}</div>'

    def _generate_table_form_with_data(self, element: dict, form_data: dict) -> str:
        """生成带有预填充数据的表格表单，正确处理合并单元格"""
        html_parts = []
        html_parts.append('    <table class="document-table">')

        for row_info in element['structure']:
            html_parts.append('      <tr>')

            for cell_info in row_info['cells']:
                cell_html = self._generate_table_cell_form_with_data(cell_info, form_data)

                # 检查是否是合并单元格
                colspan = cell_info.get('colspan', 1)
                if colspan > 1:
                    html_parts.append(f'        <td colspan="{colspan}">{cell_html}</td>')
                else:
                    html_parts.append(f'        <td>{cell_html}</td>')

            html_parts.append('      </tr>')

        html_parts.append('    </table>')

        return '\n'.join(html_parts)

    def _generate_table_cell_form_with_data(self, cell_info: dict, form_data: dict) -> str:
        """生成带有预填充数据的表格单元格表单内容"""
        cell_content_parts = []

        for para_info in cell_info['paragraphs']:
            para_text = para_info['text']

            # 处理占位符
            placeholder_pattern = r'\{\{([^}]+)\}\}'

            def replace_placeholder(match):
                placeholder = match.group(1).strip()
                field_id = f"field_{placeholder.replace(' ', '_').replace('（', '_').replace('）', '_')}"

                # 获取预填充值，支持当事人索引格式
                value = self._get_placeholder_value(placeholder, form_data)

                # 统一使用多行输入框，自动调整高度
                return f'<textarea name="{placeholder}" id="{field_id}" class="cell-textarea auto-resize" placeholder="请输入{placeholder}" rows="1">{value}</textarea>'

            # 替换占位符为输入控件
            form_text = re.sub(placeholder_pattern, replace_placeholder, para_text)

            # 处理复选框
            form_text = self._replace_checkboxes_in_text_with_data(form_text, para_text, form_data)

            cell_content_parts.append(form_text)

        return '<br>'.join(cell_content_parts) if cell_content_parts else ''

    def _extract_placeholders_from_doc(self, doc) -> Dict[str, str]:
        """
        从Word文档中提取所有占位符，生成空白表单数据

        Args:
            doc: Word文档对象

        Returns:
            dict: 占位符名称到空字符串的映射
        """
        placeholders = set()
        placeholder_pattern = r'\{\{([^}]+)\}\}'

        # 从段落中提取占位符
        for para in doc.paragraphs:
            matches = re.findall(placeholder_pattern, para.text)
            for match in matches:
                placeholders.add(match.strip())

        # 从表格中提取占位符
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for para in cell.paragraphs:
                        matches = re.findall(placeholder_pattern, para.text)
                        for match in matches:
                            placeholders.add(match.strip())

        # 返回占位符到空字符串的映射
        return {placeholder: "" for placeholder in placeholders}

    def _get_placeholder_value(self, placeholder: str, form_data: dict) -> str:
        """
        获取占位符的值，支持当事人索引格式

        Args:
            placeholder (str): 占位符名称，可能是普通格式或当事人索引格式
            form_data (dict): 表单数据

        Returns:
            str: 占位符对应的值
        """
        # 首先尝试直接获取
        if placeholder in form_data:
            value = form_data[placeholder]
            return str(value) if value is not None else ''

        # 检查是否是当事人索引格式：原告_自然人__1_姓名
        import re
        party_pattern = r'^(原告|被告|第三人)_([^_]+)_(\d+)_(.+)$'
        match = re.match(party_pattern, placeholder)

        if match:
            party_role = match.group(1)  # 原告、被告、第三人
            party_subtype = match.group(2)  # 自然人_、法人_非法人组织_
            party_index = int(match.group(3))  # 1, 2, 3...
            field_name = match.group(4)  # 姓名、身份证号等

            # 构建当事人列表的键名
            party_subtype_clean = party_subtype.replace('_', '、').rstrip('、')
            list_key = f"{party_role}（{party_subtype_clean}）列表"

            print(f"解析当事人占位符: {placeholder}")
            print(f"  - 角色: {party_role}")
            print(f"  - 子类型: {party_subtype_clean}")
            print(f"  - 索引: {party_index}")
            print(f"  - 字段: {field_name}")
            print(f"  - 列表键: {list_key}")

            # 从当事人列表中获取值
            if list_key in form_data and isinstance(form_data[list_key], list):
                party_list = form_data[list_key]
                if 0 < party_index <= len(party_list):
                    party_data = party_list[party_index - 1]  # 索引从1开始，列表从0开始
                    if field_name in party_data:
                        value = party_data[field_name]
                        print(f"  - 找到值: {value}")
                        return str(value) if value is not None else ''
                    else:
                        print(f"  - 字段 {field_name} 不存在于当事人数据中")
                else:
                    print(f"  - 当事人索引 {party_index} 超出范围，列表长度: {len(party_list)}")
            else:
                print(f"  - 当事人列表 {list_key} 不存在或不是列表")
                if list_key in form_data:
                    print(f"    实际类型: {type(form_data[list_key])}")

        # 如果都没找到，返回空字符串
        return ''

    def _is_title_paragraph(self, text: str) -> bool:
        """判断段落是否是标题"""
        # 简单的标题判断逻辑
        title_keywords = ['起诉状', '申请书', '答辩书', '上诉状']
        return any(keyword in text for keyword in title_keywords) and len(text.strip()) < 50

    def _convert_paragraph_to_form(self, para_text: str, form_data: dict, is_title: bool = False) -> str:
        """将段落文本转换为表单HTML"""
        import re

        # 处理占位符，将其转换为输入框
        placeholder_pattern = r'\{\{([^}]+)\}\}'

        def replace_placeholder(match):
            placeholder = match.group(1).strip()
            field_id = f"field_{placeholder.replace(' ', '_').replace('（', '_').replace('）', '_')}"

            # 获取预填充值
            value = form_data.get(placeholder, '')

            # 统一使用多行输入框，自动调整高度
            return f'<textarea name="{placeholder}" id="{field_id}" class="inline-textarea auto-resize" placeholder="请输入{placeholder}" rows="1">{value}</textarea>'

        # 替换占位符为输入控件
        form_text = re.sub(placeholder_pattern, replace_placeholder, para_text)

        # 处理复选框
        form_text = self._replace_checkboxes_in_text_with_data(form_text, para_text, form_data)

        # 确定段落样式
        css_class = 'document-title' if is_title else 'document-paragraph'

        return f'    <div class="{css_class}">{form_text}</div>'

    def _replace_checkboxes_in_text_with_data(self, form_text: str, original_text: str, form_data: dict) -> str:
        """在文本中替换复选框为HTML复选框控件，并设置预填充状态"""
        # 使用统一的复选框字符定义
        from document.checkbox.constants import CHECKBOX_CHARS
        checkbox_chars = CHECKBOX_CHARS

        print(f"📍 [表单CHECKBOX] 处理文本: {original_text[:50]}...")
        print(f"📍 [表单CHECKBOX] 可用的CHECKBOX数据键: {[k for k in form_data.keys() if 'checkbox' in k.lower()]}")

        # 使用全局计数器确保与CHECKBOX提取器的顺序一致
        if not hasattr(self, '_global_checkbox_counter'):
            self._global_checkbox_counter = 0

        # 按字符位置顺序处理复选框（确保与扫描顺序一致）
        text_chars = list(form_text)
        for char_idx, char in enumerate(text_chars):
            if char in checkbox_chars:
                self._global_checkbox_counter += 1

                # 使用全局计数器匹配CHECKBOX状态
                is_checked = self._get_checkbox_state(self._global_checkbox_counter, original_text, form_data)
                checked_attr = ' checked' if is_checked else ''

                # 为每个复选框创建标准ID格式（与DocumentGenerator一致）
                checkbox_id = f"checkbox_{self._global_checkbox_counter}"

                print(f"📍 [表单CHECKBOX] 复选框 #{self._global_checkbox_counter}: {char} -> {'选中' if is_checked else '未选中'} (ID: {checkbox_id})")

                # 创建HTML复选框
                checkbox_html = f'<input type="checkbox" name="{checkbox_id}" id="{checkbox_id}" class="inline-checkbox" value="1"{checked_attr}>'

                # 替换字符
                text_chars[char_idx] = checkbox_html

        return ''.join(text_chars)

    def _get_checkbox_state(self, checkbox_index: int, original_text: str, form_data: dict) -> bool:
        """获取复选框的状态，尝试多种匹配方式"""

        # 方式1: 尝试匹配 checkbox_N 格式
        checkbox_key = f"checkbox_{checkbox_index}"
        if checkbox_key in form_data:
            print(f"📍 [表单CHECKBOX] 通过 {checkbox_key} 匹配到状态: {form_data[checkbox_key]}")
            return bool(form_data[checkbox_key])

        # 方式2: 尝试匹配所有checkbox_开头的键
        checkbox_keys = [k for k in form_data.keys() if k.startswith('checkbox_')]
        if checkbox_keys and checkbox_index <= len(checkbox_keys):
            # 按索引顺序获取
            sorted_keys = sorted(checkbox_keys)
            if checkbox_index - 1 < len(sorted_keys):
                key = sorted_keys[checkbox_index - 1]
                print(f"📍 [表单CHECKBOX] 通过索引 {checkbox_index} 匹配到键 {key}: {form_data[key]}")
                return bool(form_data[key])

        # 方式3: 基于文本内容的智能匹配
        checkbox_keywords = form_data.get('checkbox_keywords', {})
        if checkbox_keywords:
            for keyword, is_checked in checkbox_keywords.items():
                if keyword in original_text:
                    print(f"📍 [表单CHECKBOX] 通过关键词 '{keyword}' 匹配: {is_checked}")
                    return bool(is_checked)

        # 方式4: 检查是否有任何checkbox相关的数据
        all_checkbox_data = {k: v for k, v in form_data.items() if 'checkbox' in k.lower()}
        if all_checkbox_data:
            print(f"📍 [表单CHECKBOX] 找到CHECKBOX相关数据: {all_checkbox_data}")
            # 如果有checkbox数据但没有匹配到，可能需要更智能的匹配

        print(f"📍 [表单CHECKBOX] 复选框 #{checkbox_index} 未找到匹配状态，默认未选中")
        return False

    def _generate_section_with_data(self, section: dict, form_data: dict) -> str:
        """生成带有预填充数据的表单区域"""
        html_parts = []

        # 区域标题
        if section.get('title'):
            html_parts.append(f'<div class="form-section">')
            html_parts.append(f'<h3 class="section-title">{section["title"]}</h3>')

        # 生成字段
        for field in section.get('fields', []):
            field_html = self._generate_field_with_data(field, form_data)
            if field_html:
                html_parts.append(field_html)

        if section.get('title'):
            html_parts.append('</div>')

        return '\n'.join(html_parts)

    def _generate_field_with_data(self, field: dict, form_data: dict) -> str:
        """生成带有预填充数据的表单字段"""
        field_name = field.get('name', '')
        field_type = field.get('type', 'text')
        field_label = field.get('label', field_name)
        field_value = form_data.get(field_name, '')

        # 转换复选框字段值
        if field_type == 'checkbox':
            field_value = bool(field_value)

        html_parts = []
        html_parts.append('<div class="form-field">')
        html_parts.append(f'<label for="{field_name}">{field_label}:</label>')

        if field_type == 'textarea':
            html_parts.append(f'''
            <textarea id="{field_name}" name="{field_name}"
                      class="form-control auto-resize"
                      placeholder="请输入{field_label}">{field_value}</textarea>
            ''')
        elif field_type == 'checkbox':
            checked = 'checked' if field_value else ''
            html_parts.append(f'''
            <input type="checkbox" id="{field_name}" name="{field_name}"
                   class="form-control" {checked}>
            ''')
        elif field_type == 'select':
            options = field.get('options', [])
            html_parts.append(f'<select id="{field_name}" name="{field_name}" class="form-control">')
            for option in options:
                selected = 'selected' if str(option) == str(field_value) else ''
                html_parts.append(f'<option value="{option}" {selected}>{option}</option>')
            html_parts.append('</select>')
        else:
            html_parts.append(f'''
            <input type="{field_type}" id="{field_name}" name="{field_name}"
                   class="form-control" value="{field_value}"
                   placeholder="请输入{field_label}">
            ''')

        html_parts.append('</div>')
        return '\n'.join(html_parts)

    def _generate_document_structure_preview(self, llm_data: dict) -> str:
        """生成文档结构预览"""
        template_type = llm_data.get('_template_type', '未知模板')

        return f'''
        <div class="document-structure-preview">
            <h4><i class="fas fa-file-alt"></i> 文档结构：{template_type}</h4>
            <div class="structure-info">
                <span class="badge bg-primary">当事人信息</span>
                <span class="badge bg-info">案件事实</span>
                <span class="badge bg-warning">诉讼请求</span>
                <span class="badge bg-success">其他内容</span>
            </div>
            <p class="text-muted">💡 提示：直接在下方表单中编辑内容，修改后的内容将直接应用到最终文档中。</p>
        </div>
        '''

    def _generate_wysiwyg_edit_buttons(self) -> str:
        """生成编辑专用按钮，只保留保存并生成和重置识别结果两个按钮，放在右侧"""
        return '''
        <div class="form-actions" style="justify-content: flex-end; gap: 15px;">
            <button type="button" id="generateBtn" class="btn btn-primary">
                💾 保存并生成
            </button>
            <button type="button" id="resetBtn" class="btn btn-warning">
                🔄 重置识别结果
            </button>
        </div>
        '''



    def _generate_structured_form(self) -> str:
        """生成完全按照Word文档结构排列的表单"""
        html_parts = []
        html_parts.append('<div class="document-form-section">')
        html_parts.append('  <h3 class="form-title">文档编辑</h3>')
        html_parts.append('  <div class="document-content">')

        # 按文档原始结构顺序生成表单
        for element in self.analyzer.document_structure:
            if element['type'] == 'paragraph':
                element_html = self._generate_paragraph_form(element)
                html_parts.append(element_html)
            elif element['type'] == 'table':
                element_html = self._generate_table_form(element)
                html_parts.append(element_html)

        html_parts.append('  </div>')
        html_parts.append('</div>')

        return '\n'.join(html_parts)

    def _generate_paragraph_form(self, element: Dict[str, Any]) -> str:
        """生成段落表单，保持原始格式和结构"""
        text = element['text']
        is_title = element.get('is_title', False)

        # 处理占位符，将其转换为输入框
        placeholder_pattern = r'\{\{([^}]+)\}\}'

        def replace_placeholder(match):
            placeholder = match.group(1).strip()
            field_id = f"field_{placeholder.replace(' ', '_').replace('（', '_').replace('）', '_')}"

            # 统一使用多行输入框，自动调整高度
            return f'<textarea name="{placeholder}" id="{field_id}" class="inline-textarea auto-resize" placeholder="请输入{placeholder}" rows="1"></textarea>'

        # 替换占位符为输入控件
        form_text = re.sub(placeholder_pattern, replace_placeholder, text)

        # 处理复选框 - 改进的识别和替换逻辑
        form_text = self._replace_checkboxes_in_text(form_text, text)

        # 确定段落样式
        css_class = 'document-title' if is_title else 'document-paragraph'

        return f'    <div class="{css_class}">{form_text}</div>'

    def _generate_table_form(self, element: Dict[str, Any]) -> str:
        """生成表格表单，完全保持原始表格结构，正确处理合并单元格"""
        html_parts = []
        html_parts.append('    <table class="document-table">')

        for row_info in element['structure']:
            html_parts.append('      <tr>')

            for cell_info in row_info['cells']:
                cell_html = self._generate_table_cell_form(cell_info)

                # 检查是否是合并单元格
                colspan = cell_info.get('colspan', 1)
                if colspan > 1:
                    html_parts.append(f'        <td colspan="{colspan}">{cell_html}</td>')
                else:
                    html_parts.append(f'        <td>{cell_html}</td>')

            html_parts.append('      </tr>')

        html_parts.append('    </table>')

        return '\n'.join(html_parts)

    def _generate_table_cell_form(self, cell_info: Dict[str, Any]) -> str:
        """生成表格单元格表单内容"""
        cell_content_parts = []

        for para_info in cell_info['paragraphs']:
            para_text = para_info['text']

            # 处理占位符
            placeholder_pattern = r'\{\{([^}]+)\}\}'

            def replace_placeholder(match):
                placeholder = match.group(1).strip()
                field_id = f"field_{placeholder.replace(' ', '_').replace('（', '_').replace('）', '_')}"

                # 统一使用多行输入框，自动调整高度
                return f'<textarea name="{placeholder}" id="{field_id}" class="cell-textarea auto-resize" placeholder="请输入{placeholder}" rows="1"></textarea>'

            # 替换占位符为输入控件
            form_text = re.sub(placeholder_pattern, replace_placeholder, para_text)

            # 处理复选框 - 改进的识别和替换逻辑
            form_text = self._replace_checkboxes_in_text(form_text, para_text)

            cell_content_parts.append(form_text)

        return '<br>'.join(cell_content_parts) if cell_content_parts else ''

    def _replace_checkboxes_in_text(self, form_text: str, original_text: str) -> str:
        """在文本中替换复选框为HTML复选框控件"""
        # 直接在文本中查找并替换复选框字符
        checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓"]

        # 为每个复选框字符创建一个唯一的HTML复选框
        checkbox_counter = 0

        for char in checkbox_chars:
            while char in form_text:
                checkbox_counter += 1

                # 为每个复选框创建标准ID格式（与DocumentGenerator一致）
                checkbox_id = f"checkbox_{checkbox_counter}"

                print(f"📍 [表单CHECKBOX-传统] 复选框 #{checkbox_counter}: {char} -> (ID: {checkbox_id})")

                # 创建HTML复选框
                checkbox_html = f'<input type="checkbox" name="{checkbox_id}" id="{checkbox_id}" class="inline-checkbox" value="1">'

                # 替换第一个找到的复选框字符
                form_text = form_text.replace(char, checkbox_html, 1)

        return form_text







    def _generate_section_html(self, section: Dict[str, Any]) -> str:
        """生成分组HTML"""
        html_parts = []
        
        # 分组标题
        html_parts.append(f'<div class="form-section" data-section-type="{section["type"]}">')
        html_parts.append(f'  <h3 class="section-title">{section["title"]}</h3>')
        html_parts.append('  <div class="section-content">')
        
        # 生成字段
        for field in section['fields']:
            field_html = self._generate_field_html(field)
            html_parts.append(f'    {field_html}')
        
        html_parts.append('  </div>')
        html_parts.append('</div>')
        
        return '\n'.join(html_parts)
    
    def _generate_field_html(self, field: Dict[str, Any]) -> str:
        """生成字段HTML"""
        field_type = field['type']
        field_name = field['name']
        field_label = field['label']
        required = field.get('required', False)
        placeholder = field.get('placeholder', '')
        
        # 字段容器开始
        html_parts = []
        html_parts.append(f'<div class="form-field" data-field-type="{field_type}">')
        
        # 标签
        required_mark = ' <span class="required">*</span>' if required else ''
        html_parts.append(f'  <label for="{field_name}" class="field-label">{field_label}{required_mark}</label>')
        
        # 输入控件
        if field_type == 'text':
            html_parts.append(f'  <input type="text" id="{field_name}" name="{field_name}" placeholder="{placeholder}" {"required" if required else ""}>')
        
        elif field_type == 'textarea':
            html_parts.append(f'  <textarea id="{field_name}" name="{field_name}" placeholder="{placeholder}" rows="4" {"required" if required else ""}></textarea>')
        
        elif field_type == 'number':
            html_parts.append(f'  <input type="number" id="{field_name}" name="{field_name}" placeholder="{placeholder}" {"required" if required else ""}>')
        
        elif field_type == 'date':
            html_parts.append(f'  <input type="date" id="{field_name}" name="{field_name}" {"required" if required else ""}>')
        
        elif field_type == 'tel':
            html_parts.append(f'  <input type="tel" id="{field_name}" name="{field_name}" placeholder="{placeholder}" {"required" if required else ""}>')
        
        elif field_type == 'email':
            html_parts.append(f'  <input type="email" id="{field_name}" name="{field_name}" placeholder="{placeholder}" {"required" if required else ""}>')
        
        elif field_type == 'select':
            if '性别' in field_label:
                html_parts.append(f'  <select id="{field_name}" name="{field_name}" {"required" if required else ""}>')
                html_parts.append('    <option value="">请选择</option>')
                html_parts.append('    <option value="男">男</option>')
                html_parts.append('    <option value="女">女</option>')
                html_parts.append('  </select>')
            else:
                html_parts.append(f'  <select id="{field_name}" name="{field_name}" {"required" if required else ""}>')
                html_parts.append('    <option value="">请选择</option>')
                html_parts.append('  </select>')
        
        elif field_type == 'checkbox':
            html_parts.append(f'  <div class="checkbox-wrapper">')
            html_parts.append(f'    <input type="checkbox" id="{field_name}" name="{field_name}" value="1">')
            html_parts.append(f'    <label for="{field_name}" class="checkbox-label">{field_label}</label>')
            html_parts.append(f'  </div>')
        
        # 字段容器结束
        html_parts.append('</div>')
        
        return '\n'.join(html_parts)
    
    def _generate_form_buttons(self) -> str:
        """生成表单按钮"""
        return '''
<div class="form-actions">
  <button type="button" id="generateBtn" class="btn btn-primary">生成Word文档</button>
  <button type="reset" class="btn btn-outline">重置表单</button>
</div>'''
    
    def generate_css(self) -> str:
        """生成CSS样式"""
        return '''
/* 文档表单样式 */
.document-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}



/* 文档结构表单样式 */
.document-form-section {
  margin-top: 20px;
}

.form-title {
  background: #f5f5f5;
  margin: 0 0 20px 0;
  padding: 15px 20px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.document-content {
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 30px;
  font-family: "Microsoft YaHei", "SimSun", serif;
  line-height: 1.8;
  font-size: 14px;
}

/* 文档段落样式 */
.document-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin: 20px 0;
  line-height: 1.5;
}

.document-paragraph {
  margin: 15px 0;
  text-indent: 2em;
  line-height: 1.8;
}

/* 文档表格样式 */
.document-table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
  border: 1px solid #000;
}

.document-table td {
  border: 1px solid #000;
  padding: 8px 12px;
  vertical-align: top;
  line-height: 1.6;
}

/* 多行输入框样式 */

.inline-textarea, .cell-textarea {
  border: 1px solid #007bff;
  border-radius: 3px;
  padding: 4px 6px;
  font-size: inherit;
  font-family: inherit;
  color: #007bff;
  font-weight: 500;
  resize: both;
  min-width: 120px;
  min-height: 24px;
  background: rgba(0, 123, 255, 0.02);
  line-height: 1.4;
  overflow: hidden;
  transition: border-color 0.3s, background-color 0.3s;
}

.inline-textarea:focus, .cell-textarea:focus {
  outline: none;
  border-color: #0056b3;
  background: rgba(0, 123, 255, 0.05);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

/* 自动调整高度的样式 */
.auto-resize {
  overflow-y: hidden;
  resize: both;
}

/* 段落中的多行输入框 */
.document-paragraph .inline-textarea {
  min-width: 150px;
  max-width: calc(100% - 20px);
  width: auto;
}

/* 表格中的多行输入框 */
.document-table .cell-textarea {
  width: 100%;
  min-width: 80px;
  max-width: 100%;
  box-sizing: border-box;
}

/* 表格单元格样式调整 */
.document-table td {
  position: relative;
  overflow: visible;
}

/* 防止输入框超出表格边界 */
.document-table .cell-textarea {
  max-width: calc(100% - 8px);
}



.inline-checkbox, .cell-checkbox {
  margin: 0 3px;
  transform: scale(1.2);
  accent-color: #007bff;
}



.form-section {
  margin-bottom: 30px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
}

.section-title {
  background: #f5f5f5;
  margin: 0;
  padding: 15px 20px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
}

.section-content {
  padding: 20px;
}

.form-field {
  margin-bottom: 20px;
}

.field-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #e74c3c;
}

input[type="text"],
input[type="number"],
input[type="date"],
input[type="tel"],
input[type="email"],
select,
textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

textarea {
  resize: vertical;
  min-height: 100px;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-wrapper input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkbox-label {
  margin: 0;
  cursor: pointer;
}

.form-actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
  text-align: center;
  display: flex;
  gap: 15px;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-outline {
  background: transparent;
  color: #6c757d;
  border: 1px solid #6c757d;
}

.btn-outline:hover {
  background: #6c757d;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-form {
    margin: 10px;
    padding: 15px;
  }
  
  .section-content {
    padding: 15px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}'''
    
    def generate_javascript(self) -> str:
        """生成JavaScript代码"""
        return '''
// 文档表单JavaScript
class DocumentFormHandler {
  constructor() {
    this.form = document.getElementById('documentForm');
    this.generateBtn = document.getElementById('generateBtn');

    this.init();
  }
  
  init() {
    // 绑定事件
    if (this.generateBtn) {
      this.generateBtn.addEventListener('click', () => this.generateDocument());
    }

    // 表单验证
    this.form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.generateDocument();
    });

    // 初始化自动调整高度的文本框
    this.initAutoResizeTextareas();
  }

  // 初始化自动调整高度的文本框
  initAutoResizeTextareas() {
    const textareas = this.form.querySelectorAll('textarea.auto-resize');

    textareas.forEach(textarea => {
      // 设置初始高度
      this.adjustTextareaHeight(textarea);

      // 监听输入事件
      textarea.addEventListener('input', () => {
        this.adjustTextareaHeight(textarea);
      });

      // 监听粘贴事件
      textarea.addEventListener('paste', () => {
        setTimeout(() => {
          this.adjustTextareaHeight(textarea);
        }, 10);
      });

      // 监听键盘事件
      textarea.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          setTimeout(() => {
            this.adjustTextareaHeight(textarea);
          }, 10);
        }
      });
    });
  }

  // 调整文本框高度
  adjustTextareaHeight(textarea) {
    // 重置高度以获取正确的scrollHeight
    textarea.style.height = 'auto';

    // 计算所需高度
    const minHeight = 24; // 最小高度
    const maxHeight = 200; // 最大高度
    const scrollHeight = textarea.scrollHeight;

    // 设置新高度
    const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
    textarea.style.height = newHeight + 'px';

    // 如果内容超过最大高度，显示滚动条
    if (scrollHeight > maxHeight) {
      textarea.style.overflowY = 'auto';
    } else {
      textarea.style.overflowY = 'hidden';
    }
  }
  
  // 收集表单数据
  collectFormData() {
    const data = {};

    // 收集所有多行输入框
    const textareas = this.form.querySelectorAll('textarea');
    textareas.forEach(textarea => {
      if (textarea.name) {
        data[textarea.name] = textarea.value;
      }
    });

    // 处理复选框
    const checkboxes = this.form.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
      data[checkbox.name] = checkbox.checked;
    });

    return data;
  }
  

  
  // 生成Word文档
  async generateDocument() {
    const data = this.collectFormData();
    
    // 验证必填字段
    if (!this.validateForm()) {
      return;
    }
    
    try {
      this.generateBtn.disabled = true;
      this.generateBtn.textContent = '生成中...';
      
      const response = await fetch('/api/generate-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          template_path: ''' + f'"{self.template_path}"' + ''',
          form_data: data
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.download_url) {
          // 下载文件
          window.location.href = result.download_url;
        }
      } else {
        throw new Error('文档生成失败');
      }
    } catch (error) {
      alert('生成失败: ' + error.message);
    } finally {
      this.generateBtn.disabled = false;
      this.generateBtn.textContent = '生成Word文档';
    }
  }
  
  // 表单验证
  validateForm() {
    const requiredFields = this.form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        field.style.borderColor = '#e74c3c';
        isValid = false;
      } else {
        field.style.borderColor = '#ddd';
      }
    });
    
    if (!isValid) {
      alert('请填写所有必填字段');
    }
    
    return isValid;
  }
  

}

// 初始化表单处理器
document.addEventListener('DOMContentLoaded', () => {
  new DocumentFormHandler();
});'''
    
    def generate_complete_page(self, title: str = None) -> str:
        """生成完整的HTML页面"""
        if not title:
            title = f"{self.form_config['template_name']} - 在线编辑"
        
        html = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
{self.generate_css()}
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">{title}</h1>
        {self.generate_html_form()}
    </div>
    
    <script>
{self.generate_javascript()}
    </script>
</body>
</html>'''
        
        return html
    
    def save_form_page(self, output_path: str, title: str = None):
        """保存完整的表单页面到文件"""
        html = self.generate_complete_page(title)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html)
        
        print(f"表单页面已保存到: {output_path}")
        return output_path
