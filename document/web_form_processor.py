"""
Web表单数据处理器
将Web表单数据转换为Word文档生成所需的格式
"""

import os
import json
import logging
from typing import Dict, List, Any
from document.generator import DocumentGenerator
from document.web_form_analyzer import TemplateAnalyzer
from document.structured_document_generator import StructuredDocumentGenerator

logger = logging.getLogger(__name__)

class WebFormProcessor:
    """Web表单数据处理器"""
    
    def __init__(self, template_path: str):
        """
        初始化表单数据处理器
        
        Args:
            template_path (str): Word模板文件路径
        """
        self.template_path = template_path
        self.analyzer = TemplateAnalyzer(template_path)
        self.form_config = self.analyzer.generate_form_config()
    
    def process_form_data(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理表单数据，转换为Word文档生成所需的格式
        
        Args:
            form_data (dict): 来自Web表单的数据
            
        Returns:
            dict: 转换后的数据，可用于Word文档生成
        """
        print(f"开始处理表单数据，字段数量: {len(form_data)}")
        
        # 初始化处理后的数据
        processed_data = {}
        
        # 处理占位符字段
        self._process_placeholder_fields(form_data, processed_data)
        
        # 处理复选框字段
        self._process_checkbox_fields(form_data, processed_data)
        
        # 添加默认值和计算字段
        self._add_default_values(processed_data)
        
        print(f"表单数据处理完成，生成字段数量: {len(processed_data)}")
        return processed_data
    
    def _process_placeholder_fields(self, form_data: Dict[str, Any], processed_data: Dict[str, Any]):
        """处理占位符字段"""
        for placeholder in self.analyzer.placeholders:
            if placeholder in form_data:
                value = form_data[placeholder]
                # 处理空值
                if value is None or value == '':
                    processed_data[placeholder] = ''
                else:
                    processed_data[placeholder] = str(value)
                    
                print(f"处理占位符: {placeholder} = {processed_data[placeholder]}")
    
    def _process_checkbox_fields(self, form_data: Dict[str, Any], processed_data: Dict[str, Any]):
        """处理复选框字段"""
        checkbox_data = {}
        
        for i, checkbox_info in enumerate(self.analyzer.checkboxes):
            checkbox_name = f'checkbox_{i}'
            is_checked = form_data.get(checkbox_name, False)
            
            # 转换布尔值
            if isinstance(is_checked, str):
                is_checked = is_checked.lower() in ['true', '1', 'on', 'yes']
            elif isinstance(is_checked, (int, float)):
                is_checked = bool(is_checked)
            
            checkbox_data[checkbox_name] = is_checked
            
            # 记录复选框状态用于调试
            if is_checked:
                print(f"复选框选中: {checkbox_info['location']} - {checkbox_info['text'][:30]}...")
        
        # 将复选框数据添加到处理后的数据中
        processed_data.update(checkbox_data)
        
        # 为复选框处理创建特殊的数据结构
        processed_data['_checkbox_selections'] = checkbox_data
    
    def _add_default_values(self, processed_data: Dict[str, Any]):
        """添加默认值和计算字段"""
        # 添加当前日期
        from datetime import datetime
        current_date = datetime.now().strftime('%Y年%m月%d日')
        
        # 如果没有填写日期相关字段，使用当前日期
        date_fields = [field for field in processed_data.keys() if '日期' in field or '时间' in field]
        for field in date_fields:
            if not processed_data.get(field):
                processed_data[field] = current_date
        
        # 添加模板信息
        processed_data['_template_name'] = os.path.basename(self.template_path)
        processed_data['_generation_time'] = current_date
    
    def generate_document(self, form_data: Dict[str, Any], output_dir: str = None) -> str:
        """
        基于表单数据生成Word文档，保持原始结构

        Args:
            form_data (dict): 表单数据
            output_dir (str): 输出目录

        Returns:
            str: 生成的文档路径
        """
        try:
            # 处理表单数据
            processed_data = self.process_form_data(form_data)

            # 使用结构化文档生成器
            structured_generator = StructuredDocumentGenerator(self.template_path)

            # 生成输出路径
            if output_dir:
                template_name = os.path.basename(self.template_path)
                output_name = template_name.replace('.docx', '_generated.docx')
                output_path = os.path.join(output_dir, output_name)
            else:
                output_path = None

            # 生成文档
            output_path = structured_generator.generate_document(processed_data, output_path)

            if output_path:
                print(f"结构化文档生成成功: {output_path}")
                return output_path
            else:
                raise Exception("文档生成失败")

        except Exception as e:
            logger.error(f"生成文档时出错: {str(e)}")
            raise
    
    def generate_preview_html(self, form_data: Dict[str, Any]) -> str:
        """
        生成文档预览HTML，保持Word文档结构

        Args:
            form_data (dict): 表单数据

        Returns:
            str: 预览HTML内容
        """
        try:
            # 处理表单数据
            processed_data = self.process_form_data(form_data)

            # 使用结构化文档生成器生成预览
            structured_generator = StructuredDocumentGenerator(self.template_path)
            preview_html = structured_generator.generate_preview_html(processed_data)

            return preview_html

        except Exception as e:
            logger.error(f"生成预览时出错: {str(e)}")
            return f'<div class="error">预览生成失败: {str(e)}</div>'
    
    def validate_form_data(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证表单数据
        
        Args:
            form_data (dict): 表单数据
            
        Returns:
            dict: 验证结果 {'valid': bool, 'errors': list}
        """
        errors = []
        
        # 检查必填字段
        for section in self.form_config['form_sections']:
            for field in section['fields']:
                if field.get('required', False):
                    field_name = field['name']
                    if field_name not in form_data or not form_data[field_name]:
                        errors.append(f"必填字段 '{field['label']}' 不能为空")
        
        # 检查数据类型
        for section in self.form_config['form_sections']:
            for field in section['fields']:
                field_name = field['name']
                field_type = field['type']
                
                if field_name in form_data and form_data[field_name]:
                    value = form_data[field_name]
                    
                    if field_type == 'number':
                        try:
                            float(value)
                        except (ValueError, TypeError):
                            errors.append(f"字段 '{field['label']}' 必须是数字")
                    
                    elif field_type == 'email':
                        import re
                        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                        if not re.match(email_pattern, str(value)):
                            errors.append(f"字段 '{field['label']}' 必须是有效的邮箱地址")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def get_form_statistics(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取表单填写统计信息
        
        Args:
            form_data (dict): 表单数据
            
        Returns:
            dict: 统计信息
        """
        total_fields = len(self.analyzer.placeholders)
        filled_fields = sum(1 for placeholder in self.analyzer.placeholders 
                          if placeholder in form_data and form_data[placeholder])
        
        total_checkboxes = len(self.analyzer.checkboxes)
        selected_checkboxes = sum(1 for i in range(total_checkboxes)
                                if form_data.get(f'checkbox_{i}', False))
        
        return {
            'total_fields': total_fields,
            'filled_fields': filled_fields,
            'fill_rate': round(filled_fields / total_fields * 100, 1) if total_fields > 0 else 0,
            'total_checkboxes': total_checkboxes,
            'selected_checkboxes': selected_checkboxes,
            'selection_rate': round(selected_checkboxes / total_checkboxes * 100, 1) if total_checkboxes > 0 else 0
        }
