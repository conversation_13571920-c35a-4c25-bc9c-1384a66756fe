"""
当事人信息处理器
"""

import json
import logging
import os
import re
import time
import requests
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class PartyInfoProcessor:
    """当事人信息处理器，用于从文本中提取和验证各类当事人信息"""

    def __init__(self, api_url: str, api_key: str, model: str, debug: bool = False):
        """
        初始化当事人信息处理器

        Args:
            api_url: LLM API 地址
            api_key: LLM API 密钥
            model: 使用的模型名称
            debug: 是否启用调试模式
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model = model
        self.debug = debug

        # 确保API URL以/结尾
        if not self.api_url.endswith('/'):
            self.api_url += '/'

        # 配置日志
        self.logger = logging.getLogger(__name__)

        # 从环境变量或Flask配置中获取日志级别
        try:
            from flask import current_app
            log_level_str = current_app.config.get('LOG_LEVEL', 'INFO')
        except:
            # 如果不在Flask环境中，从环境变量获取
            import os
            log_level_str = os.environ.get('LOG_LEVEL', 'DEBUG' if debug else 'INFO')

        # 转换日志级别字符串为logging常量
        log_level = getattr(logging, log_level_str.upper(), logging.INFO)

        # 设置日志级别
        self.logger.setLevel(log_level)

        # 如果还没有配置过basicConfig，则配置它
        if not logging.getLogger().handlers:
            logging.basicConfig(
                level=log_level,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )

        self.logger.info(f"初始化当事人信息处理器，使用API: {self.api_url}")

    def _remove_thinking_tags(self, content):
        """
        移除LLM响应中的thinking标签

        Args:
            content (str): LLM响应内容

        Returns:
            str: 移除thinking标签后的内容
        """
        try:
            # 检查是否需要保留thinking标签
            try:
                from flask import current_app
                include_thinking = current_app.config.get('INCLUDE_THINKING_TAG', False)
            except:
                # 如果不在Flask环境中，默认为False
                include_thinking = False

            # 如果配置为保留thinking标签，直接返回原内容
            if include_thinking:
                return content

            # 使用正则表达式移除thinking标签及其内容
            # 匹配 <thinking>...</thinking> 和 <think>...</think> 标签（支持多行）
            cleaned_content = re.sub(r'<think(?:ing)?>.*?</think(?:ing)?>', '', content, flags=re.DOTALL | re.IGNORECASE)

            # 清理可能留下的多余空白行
            cleaned_content = re.sub(r'\n\s*\n', '\n', cleaned_content)
            cleaned_content = cleaned_content.strip()

            # 如果内容被清理了，记录日志
            if cleaned_content != content:
                self.logger.info("已移除LLM响应中的thinking标签")

            return cleaned_content

        except Exception as e:
            self.logger.warning(f"移除thinking标签时出错: {e}")
            return content

    def _analyze_template_party_requirements(self, template_path: str) -> List[str]:
        """
        分析模板中需要的当事人类型

        Args:
            template_path: 模板文件路径

        Returns:
            需要提取的当事人类型列表
        """
        if not template_path or not os.path.exists(template_path):
            self.logger.warning(f"模板文件不存在: {template_path}")
            return None

        try:
            from docx import Document

            # 加载模板文档
            doc = Document(template_path)

            # 定义所有可能的当事人插入点标记
            party_markers = [
                ('原告（自然人）插入点', '原告（自然人）列表'),
                ('原告（法人、非法人组织）插入点', '原告（法人、非法人组织）列表'),
                ('被告（自然人）插入点', '被告（自然人）列表'),
                ('被告（法人、非法人组织）插入点', '被告（法人、非法人组织）列表'),
                ('第三人（自然人）插入点', '第三人（自然人）列表'),
                ('第三人（法人、非法人组织）插入点', '第三人（法人、非法人组织）列表')
            ]

            required_types = []

            # 检查文档段落中的插入点标记
            for para in doc.paragraphs:
                para_text = para.text
                for marker, party_type in party_markers:
                    if f"{{{{{marker}}}}}" in para_text:
                        required_types.append(party_type)
                        self.logger.debug(f"在段落中找到插入点: {marker}")

            # 检查文档表格中的插入点标记
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for para in cell.paragraphs:
                            para_text = para.text
                            for marker, party_type in party_markers:
                                if f"{{{{{marker}}}}}" in para_text:
                                    required_types.append(party_type)
                                    self.logger.debug(f"在表格中找到插入点: {marker}")

            # 去重并返回
            required_types = list(set(required_types))
            self.logger.info(f"模板分析完成，需要的当事人类型: {required_types}")
            return required_types

        except Exception as e:
            self.logger.error(f"分析模板当事人需求时出错: {str(e)}")
            return None

    def extract_party_info(self, text: str, template_path: str = None) -> Dict[str, Any]:
        """
        从文本中提取当事人信息

        Args:
            text: 包含当事人信息的文本
            template_path: 模板文件路径，用于分析需要提取哪些类型的当事人

        Returns:
            包含各类当事人信息的字典，格式为:
            {
                "原告（自然人）列表": [{"姓名": "张三", "性别": "男", ...}, ...],
                "原告（法人、非法人组织）列表": [...],
                "被告（自然人）列表": [...],
                "被告（法人、非法人组织）列表": [...],
                "第三人（自然人）列表": [...],
                "第三人（法人、非法人组织）列表": [...]
            }
        """
        self.logger.info("开始提取当事人信息...")

        # 如果提供了模板路径，先分析模板中需要的当事人类型
        required_party_types = self._analyze_template_party_requirements(template_path) if template_path else None

        if required_party_types:
            self.logger.info(f"根据模板分析，需要提取的当事人类型: {required_party_types}")
        else:
            self.logger.info("未提供模板路径或分析失败，将提取所有类型的当事人")

        # 从专用的提示词模块中导入分离的提示词函数
        from llm.prompts.party_extraction_prompt import get_party_extraction_system_prompt, get_party_extraction_user_prompt
        system_prompt = get_party_extraction_system_prompt(required_party_types)
        user_prompt = get_party_extraction_user_prompt(text)

        try:
            # 调用LLM API
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # 从Flask配置或环境变量中获取LLM参数
            try:
                from flask import current_app
                temperature = current_app.config.get('LLM_TEMPERATURE', 0.1)
                max_tokens = current_app.config.get('LLM_MAX_TOKENS', 4000)
                top_p = current_app.config.get('LLM_TOP_P', 0.9)
            except:
                # 如果不在Flask环境中，使用默认值
                temperature = 0.1
                max_tokens = 4000
                top_p = 0.9

            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": temperature,
                "max_tokens": max_tokens,
                "top_p": top_p,
                "stream": False
            }

            # 如果启用了阿里云思维链功能，添加enable_thinking字段
            try:
                from flask import current_app
                enable_ali_thinking = current_app.config.get('LLM_ENABLE_ALI_THINKING', False)
                if enable_ali_thinking:
                    data["enable_thinking"] = True
            except:
                # 如果不在Flask环境中，从环境变量获取
                import os
                enable_ali_thinking = os.environ.get('LLM_ENABLE_ALI_THINKING', 'false').lower() == 'true'
                if enable_ali_thinking:
                    data["enable_thinking"] = True

            # 打印LLM API调用信息
            api_url = f"{self.api_url}chat/completions"
            self.logger.info(f"调用LLM API: {api_url}")
            self.logger.info(f"LLM API参数: model={self.model}, temperature={temperature}, max_tokens={max_tokens}, top_p={top_p}")

            # 打印完整的请求体
            print("\n" + "="*50)
            print("Party Processor API 请求体:")
            print("="*50)
            print(f"URL: {api_url}")
            print(f"Headers: {headers}")
            print(f"Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
            print("="*50 + "\n")

            start_time = time.time()
            response = requests.post(api_url, headers=headers, json=data, timeout=120)
            end_time = time.time()

            self.logger.debug(f"API调用耗时: {end_time - start_time:.2f}秒")

            if response.status_code != 200:
                self.logger.error(f"API调用失败: {response.status_code} - {response.text}")
                return self._get_empty_party_info()

            # 解析响应
            response_data = response.json()

            if 'choices' not in response_data or len(response_data['choices']) == 0:
                self.logger.error("API响应中未找到choices字段")
                return self._get_empty_party_info()

            content = response_data['choices'][0]['message']['content']

            # 移除thinking标签
            content = self._remove_thinking_tags(content)

            # 从内容中提取JSON
            try:
                # 去除可能的markdown代码块标记
                if "```json" in content:
                    content = content.split("```json")[1].split("```")[0].strip()
                elif "```" in content:
                    content = content.split("```")[1].split("```")[0].strip()

                party_info = json.loads(content)
                self.logger.info("成功从LLM响应中提取当事人信息")

                # 补全可能缺失的列表
                self._ensure_party_lists(party_info, required_party_types)

                # 记录当事人统计信息
                self._log_party_stats(party_info)

                # 调试输出
                if self.debug:
                    self.logger.debug(f"提取的当事人信息: {json.dumps(party_info, ensure_ascii=False, indent=2)}")

                return party_info

            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败: {e}")
                self.logger.debug(f"原始内容: {content}")
                return self._get_empty_party_info()

        except Exception as e:
            self.logger.error(f"提取当事人信息时发生错误: {str(e)}")
            return self._get_empty_party_info()

    def _ensure_party_lists(self, party_info: Dict[str, Any], required_party_types: List[str] = None) -> None:
        """
        确保party_info中包含必要的当事人列表

        Args:
            party_info: 当事人信息字典
            required_party_types: 需要确保的当事人类型列表，如果为None则确保所有类型
        """
        if required_party_types:
            # 只确保需要的当事人类型
            required_lists = required_party_types
        else:
            # 确保所有当事人类型
            required_lists = [
                "原告（自然人）列表",
                "原告（法人、非法人组织）列表",
                "被告（自然人）列表",
                "被告（法人、非法人组织）列表",
                "第三人（自然人）列表",
                "第三人（法人、非法人组织）列表"
            ]

        # 检查并添加缺失的列表
        for list_key in required_lists:
            if list_key not in party_info:
                party_info[list_key] = []

    def validate_party_info(self, party_info: Dict[str, Any]) -> bool:
        """
        验证提取的当事人信息是否有效

        Args:
            party_info: 当事人信息字典

        Returns:
            当事人信息是否有效
        """
        # 确保包含所有必要的列表（验证时需要检查所有类型）
        self._ensure_party_lists(party_info, None)

        # 检查原告或被告至少有一个不为空
        plaintiff_count = len(party_info.get("原告（自然人）列表", [])) + len(party_info.get("原告（法人、非法人组织）列表", []))
        defendant_count = len(party_info.get("被告（自然人）列表", [])) + len(party_info.get("被告（法人、非法人组织）列表", []))

        if plaintiff_count == 0 and defendant_count == 0:
            self.logger.error("原告和被告不能同时为空")
            return False

        # 验证每个当事人是否包含必要信息
        for list_key, field in [
            ("原告（自然人）列表", "姓名"),
            ("原告（法人、非法人组织）列表", "名称"),
            ("被告（自然人）列表", "姓名"),
            ("被告（法人、非法人组织）列表", "名称"),
            ("第三人（自然人）列表", "姓名"),
            ("第三人（法人、非法人组织）列表", "名称")
        ]:
            for party in party_info.get(list_key, []):
                if field not in party or not party[field]:
                    self.logger.error(f"{list_key}中存在没有{field}的当事人")
                    return False

        return True

    def _get_empty_party_info(self) -> Dict[str, List]:
        """
        获取空的当事人信息结构

        Returns:
            空的当事人信息字典
        """
        return {
            "原告（自然人）列表": [],
            "原告（法人、非法人组织）列表": [],
            "被告（自然人）列表": [],
            "被告（法人、非法人组织）列表": [],
            "第三人（自然人）列表": [],
            "第三人（法人、非法人组织）列表": [],
        }

    def log(self, message):
        """简单日志方法"""
        if self.debug:
            print(f"[PartyInfoProcessor] {message}")

    def _log_party_stats(self, party_info):
        """记录当事人统计信息"""
        plaintiff_natural_count = len(party_info.get("原告（自然人）列表", []))
        plaintiff_legal_count = len(party_info.get("原告（法人、非法人组织）列表", []))
        defendant_natural_count = len(party_info.get("被告（自然人）列表", []))
        defendant_legal_count = len(party_info.get("被告（法人、非法人组织）列表", []))
        third_party_natural_count = len(party_info.get("第三人（自然人）列表", []))
        third_party_legal_count = len(party_info.get("第三人（法人、非法人组织）列表", []))

        self.logger.info(f"提取到 {plaintiff_natural_count} 个原告(自然人), {plaintiff_legal_count} 个原告(法人)")
        self.logger.info(f"提取到 {defendant_natural_count} 个被告(自然人), {defendant_legal_count} 个被告(法人)")
        self.logger.info(f"提取到 {third_party_natural_count} 个第三人(自然人), {third_party_legal_count} 个第三人(法人)")

    def call_api(self, prompt):
        """
        调用LLM API

        Args:
            prompt (str): 提示词

        Returns:
            str: API响应内容
        """
        import requests

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        # 从Flask配置或环境变量中获取LLM参数
        try:
            from flask import current_app
            temperature = current_app.config.get('LLM_TEMPERATURE', 0.2)
            max_tokens = current_app.config.get('LLM_MAX_TOKENS', 4000)
            top_p = current_app.config.get('LLM_TOP_P', 0.9)
        except:
            # 如果不在Flask环境中，使用默认值
            temperature = 0.2
            max_tokens = 4000
            top_p = 0.9

        payload = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": temperature,
            "max_tokens": max_tokens,
            "top_p": top_p,
            "stream": False
        }

        # 如果启用了阿里云思维链功能，添加enable_thinking字段
        try:
            from flask import current_app
            enable_ali_thinking = current_app.config.get('LLM_ENABLE_ALI_THINKING', False)
            if enable_ali_thinking:
                payload["enable_thinking"] = True
        except:
            # 如果不在Flask环境中，从环境变量获取
            import os
            enable_ali_thinking = os.environ.get('LLM_ENABLE_ALI_THINKING', 'false').lower() == 'true'
            if enable_ali_thinking:
                payload["enable_thinking"] = True

        try:
            # 打印LLM API调用信息
            api_url = f"{self.api_url.rstrip('/')}/chat/completions"
            self.logger.info(f"调用LLM API: {api_url}")
            self.logger.info(f"LLM API参数: model={self.model}, temperature={temperature}, max_tokens={max_tokens}, top_p={top_p}")

            # 打印完整的请求体
            print("\n" + "="*50)
            print("Party Processor call_api 请求体:")
            print("="*50)
            print(f"URL: {api_url}")
            print(f"Headers: {headers}")
            print(f"Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
            print("="*50 + "\n")

            start_time = time.time()
            response = requests.post(api_url, headers=headers, json=payload)
            end_time = time.time()

            self.log(f"API响应时间: {end_time - start_time:.2f}秒")

            if response.status_code == 200:
                result = response.json()
                content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                # 移除thinking标签
                content = self._remove_thinking_tags(content)
                return content
            else:
                self.log(f"API调用失败: {response.status_code}, {response.text}")
                return None
        except Exception as e:
            self.log(f"API调用异常: {str(e)}")
            return None