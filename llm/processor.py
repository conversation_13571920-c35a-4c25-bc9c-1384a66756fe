import json
import logging
import re
import requests
from typing import Dict, Any, List, Optional, Union
from flask import current_app

logger = logging.getLogger(__name__)

class LLMProcessor:
    """大模型文本处理器"""

    def __init__(self, api_url, api_key, model="gpt-3.5-turbo", classification_model=None,
                 classification_api_url=None, classification_api_key=None):
        """
        初始化大模型处理器

        Args:
            api_url (str): API URL
            api_key (str): API密钥
            model (str): 主模型名称
            classification_model (str, optional): 分类模型名称，如果为None则使用主模型
            classification_api_url (str, optional): 分类LLM的API URL，如果为None则使用主API URL
            classification_api_key (str, optional): 分类LLM的API密钥，如果为None则使用主API密钥
        """
        self.api_url = api_url
        self.api_key = api_key
        self.model = model
        self.classification_model = classification_model or model  # 如果没有指定分类模型，则使用主模型
        self.classification_api_url = classification_api_url or api_url  # 如果没有指定分类API URL，则使用主API URL
        self.classification_api_key = classification_api_key or api_key  # 如果没有指定分类API密钥，则使用主API密钥

        # 确保API URL格式正确
        if self.api_url and not self.api_url.endswith("/"):
            self.api_url = f"{self.api_url}/"

        if self.classification_api_url and not self.classification_api_url.endswith("/"):
            self.classification_api_url = f"{self.classification_api_url}/"

        # 记录初始化信息
        logger.info(f"LLMProcessor初始化: API URL={self.api_url}, Model={self.model}")
        logger.info(f"分类配置: API URL={self.classification_api_url}, Model={self.classification_model}")

    def _remove_thinking_tags(self, content):
        """
        移除LLM响应中的thinking标签

        Args:
            content (str): LLM响应内容

        Returns:
            str: 移除thinking标签后的内容
        """
        try:
            # 检查是否需要保留thinking标签
            try:
                include_thinking = current_app.config.get('INCLUDE_THINKING_TAG', False)
            except:
                # 如果不在Flask环境中，默认为False
                include_thinking = False

            # 如果配置为保留thinking标签，直接返回原内容
            if include_thinking:
                return content

            # 使用正则表达式移除thinking标签及其内容
            # 匹配 <thinking>...</thinking> 和 <think>...</think> 标签（支持多行）
            cleaned_content = re.sub(r'<think(?:ing)?>.*?</think(?:ing)?>', '', content, flags=re.DOTALL | re.IGNORECASE)

            # 清理可能留下的多余空白行
            cleaned_content = re.sub(r'\n\s*\n', '\n', cleaned_content)
            cleaned_content = cleaned_content.strip()

            # 如果内容被清理了，记录日志
            if cleaned_content != content:
                logger.info("已移除LLM响应中的thinking标签")

            return cleaned_content

        except Exception as e:
            logger.warning(f"移除thinking标签时出错: {e}")
            return content

    def call_api(self, messages, temperature=None, max_tokens=None, top_p=None, use_classification_api=False):
        """
        调用LLM API并返回响应文本

        Args:
            messages: 消息列表，每个消息包含role和content
            temperature: 温度参数
            max_tokens: 最大生成token数
            top_p: top_p参数
            use_classification_api: 是否使用分类API URL

        Returns:
            LLM的响应文本

        Raises:
            Exception: 如果API调用失败
        """

        # 如果参数为None，从配置中获取默认值
        if temperature is None:
            try:
                temperature = current_app.config.get('LLM_TEMPERATURE', 0.5)
            except:
                temperature = 0.5

        if max_tokens is None:
            try:
                max_tokens = current_app.config.get('LLM_MAX_TOKENS', 16000)
            except:
                max_tokens = 16000

        if top_p is None:
            try:
                top_p = current_app.config.get('LLM_TOP_P', 0.8)
            except:
                top_p = 0.8

        try:
            # 根据是否使用分类API来选择URL和密钥
            api_url = self.classification_api_url if use_classification_api else self.api_url
            api_key = self.classification_api_key if use_classification_api else self.api_key
            model = self.classification_model if use_classification_api else self.model

            # 构造请求头
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }

            # 构造请求数据
            data = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "top_p": top_p,
                "stream": False
            }

            # 如果启用了阿里云思维链功能，添加enable_thinking字段
            try:
                enable_ali_thinking = current_app.config.get('LLM_ENABLE_ALI_THINKING', False)
                if enable_ali_thinking:
                    data["enable_thinking"] = True
            except:
                # 如果不在Flask环境中，从环境变量获取
                import os
                enable_ali_thinking = os.environ.get('LLM_ENABLE_ALI_THINKING', 'false').lower() == 'true'
                if enable_ali_thinking:
                    data["enable_thinking"] = True

            # 构造完整的API URL
            chat_completion_url = f"{api_url.rstrip('/')}/chat/completions"

            # 打印LLM API调用信息
            logger.info(f"调用LLM API: {chat_completion_url}")
            logger.info(f"LLM API参数: model={model}, temperature={temperature}, max_tokens={max_tokens}, top_p={top_p}")

            # 打印完整的请求体
            print("\n" + "="*50)
            print("LLM Processor API 请求体:")
            print("="*50)
            print(f"URL: {chat_completion_url}")
            print(f"Headers: {headers}")
            print(f"Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
            print("="*50 + "\n")

            # 发送请求
            response = requests.post(chat_completion_url, headers=headers, json=data, timeout=120)
            response.raise_for_status()

            # 解析响应
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"].strip()
                # 移除thinking标签
                content = self._remove_thinking_tags(content)
                return content
            else:
                logger.warning(f"LLM API返回的响应格式不正确: {result}")
                raise Exception("LLM API返回的响应格式不正确")

        except requests.exceptions.RequestException as e:
            logger.error(f"LLM API请求失败: {str(e)}")
            raise Exception(f"LLM API请求失败: {str(e)}")

    def process_checkbox(self, checkbox_context, case_data):
        """
        处理复选框，决定是否应该勾选

        Args:
            checkbox_context: 复选框上下文，包含前后文本
            case_data: 案件数据

        Returns:
            布尔值，表示复选框是否应该被勾选
        """
        try:
            # 提取复选框文本
            text_before = checkbox_context.get('text_before', '')
            text_after = checkbox_context.get('text_after', '')
            full_text = checkbox_context.get('full_text', '')

            # 获取案件类型
            case_type = case_data.get('case_type', '')

            # 构建提示
            prompt = f"""
你是一个法律文书处理助手，现在需要你帮助决定是否勾选文档中的复选框。

请根据以下信息，判断复选框是否应该被勾选：

案件类型：{case_type}
案件数据：{json.dumps(case_data, ensure_ascii=False)}

复选框：
- 前文本：{text_before}
- 后文本：{text_after}
- 完整文本：{full_text}

请分析上述信息，并给出应该勾选(true)还是不勾选(false)的判断。
你的回答应该是一个JSON格式的布尔值，例如 true 或 false。

注意：
1. 只有当你非常确定时才返回true，否则返回false
2. 考虑复选框的上下文和案件信息
3. 注意互斥选项（如"男/女"、"是/否"）不应同时勾选
4. 如果选项与案件类型无关，则不勾选

请只返回JSON布尔值，不要有其他解释。
            """

            # 打印请求内容
            logger.info("\n======== Checkbox请求内容 ========")
            logger.info(prompt)
            logger.info("==================================\n")

            # 调用API
            messages = [{"role": "user", "content": prompt}]
            response = self.call_api(messages=messages, temperature=0.1, max_tokens=100)

            # 打印LLM回复内容
            logger.info("\n======== Checkbox LLM回复内容 ========")
            logger.info(response)
            logger.info("======================================\n")

            # 解析响应
            try:
                # 尝试直接解析JSON
                result = json.loads(response)

                # 确保结果是布尔值
                if isinstance(result, bool):
                    return result

                # 如果不是布尔值，尝试转换
                if result in (0, 1, "true", "false"):
                    return bool(result)

                logger.warning(f"LLM返回的不是布尔值: {response}")
                logger.info(f"复选框文本: {full_text}")
                return False

            except json.JSONDecodeError:
                # 尝试从文本中提取布尔值
                response_lower = response.lower()
                if "true" in response_lower:
                    return True
                elif "false" in response_lower:
                    return False
                else:
                    logger.warning(f"无法从LLM响应中提取布尔值: {response}")
                    logger.info(f"复选框文本: {full_text}")
                    return False

        except Exception as e:
            logger.error(f"处理复选框时出错: {str(e)}")
            logger.info(f"复选框文本: {full_text if 'full_text' in locals() else checkbox_context}")
            return False

    def process_text(self, text, template_type=None, classification_mode="auto"):
        """
        处理文本并返回格式化后的结果

        Args:
            text (str): 原始文本
            template_type (str, optional): 手动选择的模板类型
            classification_mode (str, optional): 分类模式，"auto"或"manual"

        Returns:
            dict: 格式化后的文本，包含以下字段：
                - title: 起诉状标题
                - plaintiff: 原告信息
                - defendant: 被告信息
                - claims: 诉讼请求
                - facts: 事实与理由
                - evidence: 证据
                - date: 日期
                - template_type: 使用的模板类型
        """
        try:
            # 记录开始处理的时间
            logger.info(f"[开始处理文本] 分类模式: {classification_mode}, 模板类型: {template_type}")

            # 如果是手动模式，直接使用选择的模板类型
            if classification_mode == "manual" and template_type:
                logger.info(f"[手动模式] 使用指定的模板类型: {template_type}")

                # 从文件名中提取案件类型，例如从"2-离婚纠纷起诉状.docx"提取"离婚纠纷起诉状"
                import re
                import os

                # 提取文件名（不含路径）
                template_filename = os.path.basename(template_type)

                # 尝试从文件名中提取案件类型
                match = re.match(r'^\d+-(.+)\.docx$', template_filename)
                if match:
                    # 如果匹配成功，使用提取的案件类型
                    case_type = match.group(1)
                    selected_template_type = case_type
                else:
                    # 如果无法提取，使用原始模板类型（去除扩展名）
                    selected_template_type = os.path.splitext(template_filename)[0]
            else:
                # 如果是自动模式或没有选择模板，进行分类
                logger.info(f"[自动模式] 调用大模型进行分类...")
                selected_template_type = self.classify_case_type(text)
                logger.info(f"[自动模式] 分类结果: {selected_template_type}")

            # 根据模板类型构建提示词
            prompt = self.get_prompt_for_template(selected_template_type, text)

            logger.info(f"使用模板类型: {selected_template_type}")

            # 调用API
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # 从应用配置中获取LLM请求参数
            try:
                temperature = current_app.config['LLM_TEMPERATURE']
                max_tokens = current_app.config['LLM_MAX_TOKENS']
                top_p = current_app.config['LLM_TOP_P']
            except:
                # 如果不在Flask环境中，使用默认值
                temperature = 0.3
                max_tokens = 4000
                top_p = 0.9

            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": "你是一个法律文档处理专家，擅长分析和格式化法律文档。"},
                    {"role": "user", "content": prompt}
                ],
                "temperature": temperature,
                "max_tokens": max_tokens,
                "top_p": top_p,
                "stream": False
            }

            # 如果启用了阿里云思维链功能，添加enable_thinking字段
            try:
                enable_ali_thinking = current_app.config.get('LLM_ENABLE_ALI_THINKING', False)
                if enable_ali_thinking:
                    data["enable_thinking"] = True
            except:
                # 如果不在Flask环境中，从环境变量获取
                import os
                enable_ali_thinking = os.environ.get('LLM_ENABLE_ALI_THINKING', 'false').lower() == 'true'
                if enable_ali_thinking:
                    data["enable_thinking"] = True

            # 构造完整的API URL
            chat_completion_url = f"{self.api_url.rstrip('/')}/chat/completions"

            # 打印LLM API调用信息
            logger.info(f"调用LLM API: {chat_completion_url}")
            logger.info(f"LLM API参数: model={self.model}, temperature={temperature}, max_tokens={max_tokens}, top_p={top_p}")

            # 打印完整的请求体
            print("\n" + "="*50)
            print("LLM Processor process_text API 请求体:")
            print("="*50)
            print(f"URL: {chat_completion_url}")
            print(f"Headers: {headers}")
            print(f"Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
            print("="*50 + "\n")

            # 发送请求
            response = requests.post(chat_completion_url, headers=headers, json=data)

            # 打印响应状态和头信息
            logger.info(f"Response status: {response.status_code}")
            logger.info(f"Response headers: {response.headers}")

            # 检查响应状态
            response.raise_for_status()

            # 解析响应
            result = response.json()

            # 打印完整的 API 响应内容
            logger.info("\n======== LLM 返回的完整响应 ========")
            logger.info(json.dumps(result, ensure_ascii=False, indent=2))
            logger.info("======================================\n")

            # 处理不同的API响应格式
            try:
                # 尝试标准OpenAI格式
                if 'choices' in result and len(result['choices']) > 0:
                    if 'message' in result['choices'][0]:
                        content = result['choices'][0]['message']['content']
                    elif 'text' in result['choices'][0]:
                        content = result['choices'][0]['text']
                    else:
                        # 其他兼容格式
                        logger.warning("API响应格式不标准，尝试提取内容")
                        logger.info(f"API响应: {result}")
                        # 尝试其他可能的格式
                        if isinstance(result, dict):
                            # 尝试直接找到内容字段
                            for key in ['content', 'text', 'result', 'response', 'output']:
                                if key in result:
                                    content = result[key]
                                    break
                            else:
                                # 如果找不到标准字段，返回整个响应
                                return {"warning": "无法解析API响应格式", "api_response": result}
                        else:
                            content = str(result)
                else:
                    # 如果没有choices字段，可能是其他兼容API格式
                    logger.warning(f"API响应不包含choices字段: {result}")
                    if isinstance(result, dict):
                        for key in ['content', 'text', 'result', 'response', 'output']:
                            if key in result:
                                content = result[key]
                                break
                        else:
                            # 如果找不到标准字段，返回整个响应
                            return {"warning": "无法解析API响应格式", "api_response": result}
                    else:
                        content = str(result)

                # 移除thinking标签
                content = self._remove_thinking_tags(content)

                # 打印 LLM 返回的内容
                logger.info("\n======== LLM 返回的内容 ========")
                logger.info(content)
                logger.info("==================================\n")

                # 尝试解析JSON
                try:
                    # 如果内容是JSON字符串，解析它
                    formatted_text = json.loads(content)

                    # 打印解析后的 JSON 数据
                    logger.info("\n======== 解析后的 JSON 数据 ========")
                    logger.info(json.dumps(formatted_text, ensure_ascii=False, indent=2))
                    logger.info("====================================\n")

                    # 添加模板类型到返回结果中
                    if isinstance(formatted_text, dict) and 'template_type' not in formatted_text:
                        formatted_text['template_type'] = selected_template_type

                    # 添加内容分析用于增强复选框处理
                    if isinstance(formatted_text, dict):
                        self._enhance_content_for_checkboxes(formatted_text)

                    # 保留原始OCR文本，用于复选框处理
                    if isinstance(formatted_text, dict):
                        formatted_text['ocr_original_text'] = text

                    return formatted_text
                except json.JSONDecodeError:
                    # 如果内容不是JSON，尝试从文本中提取JSON部分
                    try:
                        # 尝试匹配JSON块
                        import re
                        json_match = re.search(r'\{[\s\S]*\}', content)
                        if json_match:
                            json_str = json_match.group(0)
                            formatted_text = json.loads(json_str)

                            # 打印从文本中提取的 JSON 数据
                            logger.info("\n======== 从文本中提取的 JSON 数据 ========")
                            logger.info(json.dumps(formatted_text, ensure_ascii=False, indent=2))
                            logger.info("=========================================\n")

                            # 添加模板类型到返回结果中
                            if isinstance(formatted_text, dict) and 'template_type' not in formatted_text:
                                formatted_text['template_type'] = selected_template_type

                            # 保留原始OCR文本，用于复选框处理
                            if isinstance(formatted_text, dict):
                                formatted_text['ocr_original_text'] = text

                            return formatted_text
                        else:
                            # 如果无法解析JSON，返回原始内容
                            return {"error": "无法解析响应为JSON", "raw_content": content}
                    except Exception as e:
                        return {"error": f"解析响应失败: {str(e)}", "raw_content": content}
            except Exception as e:
                return {"error": f"处理API响应时出错: {str(e)}", "api_response": str(result)}

        except Exception as e:
            logger.error(f"处理文本时出错: {e}")
            return {"error": str(e)}

    def classify_case_type(self, text):
        """
        使用LLM对文本进行案件类型分类，匹配到templates目录中的11个模板

        Args:
            text (str): 原始文本

        Returns:
            str: 案件类型，对应templates目录中的模板名称
        """
        try:
            # 从 Flask应用中获取案件类型列表
            from flask import current_app
            case_types = current_app.config['CASE_TYPES']

            logger.info(f"[分类] 可用的案件类型: {case_types}")
            logger.info(f"[分类] 使用模型: {self.classification_model}")
            logger.info(f"[分类] 使用API URL: {self.classification_api_url}")

            # 构建分类提示词，包含所有11个模板类型
            case_types_list = "\n".join([f"{i+1}. {case_type}" for i, case_type in enumerate(case_types)])
            prompt = f"""
            你是一个法律文档分类专家。请分析以下起诉状文本，判断这是哪种类型的法律案件。

            请从以下类型中选择最匹配的一种：
            {case_types_list}

            只输出最匹配的类型名称，不要包含编号或其他解释。
            请确保你的回答完全匹配上述列表中的一个选项。

            原始文本：
            {text}
            """

            logger.info(f"[分类] 提示词长度: {len(prompt)}")

            # 从应用配置中获取分类模型请求参数
            try:
                temperature = current_app.config['CLASSIFICATION_TEMPERATURE']
                max_tokens = current_app.config['CLASSIFICATION_MAX_TOKENS']
                top_p = current_app.config['CLASSIFICATION_TOP_P']
            except:
                # 如果不在Flask环境中，使用默认值
                temperature = 0.3
                max_tokens = 500
                top_p = 0.9

            # 使用call_api方法发送请求，并指定使用分类API URL
            messages = [
                {"role": "system", "content": "你是一个法律文档分类专家，擅长识别不同类型的法律文书。你的任务是将文本分类为11种特定类型之一。"},
                {"role": "user", "content": prompt}
            ]

            logger.info(f"[分类] 发送API请求...")
            response_content = self.call_api(
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p,
                use_classification_api=True  # 使用分类API URL
            )

            # 清理内容，移除可能的引号和空白
            content = response_content.strip().strip('\"\'')

            # 打印分类结果
            logger.info("\n======== 分类结果 ========")
            logger.info(content)
            logger.info("==============================\n")

            logger.info(f"[分类] 清理后的内容: {content}")

            # 从响应中精确匹配案件类型
            for case_type in case_types:
                if case_type.lower() == content.lower() or case_type.lower() in content.lower():
                    logger.info(f"分类结果: {case_type}")
                    return case_type

            # 关键词匹配逻辑 - 根据11个模板类型定义关键词
            keyword_mapping = {
                "民间借贷纠纷起诉状": ["民间借贷", "借款", "借贷", "贷款", "欠款", "债务", "民间", "借条"],
                "离婚纠纷起诉状": ["离婚", "婚姻", "夫妻", "家庭", "子女抚养", "财产分割"],
                "买卖合同纠纷起诉状": ["买卖", "购买", "销售", "商品", "货物", "交付"],
                "金融借款合同纠纷起诉状": ["金融借款", "银行贷款", "金融机构", "贷款合同"],
                "物业服务合同纠纷起诉状": ["物业", "物业服务", "物业费", "小区", "业主"],
                "银行信用卡纠纷起诉状": ["信用卡", "银行卡", "透支", "刷卡", "还款"],
                "机动车交通事故责任纠纷起诉状": ["交通事故", "机动车", "车祸", "肇事", "赔偿"],
                "劳动争议起诉状": ["劳动", "工作", "雇佣", "员工", "工资", "加班", "解雇", "辞职"],
                "融资租赁合同纠纷起诉状": ["融资租赁", "租赁", "租金", "租期", "融资"],
                "保证保险合同纠纷起诉状": ["保险", "保证", "保单", "理赔", "投保", "保费"],
                "证券虚假陈述责任纠纷起诉状": ["证券", "股票", "虚假陈述", "投资", "股东", "上市公司"]
            }

            # 计算每个类型的关键词匹配度
            match_scores = {}
            content_lower = content.lower()
            text_lower = text.lower()[:10000]  # 限制文本长度

            for case_type, keywords in keyword_mapping.items():
                # 先检查LLM返回的内容
                content_score = sum(1 for kw in keywords if kw in content_lower)
                # 再检查原始文本
                text_score = sum(1 for kw in keywords if kw in text_lower)
                # 综合得分，LLM返回内容的权重更高
                match_scores[case_type] = content_score * 2 + text_score

            # 找出得分最高的类型
            if match_scores:
                best_match = max(match_scores.items(), key=lambda x: x[1])
                if best_match[1] > 0:  # 至少有一个关键词匹配
                    logger.info(f"基于关键词匹配的最佳结果: {best_match[0]} (得分: {best_match[1]})")
                    return best_match[0]

            # 如果没有找到匹配的类型，返回默认类型（第一个模板）
            default_type = case_types[0] if case_types else "民间借贷纠纷起诉状"
            logger.info(f"未找到匹配的案件类型，使用默认类型: {default_type}")
            return default_type

        except Exception as e:
            logger.error(f"案件分类出错: {e}")
            # 出错时返回默认类型（第一个模板）
            try:
                from flask import current_app
                case_types = current_app.config['CASE_TYPES']
                default_type = case_types[0] if case_types else "民间借贷纠纷起诉状"
                return default_type
            except:
                return "民间借贷纠纷起诉状"  # 硬编码默认值作为后备

    def get_prompt_for_template(self, template_type, text):
        """
        根据模板类型获取适当的提示词

        Args:
            template_type (str): 模板类型
            text (str): 原始文本

        Returns:
            str: 提示词
        """
        # 获取模板文件路径和占位符
        template_file = None

        try:
            # 从Flask应用中获取模板映射
            from flask import current_app
            template_mapping = current_app.config.get('TEMPLATE_MAPPING', {})

            # 使用模板映射获取对应的模板文件
            if template_type in template_mapping:
                template_file = template_mapping[template_type]
                logger.info(f"从TEMPLATE_MAPPING中获取模板文件: {template_type} -> {template_file}")
            else:
                # 如果找不到映射，尝试一些常见的别名
                if template_type == "借贷纠纷起诉状":
                    template_file = template_mapping.get("民间借贷纠纷起诉状", "1-民间借贷纠纷起诉状.docx")
                elif template_type == "婚姻家庭纠纷起诉状":
                    template_file = template_mapping.get("离婚纠纷起诉状", "2-离婚纠纷起诉状.docx")
                else:
                    # 默认使用通用模板
                    template_file = "complaint_template.docx"
                    logger.info(f"未找到模板类型 {template_type} 的映射，使用默认模板: {template_file}")
        except Exception as e:
            logger.error(f"获取模板映射时出错: {e}")
            # 出错时使用硬编码的备选方案
            if template_type == "借贷纠纷起诉状":
                template_file = "1-民间借贷纠纷起诉状.docx"
            elif template_type == "婚姻家庭纠纷起诉状":
                template_file = "2-离婚纠纷起诉状.docx"
            else:
                template_file = "complaint_template.docx"

        # 获取模板的占位符
        placeholders_prompt = ""
        placeholders_list = []
        try:
            import os
            from document.generator import DocumentGenerator
            template_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static", "templates", template_file)
            if os.path.exists(template_path):
                doc_gen = DocumentGenerator(template_path)
                doc_gen.update_placeholders()
                template_name = os.path.basename(template_path)
                placeholders = doc_gen.placeholders.get(template_name, [])
                if placeholders:
                    placeholders_list = placeholders
                    placeholders_prompt = "\n模板中包含以下占位符字段，请确保在返回的JSON中将这些字段作为key包含：\n"
                    for field in placeholders:
                        placeholders_prompt += f"- \"{field}\"\n"
                    # 添加示例说明
                    placeholders_prompt += "\n返回的JSON格式应该直接使用这些占位符作为key，例如：\n"
                    placeholders_prompt += "{"
                    for i, field in enumerate(placeholders[:3]):
                        if i > 0:
                            placeholders_prompt += ", "
                        placeholders_prompt += f"\"{field}\": \"对应的值\""
                    if len(placeholders) > 3:
                        placeholders_prompt += ", ..."
                    placeholders_prompt += "}\n"
        except Exception as e:
            logger.error(f"获取模板占位符时出错: {e}")

        # 基础提示词
        base_prompt = f'''
        请分析以下起诉状文本，并提取关键信息，格式化为JSON格式

        还需要特别注意文档中的复选框处理，请在"诉讼请求"和"事实与理由"中详细描述相关内容，以便系统能够正确地自动勾选问题复选框。

        {placeholders_prompt}
        '''

        base_prompt += f'''
        原始文本：
        {text}
        '''

        base_prompt += '''
        请以JSON格式返回，不要包含任何其他解释或评论。

        请直接使用模板中的占位符字段作为JSON的key，而不是聚合字段（如"原告信息"、"被告信息"等）。
        对于每个占位符字段，请确保提供合适的值填充到文档中。

        【极其重要】：当文本中没有提供某个字段的相关信息时：
        1. 必须将该字段留空（空字符串""）
        2. 绝对不要填写"无"、"未提供"、"不详"或任何其他占位文本
        3. 空字段应该是这样的格式："字段名": ""
        4. 这将确保在生成的文档中，缺失的信息字段显示为空白

        错误示例："原告年龄": "无" 或 "原告年龄": "未提供"
        正确示例："原告年龄": ""

        【关于被告信息】：
        1. 如文本中出现多名被告（自然人），务必分别提取每名被告的信息，使用数字编号区分（"被告1姓名"、"被告2姓名"等）。
        2. 千万不要将多名被告的信息合并在同一字段中，例如不要用"王晓燕；赵静"这样的格式。
        3. 不要创建"被告姓名"这样的通用字段，必须为每个被告单独创建带编号的字段
        4. 不要将被告信息合并到"原告信息"字段中，而是应该分别提取每个被告的信息，并使用数字编号区分（"被告1姓名"、"被告2姓名"等）。
        5. 如果只有一个被告，生成的数组中只有一个被告信息，不要生成空数组

        【关于诉状其他请求】：
        1. 对于占位符中的"其他诉求"，把诉讼请求中未通过占位符提取的诉求，尽可能写在这里
        2.【交通事故赔偿】中，如果如果没有提供明确的的医疗费、护理费、营养师、住院伙食补助费、误工费、交通费、残疾赔偿金、残疾辅助器具费、死亡赔偿金、丧葬费、精神损害赔偿金等单项费用，则不需要填写对应的细项，在其他请求中统一描述即可

        '''
        return base_prompt

    def _enhance_content_for_checkboxes(self, data):
        """
        分析文本内容，添加关键词标记用于复选框处理
        基于语义理解自动选择模板中的复选框选项

        Args:
            data (dict): 格式化后的数据字典
        """
        if not isinstance(data, dict):
            return

        # 获取模板类型
        template_type = data.get('template_type', '')

        # 获取案件内容
        facts = data.get('事实与理由', data.get('facts', ''))
        claims = data.get('诉讼请求', data.get('claims', ''))
        title = data.get('标题', data.get('title', ''))
        plaintiff_info = data.get('原告信息', {})
        defendant_info = data.get('被告信息', {})

        # 转换为字符串以便于分析
        if isinstance(facts, list):
            facts = ' '.join(facts)
        if isinstance(claims, list):
            claims = ' '.join(claims)

        # 合并内容以便分析
        content = f"{title} {facts} {claims}".lower()

        # 初始化复选框关键词字典
        data['checkbox_keywords'] = {}

        # 通用案件类型检测（不依赖模板类型）
        self._detect_case_type(content, data['checkbox_keywords'])

        # 通用程序类型检测
        self._detect_procedure_type(content, data['checkbox_keywords'])

        # 根据模板类型添加关键词标记
        if template_type == '民间借贷纠纷起诉状' or template_type == '借贷纠纷起诉状' or '借贷' in template_type:
            # 分析借贷类型 - 添加模板中的关键词
            data['checkbox_keywords']['借贷纠纷'] = True  # 基本类型总是选中
            data['checkbox_keywords']['民间借贷'] = True  # 民间借贷选项

            # 借贷子类型检测
            self._detect_loan_subtype(content, data['checkbox_keywords'])

            # 借贷特征检测
            self._detect_loan_features(content, data['checkbox_keywords'])

            # 添加调试信息
            logger.info(f"借贷纠纷模板复选框关键词: {data['checkbox_keywords']}")

        elif template_type == '离婚纠纷起诉状' or template_type == '婚姻家庭纠纷起诉状':
            # 分析婚姻纠纷类型
            data['checkbox_keywords']['婚姻家庭'] = True  # 基本类型总是选中

            # 婚姻家庭子类型检测
            self._detect_marriage_subtype(content, data['checkbox_keywords'])

            # 婚姻特征检测
            self._detect_marriage_features(content, data['checkbox_keywords'])

        elif template_type == 'complaint_template' or template_type == '交通事故损害赔偿起诉状':
            # 分析交通事故类型
            data['checkbox_keywords']['交通事故'] = True  # 基本类型总是选中

            # 交通事故子类型检测
            self._detect_accident_subtype(content, data['checkbox_keywords'])

            # 交通事故特征检测
            self._detect_accident_features(content, data['checkbox_keywords'])

        # 当事人身份检测
        self._detect_party_identity(plaintiff_info, defendant_info, data['checkbox_keywords'])

    def _detect_case_type(self, content, checkbox_keywords):
        """检测案件类型"""
        # 民事案件类型
        if any(keyword in content for keyword in ['民事', '合同', '侵权', '损害赔偿', '财产']):
            checkbox_keywords['民事案件'] = True

        # 刑事案件类型
        if any(keyword in content for keyword in ['刑事', '犯罪', '刑法', '公诉', '自诉']):
            checkbox_keywords['刑事案件'] = True

        # 行政案件类型
        if any(keyword in content for keyword in ['行政', '政府', '机关', '行政处罚', '行政许可', '行政复议']):
            checkbox_keywords['行政案件'] = True

    def _detect_procedure_type(self, content, checkbox_keywords):
        """检测程序类型"""
        # 简易程序
        if any(keyword in content for keyword in ['简易程序', '小额诉讼']):
            checkbox_keywords['简易程序'] = True

        # 普通程序
        if any(keyword in content for keyword in ['普通程序', '一审']):
            checkbox_keywords['普通程序'] = True

        # 特别程序
        if any(keyword in content for keyword in ['特别程序', '非讼程序', '宣告失踪', '认定公民无民事行为能力']):
            checkbox_keywords['特别程序'] = True

    def _detect_loan_subtype(self, content, checkbox_keywords):
        """检测借贷纠纷子类型"""
        # 民间借贷
        if any(keyword in content for keyword in ['民间借贷', '个人借款', '私人借款', '亲友借款']):
            checkbox_keywords['民间借贷'] = True

        # 金融借款
        if any(keyword in content for keyword in ['金融借款', '银行贷款', '信用贷款', '抵押贷款', '信用卡', '透支']):
            checkbox_keywords['金融借款'] = True

        # 小额贷款
        if any(keyword in content for keyword in ['小额贷款', '小额信贷', '网络贷款', '网贷', '消费贷']):
            checkbox_keywords['小额贷款'] = True

        # 如果没有检测到任何子类型，默认为民间借贷
        if not any(key in checkbox_keywords for key in ['民间借贷', '金融借款', '小额贷款']):
            checkbox_keywords['民间借贷'] = True

    def _detect_loan_features(self, content, checkbox_keywords):
        """检测借贷特征"""
        # 利息争议
        if any(keyword in content for keyword in ['利息', '利率', '高利贷', '超过法定利率', '复利']):
            checkbox_keywords['利息争议'] = True
            checkbox_keywords['利息'] = True  # 添加简化的关键词，用于匹配模板中的复选框

        # 担保
        if any(keyword in content for keyword in ['担保', '保证', '抵押', '质押', '保证人', '抵押物']):
            checkbox_keywords['担保'] = True

        # 借条
        if any(keyword in content for keyword in ['借条', '借据', '欠条', '借款凭证']):
            checkbox_keywords['借条'] = True

        # 添加额外的借贷相关关键词，以便更好地匹配模板中的复选框
        if any(keyword in content for keyword in ['本金', '借款', '贷款', '欠款']):
            checkbox_keywords['本金'] = True

        # 如果内容中提到了还款或者欠款，添加相关标记
        if any(keyword in content for keyword in ['还款', '偿还', '归还', '未还', '欠款']):
            checkbox_keywords['欠款'] = True

        # 打印检测到的特征
        relevant_features = [k for k, v in checkbox_keywords.items() if v and k in ['利息争议', '利息', '担保', '借条', '本金', '欠款']]
        logger.info(f"检测到的借贷特征: {relevant_features}")

    def _detect_marriage_subtype(self, content, checkbox_keywords):
        """检测婚姻家庭纠纷子类型"""
        # 离婚纠纷
        if any(keyword in content for keyword in ['离婚', '感情破裂', '分居', '婚姻关系']):
            checkbox_keywords['离婚纠纷'] = True

        # 子女抚养
        if any(keyword in content for keyword in ['子女', '抚养', '抚养费', '监护权', '探视权']):
            checkbox_keywords['子女抚养'] = True

        # 财产分割
        if any(keyword in content for keyword in ['财产分割', '夫妻共同财产', '婚前财产', '婚后财产']):
            checkbox_keywords['财产分割'] = True

        # 如果没有检测到任何子类型，默认为离婚纠纷
        if not any(key in checkbox_keywords for key in ['离婚纠纷', '子女抚养', '财产分割']):
            checkbox_keywords['离婚纠纷'] = True

    def _detect_marriage_features(self, content, checkbox_keywords):
        """检测婚姻特征"""
        # 家庭暴力
        if any(keyword in content for keyword in ['家庭暴力', '家暴', '殴打', '伤害', '虐待']):
            checkbox_keywords['家庭暴力'] = True

        # 重婚
        if any(keyword in content for keyword in ['重婚', '婚外情', '出轨', '第三者']):
            checkbox_keywords['重婚'] = True

    def _detect_accident_subtype(self, content, checkbox_keywords):
        """检测交通事故子类型"""
        # 人身损害
        if any(keyword in content for keyword in ['人身', '伤害', '伤亡', '医疗', '死亡', '残疾', '伤残', '住院']):
            checkbox_keywords['人身损害'] = True

        # 财产损失
        if any(keyword in content for keyword in ['财产', '车辆损失', '物品损失', '修理费', '车辆', '维修']):
            checkbox_keywords['财产损失'] = True

        # 如果没有检测到任何子类型，默认为人身损害
        if not any(key in checkbox_keywords for key in ['人身损害', '财产损失']):
            checkbox_keywords['人身损害'] = True

    def _detect_accident_features(self, content, checkbox_keywords):
        """检测交通事故特征"""
        # 酒驾
        if any(keyword in content for keyword in ['酒驾', '酒后驾驶', '醉酒', '酒精']):
            checkbox_keywords['酒驾'] = True

        # 超速
        if any(keyword in content for keyword in ['超速', '超速行驶', '速度过快']):
            checkbox_keywords['超速'] = True

        # 闯红灯
        if any(keyword in content for keyword in ['闯红灯', '闯信号灯', '信号灯']):
            checkbox_keywords['闯红灯'] = True

    def _detect_party_identity(self, plaintiff_info, defendant_info, checkbox_keywords):
        """检测当事人身份"""
        # 将当事人信息转换为字符串
        plaintiff_str = str(plaintiff_info).lower()
        defendant_str = str(defendant_info).lower()

        # 检测自然人
        if any(keyword in plaintiff_str for keyword in ['身份证', '居民', '公民']):
            checkbox_keywords['原告自然人'] = True
        if any(keyword in defendant_str for keyword in ['身份证', '居民', '公民']):
            checkbox_keywords['被告自然人'] = True

        # 检测法人
        if any(keyword in plaintiff_str for keyword in ['公司', '企业', '法人', '单位', '组织', '营业执照']):
            checkbox_keywords['原告法人'] = True
        if any(keyword in defendant_str for keyword in ['公司', '企业', '法人', '单位', '组织', '营业执照']):
            checkbox_keywords['被告法人'] = True

    def process_text_with_placeholders(self, original_text, template_type, classification_mode, remaining_placeholders, intermediate_doc_text):
        """
        基于中间文档和剩余占位符处理文本

        Args:
            original_text (str): 原始OCR文本
            template_type (str): 模板类型
            classification_mode (str): 分类模式
            remaining_placeholders (list): 剩余占位符列表
            intermediate_doc_text (str): 中间文档文本内容

        Returns:
            dict: 格式化后的数据
        """
        logger.info("开始基于中间文档处理占位符...")

        try:
            logger.info(f"使用模板类型: {template_type}, 剩余占位符数量: {len(remaining_placeholders)}")

            # 调用API
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            # 从应用配置中获取LLM请求参数
            try:
                temperature = current_app.config['LLM_TEMPERATURE']
                max_tokens = current_app.config['LLM_MAX_TOKENS']
                top_p = current_app.config['LLM_TOP_P']
            except:
                # 如果不在Flask环境中，使用默认值
                temperature = 0.3
                max_tokens = 4000
                top_p = 0.9

            # 构建系统提示词（包含OCR原文和规则）
            system_prompt = f'''你是一个法律文档处理专家，擅长从起诉状文本中提取信息填充占位符。

## 原始起诉状文本：
{original_text}

## 重要规则：
1. 当原始文本中没有提供某个字段的相关信息时，必须将该字段留空（空字符串""）
2. 绝对不要填写"无"、"未提供"、"不详"或任何其他等不确定信息的文本
3. 空字段格式："字段名": ""
4. 对于占位符中的"其他诉求"，把诉讼请求中未通过占位符提取的诉求，尽可能写在这里
5. 请以JSON格式返回，只包含需要填充的占位符字段，不要包含任何其他解释或评论
6. 涉及到金额的选项只要暴露数字即可，单位（元）已经在原文模板中存在了

## 特殊说明

【标的金额】诉状中标的金额的含义是案件内涉及到的金额总和，比如借款相关案件中就是本金+利息+违约金+损害赔偿金等等所有涉及到的金额总和
【交通事故赔偿】中，如果如果没有提供明确的的医疗费、护理费、营养师、住院伙食补助费、误工费、交通费、残疾赔偿金、残疾辅助器具费、死亡赔偿金、丧葬费、精神损害赔偿金等单项费用，则不需要填写对应的细项，在其他请求中统一描述即可


错误示例："原告年龄": "无" 或 "原告年龄": "未提供"
正确示例："原告年龄": ""'''

            # 构建用户提示词（只包含需要处理的占位符列表）
            user_prompt = self._build_simple_placeholder_prompt(remaining_placeholders)

            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": temperature,
                "max_tokens": max_tokens,
                "top_p": top_p,
                "stream": False
            }

            # 如果启用了阿里云思维链功能，添加enable_thinking字段
            try:
                enable_ali_thinking = current_app.config.get('LLM_ENABLE_ALI_THINKING', False)
                if enable_ali_thinking:
                    data["enable_thinking"] = True
            except:
                # 如果不在Flask环境中，从环境变量获取
                import os
                enable_ali_thinking = os.environ.get('LLM_ENABLE_ALI_THINKING', 'false').lower() == 'true'
                if enable_ali_thinking:
                    data["enable_thinking"] = True

            # 构造完整的API URL
            chat_completion_url = f"{self.api_url.rstrip('/')}/chat/completions"

            # 打印LLM API调用信息
            logger.info(f"调用占位符分析LLM API: {chat_completion_url}")
            logger.info(f"LLM API参数: model={self.model}, temperature={temperature}, max_tokens={max_tokens}, top_p={top_p}")

            # 打印完整的请求体
            print("\n" + "="*50)
            print("占位符分析 LLM API 请求体:")
            print("="*50)
            print(f"URL: {chat_completion_url}")
            print(f"Headers: {headers}")
            print(f"Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
            print("="*50 + "\n")

            # 发送请求
            import time
            start_time = time.time()
            response = requests.post(chat_completion_url, headers=headers, json=data, timeout=120)
            end_time = time.time()

            logger.info(f"占位符分析API调用耗时: {end_time - start_time:.2f}秒")

            if response.status_code == 200:
                response_data = response.json()
                content = response_data['choices'][0]['message']['content']

                # 打印完整的响应内容
                print("\n" + "="*50)
                print("占位符分析 LLM API 响应内容:")
                print("="*50)
                print(f"状态码: {response.status_code}")
                print(f"响应大小: {len(content)} 字符")
                print("原始响应内容:")
                print("-" * 30)
                print(content[:2000] + ("..." if len(content) > 2000 else ""))
                print("-" * 30)
                print("="*50 + "\n")

                # 移除thinking标签
                content = self._remove_thinking_tags(content)

                # 打印处理后的内容
                print("\n" + "="*30)
                print("移除thinking标签后的内容:")
                print("="*30)
                print(content[:1000] + ("..." if len(content) > 1000 else ""))
                print("="*30 + "\n")

                # 解析JSON响应
                try:
                    # 去除可能的markdown代码块标记
                    if "```json" in content:
                        content = content.split("```json")[1].split("```")[0].strip()
                    elif "```" in content:
                        content = content.split("```")[1].split("```")[0].strip()

                    result = json.loads(content)

                    # 打印解析后的结果
                    print("\n" + "="*30)
                    print("解析后的JSON结果:")
                    print("="*30)
                    print(json.dumps(result, ensure_ascii=False, indent=2)[:1500] + ("..." if len(str(result)) > 1500 else ""))
                    print("="*30 + "\n")

                    # 添加模板类型信息
                    result['template_type'] = template_type

                    # 增强内容以便复选框处理
                    self._enhance_content_for_checkboxes(result)

                    logger.info("基于中间文档的占位符处理完成")
                    return result

                except json.JSONDecodeError as e:
                    logger.error(f"解析LLM响应JSON失败: {e}")
                    logger.error(f"响应内容: {content}")
                    return {"error": "解析响应失败", "template_type": template_type}
            else:
                logger.error(f"LLM API调用失败: {response.status_code} - {response.text}")
                return {"error": "API调用失败", "template_type": template_type}

        except Exception as e:
            logger.error(f"处理占位符时出错: {str(e)}")
            return {"error": str(e), "template_type": template_type}

    def _get_placeholder_prompt_with_context(self, original_text, template_type, remaining_placeholders, intermediate_doc_text):
        """
        构建基于中间文档上下文的占位符分析提示词

        Args:
            original_text (str): 原始OCR文本
            template_type (str): 模板类型
            remaining_placeholders (list): 剩余占位符列表
            intermediate_doc_text (str): 中间文档文本内容

        Returns:
            str: 构建的提示词
        """
        # 构建占位符提示
        placeholders_prompt = ""
        if remaining_placeholders:
            placeholders_prompt = "\n需要填充的剩余占位符字段：\n"
            for field in remaining_placeholders:
                placeholders_prompt += f"- \"{field}\"\n"

            # 添加示例说明
            placeholders_prompt += "\n返回的JSON格式应该直接使用这些占位符作为key，例如：\n"
            placeholders_prompt += "{"
            for i, field in enumerate(remaining_placeholders[:3]):
                if i > 0:
                    placeholders_prompt += ", "
                placeholders_prompt += f"\"对应的值\""
            if len(remaining_placeholders) > 3:
                placeholders_prompt += ", ..."
            placeholders_prompt += "}\n"

        # 构建完整提示词
        prompt = f'''
请基于以下信息分析并填充文档中的剩余占位符：

## 原始起诉状文本
{original_text}

## 当前文档状态（已填充当事人信息）
{intermediate_doc_text}

## 模板类型
{template_type}

{placeholders_prompt}

## 分析要求
1. 请仔细对比原始文本和当前文档状态，理解已经填充的当事人信息
2. 基于原始文本中的信息，填充剩余的占位符
3. 充分利用当前文档的上下文信息，确保填充内容与已有信息一致
4. 对于复选框处理，请在"诉讼请求"和"事实与理由"中详细描述相关内容

## 重要规则
【极其重要】：当原始文本中没有提供某个字段的相关信息时：
1. 必须将该字段留空（空字符串""）
2. 绝对不要填写"无"、"未提供"、"不详"或任何其他占位文本
3. 空字段应该是这样的格式："字段名": ""
4. 这将确保在生成的文档中，缺失的信息字段显示为空白

错误示例："原告年龄": "无" 或 "原告年龄": "未提供"
正确示例："原告年龄": ""

## 输出格式
请以JSON格式返回，只包含需要填充的占位符字段，不要包含任何其他解释或评论。
'''

        return prompt

    def _build_simple_placeholder_prompt(self, remaining_placeholders):
        """
        构建简化的占位符提示词

        Args:
            remaining_placeholders (list): 剩余占位符列表

        Returns:
            str: 简化的提示词
        """
        if not remaining_placeholders:
            return "请分析原始文本，提取所有可用信息。"

        prompt = "请从原始文本中提取以下占位符的信息：\n\n"
        for field in remaining_placeholders:
            prompt += f"- {field}\n"

        prompt += f"\n请以JSON格式返回，使用这些占位符作为key。示例格式：\n"
        prompt += "{\n"
        for i,field in enumerate(remaining_placeholders[:3]):
            prompt += f'  "{field}": "对应的值"\n'
        prompt += "}"

        return prompt
