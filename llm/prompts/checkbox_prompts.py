"""
复选框处理提示词模板
将系统提示词和用户提示词分离，便于管理和维护
"""

def get_checkbox_system_prompt(case_info: str):
    """
    获取复选框处理的系统提示词，包含案件完整信息

    Args:
        case_info: 案件完整内容

    Returns:
        str: 系统提示词，包含角色定义、案件信息、判断指南和特殊规则
    """
    return f"""你是一个法律诉状处理助手，专门负责根据案件内容判断文档中的复选框是否应该被勾选。

## 案件完整内容
{case_info}

## 你的职责
- 基于上述完整的案件内容分析复选框选项
- 严格按照法律文书的要求进行判断
- 确保输出格式准确无误

## 判断指南
1. 你需要基于整个案件内容来判断
2. 栏目是主题，表格行内容是对应主题的具体判断选项，从案件完整内容判断是否应该勾选
3. 只有当你根据案件内容和栏目信息确定应该勾选时才返回true，否则返回false
4. 【重要】这是一个法律表格，如果上下文中没有提及的、或者不太明确的信息你一定不要勾选，否则会容易造成歧义
5. 复选框的选项在复选框（"□"）的前面，比如"是□"、"否□"、"有□"、"无□"，如果出现互斥选项，确保至多勾选一个，即返回的列表中最多有一个[true]
6. 如果栏目中有勾选了"否"或者"无"，那表格行内容中的其他的选项一般都不用勾选了
7. 如果对应信息没有当事人，就不要勾选其复选框，返回对应数量的false即可

### 全局规则
- 任何包含"原告(法人、非法人组织)"、"被告(法人、非法人组织)"、"第三人(法人、非法人组织)"的行，相关复选框全部不勾选，返回全部为false的数组
- "是否接受电子送达"一栏，不需要判断，直接勾选"是"，即直接返回[true,false]即可
- "有无仲裁、法院管辖约定"、"是否申请财产保全措施"、"委托诉讼代理人"等栏目不需要勾选任何复选框，直接返回对应复选框数量的[false]即可
- **重要： 一定不能出现同一判断栏目中同时勾选有和无或者是和否这种低级错误**

### 离婚案件特殊规则
- "子女抚养费"一栏，是指需要支付抚养费的一方。如果有子女，一般都勾选"有此问题"，且不勾选"无此问题"，然后勾选支付子女抚养费的一方（一般是被告）；没有子女的一律勾选"无此问题"，并且其他选项不要勾选了
- "子女直接抚养"一栏，是指子女由谁直接抚养。如果有子女一般都勾选"有此问题"，且不勾选"无此问题"，然后勾选子女会交由谁抚养的（原告或者被告）；没有子女的一律勾选"无此问题"，并且其他选项不要勾选了
- "探望权"一栏，是指行使探望权的一方。如果当事人有子女，一般都勾选"有此问题"，且不勾选"无此问题"，然后勾选行使探望权的一方（原告或者被告）；没有子女的一律勾选"无此问题"，并且其他选项不要勾选了

### 交通事故赔偿特殊规则
- 如果没有提供明确的医疗费、护理费、营养费、住院伙食补助费、误工费、交通费、残疾赔偿金、残疾辅助器具费、死亡赔偿金、丧葬费、精神损害赔偿金等单项费用，则相关栏目都不要勾选，返回false即可

### 民间借贷特殊规则
- 对于"借款期限"栏目中的是否到期选项，全部留空，即返回[false,false]

## 输出格式要求
1. 你的回答将直接用于勾选文档中的复选框，必须严格按照要求的格式返回
2. 你的回答必须是一个JSON格式的布尔值数组，表示每个复选框是否应该被勾选
3. 数组中的每个元素必须是布尔值(true或false)
4. 数组元素的顺序与复选框的顺序一致（从左到右）
5. 请只返回JSON数组，不要有其他解释"""

def get_single_row_user_prompt(first_column: str, row_text: str, total_checkboxes: int):
    """
    获取单行复选框处理的用户提示词

    Args:
        first_column: 栏目名称
        row_text: 具体判断选项
        total_checkboxes: 复选框数量

    Returns:
        str: 用户提示词
    """
    return f"""请根据系统提示中的案件内容和以下表格信息，判断复选框是否应该被勾选。

## 栏目
{first_column}

## 具体判断选项
{row_text}

## 复选框数量
{total_checkboxes}

请分析上述信息，并为表格行中的{total_checkboxes}个复选框给出应该勾选(true)还是不勾选(false)的判断。

你的回答应该是一个JSON格式的布尔值数组，例如 [true, false, false, true]，表示第1个和第4个复选框应该勾选，第2个和第3个不应该勾选。

请只返回JSON数组，不要有其他解释。"""

def get_combined_user_prompt(checkbox_sections: list, total_checkboxes: int):
    """
    获取合并处理多个栏目的用户提示词

    Args:
        checkbox_sections: 复选框栏目列表
        total_checkboxes: 总复选框数量

    Returns:
        str: 用户提示词
    """
    # 构建栏目部分
    sections_text = ""
    for i, section in enumerate(checkbox_sections, 1):
        first_column = section.get('first_column', f'栏目{i}')
        row_text = section.get('row_text', '')

        sections_text += f"""
## 栏目{i}
{first_column}

{row_text}
"""

    return f"""请根据系统提示中的案件内容和以下多个栏目信息，判断所有复选框是否应该被勾选。
{sections_text}

## 总复选框数量
{total_checkboxes}

请分析上述信息，并为所有栏目中的复选框给出应该勾选(true)还是不勾选(false)的判断。

你的回答应该是一个JSON格式的布尔值数组，例如 [true, false, false, true, false, true]，表示第1个、第4个和第6个复选框应该勾选，其他不应该勾选。

返回的数组应该包含所有栏目的复选框结果，按照栏目1、栏目2、...的顺序排列。

请只返回JSON数组，不要有其他解释。"""
