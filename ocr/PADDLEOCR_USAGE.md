# 使用PaddleOCR和自定义模型目录

本文档介绍如何使用PaddleOCR以及如何配置自定义模型目录。

## 准备工作

1. 确保已经安装了PaddlePaddle和PaddleOCR：

```bash
pip install paddlepaddle
pip install paddleocr
```

或者使用预设的安装脚本：

```bash
bash scripts/install_paddleocr.sh
```

2. 准备模型文件

OCR模型已经下载到`ocr/models`目录中，包括：

- `ch_PP-OCRv4_det_server_infer.tar`（文本检测模型）
- `ch_PP-OCRv4_rec_server_infer.tar`（文本识别模型）
- `ch_ppocr_mobile_v2.0_cls_infer.tar`（方向分类模型）

需要将这些tar文件解压到对应目录。可以使用以下命令：

```bash
python -m ocr.extract_models
```

这将自动解压模型文件到正确的目录结构中。

## 配置PaddleOCR模型目录

通过环境变量或应用程序配置，你可以指定PaddleOCR使用的模型目录：

### 方法1：使用环境变量（推荐）

编辑 `.env` 文件（如果没有，可以复制 `.env.example` 创建），添加或修改以下配置：

```
# PaddleOCR配置
OCR_ENGINE=paddleocr
PADDLE_USE_GPU=false
PADDLE_LANG=ch
PADDLE_USE_ANGLE_CLS=true
PADDLE_MODELS_BASE_DIR=/absolute/path/to/ocr/models
PADDLE_DET_MODEL_DIR=/absolute/path/to/ocr/models/ch_PP-OCRv4_det_server_infer
PADDLE_REC_MODEL_DIR=/absolute/path/to/ocr/models/ch_PP-OCRv4_rec_server_infer
PADDLE_CLS_MODEL_DIR=/absolute/path/to/ocr/models/ch_ppocr_mobile_v2.0_cls_infer
```

### 方法2：直接在代码中配置

如果你是直接在代码中调用OCR引擎，可以这样配置：

```python
from ocr.factory import OCRFactory

config = {
    'use_gpu': False,
    'lang': 'ch',
    'use_angle_cls': True,
    'det_model_dir': '/path/to/ocr/models/ch_PP-OCRv4_det_server_infer',
    'rec_model_dir': '/path/to/ocr/models/ch_PP-OCRv4_rec_server_infer',
    'cls_model_dir': '/path/to/ocr/models/ch_ppocr_mobile_v2.0_cls_infer',
    'show_log': False
}

ocr_engine = OCRFactory.get_ocr_engine('paddleocr', config)
text = ocr_engine.process_file('your_document.pdf')
```

## 测试PaddleOCR

使用提供的示例脚本测试PaddleOCR配置：

```bash
python -m ocr.example_paddle_ocr --image path/to/image.jpg --models_dir ./ocr/models --use_angle_cls
```

这个命令会使用指定的模型目录处理图像并输出识别的文本。

## 注意事项

1. 确保模型目录中包含完整的模型文件（`.pdmodel`、`.pdiparams`、`.pdiparams.info`）
2. PaddleOCR需要检测模型和识别模型才能正常工作，方向分类模型是可选的
3. 默认情况下，应用程序会在`ocr/models`目录中查找模型文件
4. 如果使用GPU，请确保已安装支持GPU的PaddlePaddle版本

## 故障排除

### 1. 模型加载错误

如果遇到模型加载错误，请检查：
- 模型目录路径是否正确
- 模型文件是否完整
- 模型版本是否与PaddleOCR版本匹配

### 2. 内存问题

PaddleOCR模型可能占用较大内存，特别是服务器版本的模型。如果遇到内存不足问题：
- 考虑使用移动版模型（如`ch_PP-OCRv4_det_infer`代替`ch_PP-OCRv4_det_server_infer`）
- 减小处理的图像大小
- 增加系统内存或使用虚拟内存 