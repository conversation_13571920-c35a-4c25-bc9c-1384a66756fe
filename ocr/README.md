# OCR (Optical Character Recognition)

本目录包含了几种OCR引擎的实现，包括PaddleOCR, Tesseract和OLM等。

## 模型目录结构

PaddleOCR模型的目录结构应当如下：

```
ocr/models/
├── ch_PP-OCRv4_det_server_infer/   # 检测模型目录
│   ├── inference.pdiparams
│   ├── inference.pdiparams.info
│   └── inference.pdmodel
├── ch_PP-OCRv4_rec_server_infer/   # 识别模型目录
│   ├── inference.pdiparams
│   ├── inference.pdiparams.info
│   └── inference.pdmodel
└── ch_ppocr_mobile_v2.0_cls_infer/ # 方向分类模型目录
    ├── inference.pdiparams
    ├── inference.pdiparams.info
    └── inference.pdmodel
```

如果你已经下载了 `.tar` 格式的模型文件，可以通过以下命令解压：

```bash
# 在ocr/models目录下
tar -xf ch_PP-OCRv4_det_server_infer.tar
tar -xf ch_PP-OCRv4_rec_server_infer.tar
tar -xf ch_ppocr_mobile_v2.0_cls_infer.tar
```

## 使用PaddleOCR进行OCR识别

PaddleOCR现在支持自定义模型目录，你可以通过以下方式使用：

```python
from ocr.paddle_ocr import PaddleOCR

# 配置自定义模型目录
config = {
    'use_angle_cls': True,  # 是否使用方向分类器
    'lang': 'ch',           # 语言，如'ch', 'en'
    'use_gpu': False,       # 是否使用GPU
    'det_model_dir': '/path/to/ocr/models/ch_PP-OCRv4_det_server_infer',  # 检测模型目录
    'rec_model_dir': '/path/to/ocr/models/ch_PP-OCRv4_rec_server_infer',  # 识别模型目录
    'cls_model_dir': '/path/to/ocr/models/ch_ppocr_mobile_v2.0_cls_infer' # 方向分类模型目录
}

# 初始化PaddleOCR
ocr = PaddleOCR(config)

# 处理图像或文件
# 处理图像
from PIL import Image
import numpy as np
image = Image.open('image.jpg')
text = ocr.process_image(np.array(image))
print(text)

# 处理文件
text = ocr.process_file('document.pdf')
print(text)
```

## 示例脚本

本目录包含了一个示例脚本 `example_paddle_ocr.py`，可以用来测试PaddleOCR的自定义模型：

```bash
python -m ocr.example_paddle_ocr --image /path/to/your/image.jpg --models_dir /path/to/ocr/models --use_angle_cls
```

参数说明：
- `--image`: 要进行OCR识别的图像路径
- `--models_dir`: 模型根目录，默认为`./models`
- `--lang`: 语言，如ch, en，默认为ch
- `--use_gpu`: 是否使用GPU
- `--use_angle_cls`: 是否使用方向分类器 