import os
import tempfile
import base64
import json
import time
import requests
from PIL import Image
from pdf2image import convert_from_path
import docx
from .base_ocr import OCREngine

class BaiduOCR(OCREngine):
    """百度云OCR引擎实现"""
    
    def __init__(self, config=None):
        """
        初始化百度云OCR引擎
        
        Args:
            config (dict): 配置参数，需包含app_id, api_key, secret_key
        """
        self.config = config or {}
        self.available = False
        
        # 检查必要的配置参数
        required_keys = ['app_id', 'api_key', 'secret_key']
        if all(key in self.config for key in required_keys):
            self.app_id = self.config['app_id']
            self.api_key = self.config['api_key']
            self.secret_key = self.config['secret_key']
            self.available = True
            self.access_token = None
            self.token_expires_time = 0
            print("百度云OCR引擎初始化成功")
        else:
            missing_keys = [key for key in required_keys if key not in self.config]
            print(f"错误: 百度云OCR引擎初始化失败，缺少配置参数: {', '.join(missing_keys)}")
    
    def _get_access_token(self):
        """
        获取百度云API的access_token
        
        Returns:
            str: access_token
        """
        current_time = time.time()
        
        # 如果已有token且未过期，直接返回
        if self.access_token and self.token_expires_time > current_time:
            return self.access_token
        
        # 请求获取新token
        url = f"https://aip.baidubce.com/oauth/2.0/token"
        params = {
            'grant_type': 'client_credentials',
            'client_id': self.api_key,
            'client_secret': self.secret_key
        }
        
        try:
            response = requests.post(url, params=params)
            result = response.json()
            
            if 'access_token' in result:
                self.access_token = result['access_token']
                # 设置token过期时间（提前5分钟过期，避免临界问题）
                self.token_expires_time = current_time + int(result.get('expires_in', 2592000)) - 300
                return self.access_token
            else:
                print(f"获取百度云access_token失败: {result}")
                return None
        except Exception as e:
            print(f"获取百度云access_token出错: {e}")
            return None
    
    def _recognize_image(self, image_path):
        """
        使用百度云OCR识别图像
        
        Args:
            image_path (str): 图像文件路径
            
        Returns:
            str: 识别的文本
        """
        try:
            # 获取文件信息
            file_size = os.path.getsize(image_path)
            file_name = os.path.basename(image_path)
            _, file_ext = os.path.splitext(image_path)
            
            print(f"\n=== 使用百度云OCR识别图像 ===")
            print(f"文件名: {file_name}")
            print(f"文件大小: {file_size} 字节")
            print(f"文件类型: {file_ext}")
            
            start_time = time.time()
            
            # 获取access_token
            access_token = self._get_access_token()
            if not access_token:
                return "获取百度云access_token失败"
            
            # 读取图片文件
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            # 图片base64编码
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 请求参数
            api_url = "https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic"
            params = {
                'access_token': access_token
            }
            data = {
                'image': image_base64,
                'language_type': self.config.get('language_type', 'CHN_ENG')  # 默认中英文混合
            }
            
            # 发送OCR请求
            print(f"发送OCR请求...")
            response = requests.post(api_url, params=params, data=data)
            result = response.json()
            
            # 处理响应
            if 'error_code' in result:
                print(f"百度OCR识别错误: {result}")
                return f"识别出错，错误码: {result.get('error_code')}, 错误信息: {result.get('error_msg')}"
            
            # 提取识别的文本
            if 'words_result' in result:
                words_count = len(result['words_result'])
                print(f"识别出 {words_count} 行文本")
                
                # 合并识别的文本
                text = "\n".join([item['words'] for item in result['words_result']])
                
                processing_time = time.time() - start_time
                print(f"处理时间: {processing_time:.2f} 秒")
                return text
            else:
                print("未识别到文本内容")
                return "未识别到文本内容"
                
        except Exception as e:
            print(f"百度OCR识别图像时出错: {e}")
            return f"识别失败: {str(e)}"
    
    def process_image(self, image_path):
        """
        处理图像并返回识别的文本
        
        Args:
            image_path (str): 图像文件路径或PIL.Image对象
            
        Returns:
            str: 识别的文本
        """
        if isinstance(image_path, str):
            result = self._recognize_image(image_path)
            
            # 打印处理结果
            print(f"\n=== 图像OCR处理完成 ===")
            if len(result) > 0:
                print(f"识别出的文本长度: {len(result)} 字符")
            else:
                print("警告: 未能识别出任何文本")
                
            return result
        else:
            # 如果是PIL.Image对象，先保存到临时文件
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp:
                temp_path = temp.name
                image_path.save(temp_path, 'JPEG')
            
            # 识别图像
            result = self._recognize_image(temp_path)
            
            # 打印处理结果
            print(f"\n=== 图像OCR处理完成 ===")
            if len(result) > 0:
                print(f"从PIL图像识别出的文本长度: {len(result)} 字符")
            else:
                print("警告: 从PIL图像未能识别出任何文本")
            
            # 删除临时文件
            os.unlink(temp_path)
            
            return result
    
    def process_file(self, file_path):
        """
        处理文件并返回识别的文本
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: 识别的文本
        """
        print(f"\n=== 百度云OCR 处理文件 ===\n文件路径: {file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在: {file_path}")
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # 检查文件名
        filename = os.path.basename(file_path)
        print(f"文件名: {filename}")
        
        # 获取文件扩展名
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        print(f"文件扩展名: '{ext}'")
        
        try:
            result_text = ""
            if ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
                print("处理图像文件")
                result_text = self.process_image(file_path)
            elif ext == '.pdf':
                print("处理PDF文件")
                result_text = self.process_pdf(file_path)
            elif ext in ['.doc', '.docx']:
                print(f"处理Word文档: {ext}")
                result_text = self.process_document(file_path)
            else:
                print(f"警告: 不支持的文件格式: '{ext}'，尝试作为图像处理")
                # 尝试作为图像处理
                result_text = self.process_image(file_path)
                
            # 打印识别结果摘要
            if result_text:
                # 打印文本长度
                print(f"\n=== OCR识别结果 ===")
                print(f"识别文本长度: {len(result_text)} 字符")
                
                # 打印完整的识别结果，不省略内容
                print(f"识别结果:\n{result_text}")
                    
                print("=== OCR识别完成 ===\n")
            else:
                print("警告: OCR识别结果为空")
                
            return result_text
            
        except Exception as e:
            print(f"处理文件时出错: {type(e).__name__}: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
    
    def process_pdf(self, pdf_path):
        """
        使用百度云OCR处理PDF
        
        Args:
            pdf_path (str): PDF文件路径
            
        Returns:
            str: 识别的文本
        """
        try:
            print(f"开始处理PDF文件: {pdf_path}")
            # 将PDF转换为图像
            images = convert_from_path(pdf_path)
            print(f"PDF共有 {len(images)} 页")
            
            # 处理每一页并合并文本
            all_text = []
            for i, image in enumerate(images):
                print(f"处理第 {i+1}/{len(images)} 页...")
                # 保存图像到临时文件
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp:
                    temp_path = temp.name
                    image.save(temp_path, 'JPEG')
                
                # 处理图像
                page_text = self.process_image(temp_path)
                page_header = f"--- 第 {i+1} 页 ---"
                all_text.append(f"{page_header}\n{page_text}")
                
                # 打印当前页处理结果
                print(f"第 {i+1} 页识别完成，文本长度: {len(page_text)} 字符")
                
                # 删除临时文件
                os.unlink(temp_path)
            
            # 合并所有页面的文本
            combined_text = "\n\n".join(all_text)
            
            # 打印处理结果
            print(f"\n=== PDF处理完成 ===")
            print(f"总文本长度: {len(combined_text)} 字符")
            
            return combined_text
            
        except Exception as e:
            print(f"百度云OCR处理PDF时出错: {e}")
            return f"处理PDF失败: {str(e)}"
    
    def process_document(self, doc_path):
        """
        处理Word文档
        
        Args:
            doc_path (str): Word文档路径
            
        Returns:
            str: 识别的文本
        """
        try:
            # 检查文件扩展名
            _, ext = os.path.splitext(doc_path)
            ext = ext.lower()
            
            print(f"处理Word文档: {doc_path}，文件类型: {ext}")
            
            result_text = ""
            if ext == '.docx':
                # 如果是.docx文档，使用python-docx提取文本
                print("使用python-docx处理.docx文件")
                try:
                    doc = docx.Document(doc_path)
                    text = "\n".join([para.text for para in doc.paragraphs if para.text])
                    # 确保提取的文本不为空
                    if not text.strip():
                        print("警告: 从docx文档中提取的文本为空")
                        # 尝试从表格中提取文本
                        tables_text = []
                        for table in doc.tables:
                            for row in table.rows:
                                row_text = " ".join([cell.text for cell in row.cells if cell.text])
                                if row_text:
                                    tables_text.append(row_text)
                        if tables_text:
                            text += "\n" + "\n".join(tables_text)
                            
                    result_text = text
                    # 打印处理结果
                    print(f"\n=== DOCX处理完成 ===")
                    print(f"文档段落数: {len(doc.paragraphs)}")
                    print(f"文档表格数: {len(doc.tables)}")
                    print(f"提取的文本长度: {len(result_text)} 字符")
                    
                    return result_text
                except Exception as docx_error:
                    print(f"使用python-docx处理文件时出错: {docx_error}")
                    raise
                    
            elif ext == '.doc':
                # 对于.doc文档，在Linux环境下使用antiword工具
                print("处理.doc文件，使用antiword...")
                
                # 使用antiword (Linux环境)
                import subprocess
                import shutil
                
                if shutil.which('antiword'):
                    try:
                        print("使用antiword处理.doc文件")
                        result = subprocess.run(['antiword', doc_path], 
                                               capture_output=True, 
                                               text=True, 
                                               encoding='utf-8')
                        if result.returncode == 0:
                            result_text = result.stdout
                            # 打印处理结果
                            print(f"\n=== DOC处理完成 (antiword) ===")
                            print(f"提取的文本长度: {len(result_text)} 字符")
                            print(f"识别结果:\n{result_text}")
                            return result_text
                        else:
                            print(f"antiword处理错误，返回码: {result.returncode}")
                            print(f"错误输出: {result.stderr}")
                    except Exception as antiword_error:
                        print(f"使用antiword处理时出错: {antiword_error}")
                else:
                    print("系统中未安装antiword工具")
                
                # 如果antiword处理失败或未安装，建议用户转换格式
                print("无法处理.doc文件，请安装antiword或将文件转换为.docx格式")
                result_text = ""
                return result_text
            else:
                raise ValueError(f"不支持的文档格式: {ext}")
                
        except Exception as e:
            import traceback
            print(f"处理Word文档时出错: {e}")
            print("详细错误信息:")
            traceback.print_exc()
            return f"处理文档失败: {str(e)}" 