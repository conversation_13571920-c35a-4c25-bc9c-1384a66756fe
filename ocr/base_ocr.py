"""
OCR引擎基类定义
"""
import logging

logger = logging.getLogger(__name__)

class OCREngine:
    """OCR引擎基类，所有OCR引擎实现都应该继承此类"""
    
    def __init__(self, config=None):
        """
        初始化OCR引擎
        
        Args:
            config (dict): 配置信息
        """
        self.config = config or {}
        self.available = False
    
    def process_file(self, file_path):
        """
        处理文件并返回识别的文本
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: 识别的文本
        """
        raise NotImplementedError("子类必须实现process_file方法")
    
    def process_image(self, image):
        """
        处理图像并返回识别的文本
        
        Args:
            image: PIL.Image或numpy数组
            
        Returns:
            str: 识别的文本
        """
        raise NotImplementedError("子类必须实现process_image方法")
