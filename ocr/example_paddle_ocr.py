#!/usr/bin/env python
"""
使用PaddleOCR的示例，展示如何指定自定义模型目录
"""
import os
import sys
import argparse
import logging
from PIL import Image
import numpy as np

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PaddleOCR自定义模型目录示例')
    parser.add_argument('--image', required=True, help='输入图像路径')
    parser.add_argument('--models_dir', default='./models', help='模型根目录，默认为./models')
    parser.add_argument('--lang', default='ch', help='语言，如ch, en，默认为ch')
    parser.add_argument('--use_gpu', action='store_true', help='是否使用GPU')
    parser.add_argument('--use_angle_cls', action='store_true', help='是否使用方向分类器')
    
    args = parser.parse_args()
    
    # 检查输入图像是否存在
    if not os.path.exists(args.image):
        logger.error(f"输入图像不存在: {args.image}")
        return
    
    # 检查模型目录是否存在
    if not os.path.exists(args.models_dir):
        logger.error(f"模型目录不存在: {args.models_dir}")
        return
    
    # 构建模型路径
    det_model_dir = os.path.join(args.models_dir, f"ch_PP-OCRv4_det_server_infer")
    rec_model_dir = os.path.join(args.models_dir, f"ch_PP-OCRv4_rec_server_infer")
    cls_model_dir = os.path.join(args.models_dir, f"ch_ppocr_mobile_v2.0_cls_infer")
    
    # 检查模型目录是否存在
    if args.use_angle_cls and not os.path.exists(cls_model_dir):
        logger.warning(f"方向分类模型目录不存在: {cls_model_dir}")
        logger.warning("将禁用方向分类")
        args.use_angle_cls = False
    
    # 导入PaddleOCR类
    try:
        from ocr.paddle_ocr import PaddleOCR
    except ImportError:
        logger.error("无法导入PaddleOCR类，确保当前目录是项目根目录")
        return
    
    # 配置PaddleOCR
    config = {
        'use_angle_cls': args.use_angle_cls,
        'use_gpu': args.use_gpu,
        'lang': args.lang,
        'det_model_dir': det_model_dir,
        'rec_model_dir': rec_model_dir,
    }
    
    if args.use_angle_cls:
        config['cls_model_dir'] = cls_model_dir
    
    # 初始化PaddleOCR
    logger.info("正在初始化PaddleOCR...")
    ocr = PaddleOCR(config)
    
    # 处理图像
    logger.info(f"正在处理图像: {args.image}")
    try:
        image = Image.open(args.image).convert('RGB')
        image_np = np.array(image)
        
        # 使用OCR处理图像
        text = ocr.process_image(image_np)
        
        # 显示结果
        if text:
            logger.info("OCR识别结果:")
            print("-" * 50)
            print(text)
            print("-" * 50)
        else:
            logger.warning("未识别到文本")
            
    except Exception as e:
        logger.error(f"处理图像时出错: {str(e)}")
        
if __name__ == "__main__":
    main() 