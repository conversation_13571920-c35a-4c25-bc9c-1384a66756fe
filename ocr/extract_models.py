#!/usr/bin/env python
"""
提取PaddleOCR模型文件脚本
如果模型文件以.tar结尾且对应的目录不存在，则进行解压
"""
import os
import tarfile
import logging
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_model(tar_path, extract_dir):
    """
    解压模型文件
    
    Args:
        tar_path (str): tar文件路径
        extract_dir (str): 解压目录
    
    Returns:
        bool: 解压是否成功
    """
    if not os.path.exists(tar_path):
        logger.error(f"模型文件不存在: {tar_path}")
        return False
    
    try:
        with tarfile.open(tar_path, 'r') as tar:
            # 创建解压目录（如果不存在）
            os.makedirs(extract_dir, exist_ok=True)
            
            # 解压文件
            logger.info(f"正在解压 {tar_path} 到 {extract_dir}")
            tar.extractall(path=extract_dir)
            
            logger.info(f"解压完成: {tar_path}")
            return True
    except Exception as e:
        logger.error(f"解压 {tar_path} 失败: {str(e)}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='提取PaddleOCR模型文件')
    parser.add_argument('--models_dir', default='./ocr/models', help='模型目录，默认为./ocr/models')
    parser.add_argument('--force', action='store_true', help='强制重新解压，即使目录已存在')
    
    args = parser.parse_args()
    
    # 检查模型目录是否存在
    if not os.path.exists(args.models_dir):
        logger.error(f"模型目录不存在: {args.models_dir}")
        return
    
    # 需要处理的模型文件和对应的解压目录
    model_files = [
        "ch_PP-OCRv4_det_server_infer.tar",
        "ch_PP-OCRv4_rec_server_infer.tar",
        "ch_ppocr_mobile_v2.0_cls_infer.tar"
    ]
    
    for model_file in model_files:
        # 构建完整路径
        tar_path = os.path.join(args.models_dir, model_file)
        extract_dir_name = os.path.splitext(model_file)[0]  # 去掉.tar后缀
        extract_dir = os.path.join(args.models_dir, extract_dir_name)
        
        # 检查是否需要解压
        if not os.path.exists(tar_path):
            logger.warning(f"模型文件不存在，跳过: {tar_path}")
            continue
        
        if os.path.exists(extract_dir) and not args.force:
            logger.info(f"目录已存在，跳过解压: {extract_dir}")
            continue
        
        # 解压模型文件
        success = extract_model(tar_path, extract_dir)
        if not success:
            logger.error(f"解压失败: {model_file}")
    
    logger.info("所有模型文件处理完成")

if __name__ == "__main__":
    main() 