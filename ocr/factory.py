from .olm_ocr import OlmOCR
from .tesseract_ocr import TesseractOCR
from .paddle_ocr import PaddleOCR
from .baidu_ocr import BaiduOCR

class OCRFactory:
    """OCR引擎工厂类，根据配置选择OCR引擎"""
    
    @staticmethod
    def get_ocr_engine(engine_type="tesseract", config=None):
        """
        获取OCR引擎实例
        
        Args:
            engine_type (str): OCR引擎类型，支持'olm'、'tesseract'、'paddleocr'和'baidu'
            config (dict): OCR引擎配置
            
        Returns:
            OCREngine: OCR引擎实例
            
        Raises:
            ValueError: 不支持的OCR引擎类型
        """
        print(f"\n=== 创建OCR引擎 ===\n引擎类型: {engine_type}")
        print(f"配置信息: {config}")
        
        if engine_type.lower() == "olm":
            print("使用OLM OCR引擎")
            return OlmOCR(config)
        elif engine_type.lower() == "tesseract":
            print("使用Tesseract OCR引擎")
            return TesseractOCR(config)
        elif engine_type.lower() == "paddleocr":
            print("使用PaddleOCR引擎")
            return PaddleOCR(config)
        elif engine_type.lower() == "baidu":
            print("使用百度云OCR引擎")
            return BaiduOCR(config)
        else:
            print(f"错误: 不支持的OCR引擎类型: {engine_type}")
            raise ValueError(f"Unsupported OCR engine: {engine_type}")
