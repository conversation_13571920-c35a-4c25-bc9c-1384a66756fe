"""
PaddleOCR引擎实现
"""
import os
import logging
import tempfile
import io
import numpy as np
from PIL import Image
from .base_ocr import OCREngine

logger = logging.getLogger(__name__)

class PaddleOCR(OCREngine):
    """PaddleOCR引擎实现"""
    
    def __init__(self, config=None):
        """
        初始化PaddleOCR引擎
        
        Args:
            config (dict): 配置信息
                use_angle_cls (bool): 是否使用方向分类器
                lang (str): 语言，如'ch', 'en'
                use_gpu (bool): 是否使用GPU
                show_log (bool): 是否显示日志
                det_model_dir (str): 检测模型目录路径
                rec_model_dir (str): 识别模型目录路径
                cls_model_dir (str): 分类模型目录路径
        """
        super().__init__(config)
        self.config = config or {}
        
        # 延迟导入PaddleOCR，因为它可能不是必需的依赖
        try:
            from paddleocr import PaddleOCR as POCR
            self.use_angle_cls = self.config.get('use_angle_cls', True)
            self.lang = self.config.get('lang', 'ch')  # 默认中文
            
            # 构建PaddleOCR初始化参数
            paddle_params = {
                'use_angle_cls': self.use_angle_cls,
                'lang': self.lang,
                'use_gpu': self.config.get('use_gpu', False),
                'show_log': self.config.get('show_log', False)
            }
            
            # 添加模型目录参数（如果在配置中提供）
            if 'det_model_dir' in self.config:
                paddle_params['det_model_dir'] = self.config['det_model_dir']
                logger.info(f"使用自定义检测模型目录: {self.config['det_model_dir']}")
                
            if 'rec_model_dir' in self.config:
                paddle_params['rec_model_dir'] = self.config['rec_model_dir']
                logger.info(f"使用自定义识别模型目录: {self.config['rec_model_dir']}")
                
            if 'cls_model_dir' in self.config and self.use_angle_cls:
                paddle_params['cls_model_dir'] = self.config['cls_model_dir']
                logger.info(f"使用自定义方向分类模型目录: {self.config['cls_model_dir']}")
            
            # 初始化PaddleOCR
            self.ocr = POCR(**paddle_params)
            self.available = True
            logger.info("PaddleOCR引擎初始化成功")
            
        except ImportError as e:
            logger.error(f"无法导入PaddleOCR，请安装paddlepaddle和paddleocr: {str(e)}")
            self.available = False
            
        # 初始化文档处理相关的属性
        self.docx_available = False
        try:
            import docx
            self.docx_available = True
            logger.info("python-docx库可用，支持处理Word文档")
        except ImportError:
            logger.warning("无法导入python-docx，将无法处理Word文档")
    
    def process_file(self, file_path):
        """
        处理文件并返回识别的文本
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: 识别的文本
        """
        if not self.available:
            logger.error("PaddleOCR引擎不可用，请安装paddlepaddle和paddleocr")
            return ""
        
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return ""
        
        # 根据文件扩展名选择处理方法
        file_ext = os.path.splitext(file_path)[1].lower()
        
        # 处理Word文档
        if file_ext in [".docx", ".doc"] and self.docx_available:
            if file_ext == ".docx":
                return self.process_docx(file_path)
            else:
                logger.warning("目前只支持.docx格式，.doc格式可能无法正确处理")
                return self.process_docx(file_path)  # 尝试处理.doc文件
        
        # 处理图像和PDF
        try:
            # 使用PaddleOCR识别图像
            result = self.ocr.ocr(file_path, cls=self.use_angle_cls)
            
            # 提取文本
            text = ""
            for idx, res in enumerate(result):
                if res:  # 确保结果不为空
                    for line in res:
                        # line结构: [[[x1,y1],[x2,y2],[x3,y3],[x4,y4]], (text, confidence)]
                        if len(line) >= 2 and isinstance(line[1], tuple) and len(line[1]) >= 1:
                            text += line[1][0] + "\n"
            
            print(f"PaddleOCR成功处理文件: {file_path}")
            return text
        except Exception as e:
            logger.error(f"PaddleOCR处理文件时出错: {str(e)}")
            return ""
    
    def process_image(self, image):
        """
        处理图像并返回识别的文本
        
        Args:
            image: PIL.Image或numpy数组
            
        Returns:
            str: 识别的文本
        """
        if not self.available:
            logger.error("PaddleOCR引擎不可用，请安装paddlepaddle和paddleocr")
            return ""
        
        try:
            # 确保图像是numpy数组
            if isinstance(image, Image.Image):
                image = np.array(image)
            
            # 使用PaddleOCR识别图像
            result = self.ocr.ocr(image, cls=self.use_angle_cls)
            
            # 提取文本
            text = ""
            for idx, res in enumerate(result):
                if res:  # 确保结果不为空
                    for line in res:
                        # line结构: [[[x1,y1],[x2,y2],[x3,y3],[x4,y4]], (text, confidence)]
                        if len(line) >= 2 and isinstance(line[1], tuple) and len(line[1]) >= 1:
                            text += line[1][0] + "\n"
            
            print("PaddleOCR成功处理图像")
            return text
        except Exception as e:
            logger.error(f"PaddleOCR处理图像时出错: {str(e)}")
            return ""
    
    def process_docx(self, file_path):
        """
        处理docx文件：直接解析文本，对嵌入图片使用OCR
        
        Args:
            file_path (str): docx文件路径
        
        Returns:
            str: 提取的文本内容（包括OCR处理图片的结果）
        """
        if not self.docx_available:
            logger.error("python-docx库不可用，无法处理Word文档")
            return ""
            
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return ""
        
        try:
            # 导入docx库
            from docx import Document
            
            # 打开docx文件
            doc = Document(file_path)
            
            # 提取文本内容
            text_content = []
            for para in doc.paragraphs:
                if para.text.strip():
                    text_content.append(para.text)
                
            # 提取表格内容
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text)
                    if row_text:
                        text_content.append(" | ".join(row_text))
            
            # 提取并处理图片
            image_text = []
            with tempfile.TemporaryDirectory() as temp_dir:
                image_count = 0
                for rel in doc.part.rels.values():
                    if "image" in rel.target_ref:
                        try:
                            # 提取图片
                            image_blob = rel.target_part.blob
                            image = Image.open(io.BytesIO(image_blob))
                            
                            # 保存到临时文件
                            image_path = os.path.join(temp_dir, f"image_{image_count}.png")
                            image.save(image_path)
                            
                            # 使用OCR处理图片
                            img_text = self.process_image(image)
                            if img_text:
                                image_text.append(f"[图片{image_count+1}内容]: {img_text}")
                                
                            image_count += 1
                                
                        except Exception as e:
                            logger.error(f"处理文档中的图片时出错: {str(e)}")
            
            # 合并文本内容和图片OCR结果
            all_text = "\n".join(text_content)
            if image_text:
                all_text += "\n\n图片OCR内容:\n" + "\n".join(image_text)
                
            print(f"成功从docx文件提取文本和处理图片: {file_path}，文本段落数: {len(text_content)}，处理图片数: {len(image_text)}")
            return all_text
            
        except Exception as e:
            logger.error(f"处理docx文件时出错: {str(e)}")
            return ""
    
    def version(self):
        """
        返回OCR引擎版本
        
        Returns:
            str: 版本信息
        """
        return "PaddleOCR引擎"
