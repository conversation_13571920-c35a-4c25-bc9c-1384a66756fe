import os
import tempfile
from PIL import Image
import pytesseract
from pdf2image import convert_from_path
import docx
from .olm_ocr import OCREngine

class TesseractOCR(OCREngine):
    """Tesseract OCR引擎实现"""
    
    def __init__(self, config=None):
        """
        初始化Tesseract OCR引擎
        
        Args:
            config (dict): 配置参数
        """
        self.config = config or {}
        
        # 设置Tesseract命令路径
        if 'tesseract_cmd' in self.config:
            pytesseract.pytesseract.tesseract_cmd = self.config['tesseract_cmd']
    
    def process_file(self, file_path):
        """
        处理文件并返回识别的文本
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: 识别的文本
        """
        print(f"\n=== Tesseract OCR 处理文件 ===\n文件路径: {file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在: {file_path}")
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # 检查文件名
        filename = os.path.basename(file_path)
        print(f"文件名: {filename}")
        
        # 检查文件是否有扩展名
        if '.' not in filename:
            print(f"错误: 文件没有扩展名: {filename}")
            # 尝试根据文件内容判断文件类型
            # 这里可以添加更复杂的文件类型检测逻辑
            # 暂时将未知类型的文件当作图像处理
            print("尝试将文件当作图像处理")
            return self.process_image(file_path)
            
        # 获取文件扩展名
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        print(f"文件扩展名: '{ext}'")
        
        # 检查扩展名是否为空
        if not ext:
            print("错误: 文件扩展名为空")
            # 尝试根据文件内容判断文件类型
            print("尝试将文件当作图像处理")
            return self.process_image(file_path)
        
        try:
            if ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
                print("处理图像文件")
                return self.process_image(file_path)
            elif ext == '.pdf':
                print("处理PDF文件")
                return self.process_pdf(file_path)
            elif ext in ['.doc', '.docx']:
                print(f"处理Word文档: {ext}")
                return self.process_document(file_path)
            else:
                print(f"错误: 不支持的文件格式: '{ext}'")
                # 尝试根据文件内容判断文件类型
                print("尝试将文件当作图像处理")
                return self.process_image(file_path)
        except Exception as e:
            print(f"处理文件时出错: {type(e).__name__}: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
    
    def process_image(self, image_path):
        """
        使用Tesseract OCR处理图像
        
        Args:
            image_path (str): 图像文件路径
            
        Returns:
            str: 识别的文本
        """
        try:
            # 加载图像
            image = Image.open(image_path)
            
            # 使用Tesseract进行OCR识别
            # 使用配置文件中的语言设置
            lang = self.config.get('lang', 'chi_sim+eng')
            text = pytesseract.image_to_string(image, lang=lang)
            
            return text
            
        except Exception as e:
            print(f"Tesseract OCR处理图像时出错: {e}")
            return ""
    
    def process_pdf(self, pdf_path):
        """
        使用Tesseract OCR处理PDF
        
        Args:
            pdf_path (str): PDF文件路径
            
        Returns:
            str: 识别的文本
        """
        try:
            # 将PDF转换为图像
            images = convert_from_path(pdf_path)
            
            # 处理每一页并合并文本
            all_text = []
            for i, image in enumerate(images):
                # 保存图像到临时文件
                with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp:
                    temp_path = temp.name
                    image.save(temp_path, 'JPEG')
                
                # 处理图像
                page_text = self.process_image(temp_path)
                all_text.append(f"--- 第 {i+1} 页 ---\n{page_text}")
                
                # 删除临时文件
                os.unlink(temp_path)
            
            return "\n\n".join(all_text)
            
        except Exception as e:
            print(f"Tesseract OCR处理PDF时出错: {e}")
            return ""
    
    def process_document(self, doc_path):
        """
        使用Tesseract OCR处理Word文档
        
        Args:
            doc_path (str): Word文档路径
            
        Returns:
            str: 识别的文本
        """
        try:
            # 打印文件信息以便调试
            print(f"处理Word文档: {doc_path}")
            print(f"文件存在: {os.path.exists(doc_path)}")
            print(f"文件大小: {os.path.getsize(doc_path)} 字节")
            print(f"文件扩展名: {os.path.splitext(doc_path)[1]}")
            
            # 检查文件扩展名
            _, ext = os.path.splitext(doc_path)
            ext = ext.lower()
            
            if ext == '.docx':
                # 如果是.docx文档，使用python-docx提取文本
                doc = docx.Document(doc_path)
                return "\n".join([para.text for para in doc.paragraphs if para.text])
            elif ext == '.doc':
                # 对于.doc文档，可能需要其他处理方法
                # 这里简单返回一个提示信息，实际应用中可能需要使用其他库处理
                print("警告: .doc格式需要额外的库支持，如antiword或textract")
                return "[不支持的.doc格式，请转换为.docx格式]"  
            else:
                raise ValueError(f"不支持的文档格式: {ext}")
                
        except Exception as e:
            import traceback
            print(f"处理Word文档时出错: {e}")
            print("详细错误信息:")
            traceback.print_exc()
            return ""
