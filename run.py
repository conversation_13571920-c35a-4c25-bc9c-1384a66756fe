#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
起诉状格式化软件启动脚本
"""

import os
import sys
import webbrowser

def create_template():
    """创建模板文件"""
    template_path = os.path.join('static', 'templates', 'complaint_template.docx')
    
    if not os.path.exists(template_path):
        print("正在创建Word模板文件...")
        try:
            from create_template import create_template
            create_template()
        except Exception as e:
            print(f"创建模板失败: {e}")
            return False
    
    return True

def run_app():
    """运行Flask应用"""
    from app import app, socketio
    from config import Config
    
    # 从配置文件获取端口
    port = Config.PORT
    
    print(f"启动服务器，地址: http://0.0.0.0:{port}")
    
    # 打开浏览器
#    webbrowser.open(f"http://127.0.0.1:{port}")
    
    # 运行应用
    socketio.run(app, debug=True, host='0.0.0.0', port=port, allow_unsafe_werkzeug=True)

if __name__ == '__main__':
    print("=== 起诉状格式化软件 ===")
    
    # 创建模板
    if create_template():
        # 运行应用
        run_app()
    else:
        print("程序初始化失败，请检查错误信息。")
        sys.exit(1)
