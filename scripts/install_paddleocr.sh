#!/bin/bash
# 安装PaddleOCR依赖的脚本

# 确保在conda环境中运行
if [[ -z "${CONDA_PREFIX}" ]]; then
    echo "错误: 请在conda环境中运行此脚本"
    echo "使用 'conda activate complaint-llm' 激活环境后再运行"
    exit 1
fi

echo "=== 安装PaddleOCR依赖 ==="
echo "conda环境: $CONDA_PREFIX"

# 安装paddlepaddle CPU版本
echo "正在安装paddlepaddle..."
pip install paddlepaddle==2.5.1

# 安装paddleocr
echo "正在安装paddleocr..."
pip install paddleocr==2.7.0

# 安装其他依赖
echo "正在安装其他依赖..."
pip install shapely pyclipper opencv-python

echo "=== 安装完成 ==="
echo "可以通过运行测试来验证安装:"
echo "python tests/test_paddle_ocr.py"
