#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步templates到templates2目录的脚本
保持两个目录内容一致，用于下载功能
"""

import os
import shutil
import sys

def sync_templates():
    """同步templates目录到templates2目录"""
    base_dir = os.path.join(os.getcwd(), 'static')
    templates_dir = os.path.join(base_dir, 'templates')
    templates2_dir = os.path.join(base_dir, 'templates2')
    
    print("开始同步模板文件...")
    
    # 清空templates2目录（保留replace.py）
    if os.path.exists(templates2_dir):
        for item in os.listdir(templates2_dir):
            if item != 'replace.py':  # 保留replace.py文件
                item_path = os.path.join(templates2_dir, item)
                if os.path.isfile(item_path):
                    os.remove(item_path)
                    print(f"删除文件: {item}")
                elif os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                    print(f"删除目录: {item}")
    else:
        os.makedirs(templates2_dir, exist_ok=True)
    
    # 遍历templates目录并复制所有docx文件到templates2根目录
    file_count = 0
    for batch_name in ['第一批', '第二批']:
        batch_dir = os.path.join(templates_dir, batch_name)
        if os.path.exists(batch_dir):
            for category_name in os.listdir(batch_dir):
                category_dir = os.path.join(batch_dir, category_name)
                if os.path.isdir(category_dir):
                    for filename in os.listdir(category_dir):
                        if filename.endswith('.docx') and not filename.startswith('~'):
                            src_path = os.path.join(category_dir, filename)
                            dst_path = os.path.join(templates2_dir, filename)
                            
                            try:
                                shutil.copy2(src_path, dst_path)
                                print(f"复制: {batch_name}/{category_name}/{filename} → {filename}")
                                file_count += 1
                            except Exception as e:
                                print(f"错误: 复制文件失败 {filename}: {e}")
    
    print(f"\n同步完成！共复制了 {file_count} 个模板文件")

def main():
    """主函数"""
    try:
        sync_templates()
    except Exception as e:
        print(f"同步过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
