"""
打印服务模块 - 负责处理文档打印功能
"""

import os
import sys
import logging
import tempfile
from pathlib import Path

# 检测操作系统
IS_WINDOWS = sys.platform.startswith('win')
IS_MACOS = sys.platform.startswith('darwin')
IS_LINUX = sys.platform.startswith('linux')

logger = logging.getLogger(__name__)

# Windows系统不使用CUPS
if IS_WINDOWS:
    CUPS_AVAILABLE = False
    logger.info("检测到Windows系统，将使用Windows打印API")
else:
    # 尝试导入cups模块 (Linux/macOS)
    try:
        import cups
        CUPS_AVAILABLE = True
        logger.info("CUPS模块加载成功")
    except ImportError:
        CUPS_AVAILABLE = False
        logger.warning("未找到CUPS模块，打印功能将受限")
    except AttributeError:
        # 处理 'module 'cups' has no attribute 'Connection'' 错误
        CUPS_AVAILABLE = False
        logger.warning("CUPS模块缺少Connection属性，打印功能将受限")

class PrinterService:
    """打印服务类，提供文档打印功能"""

    def __init__(self):
        """初始化打印服务"""
        self.conn = None
        self.printers = {}
        self.default_printer = None

        # Windows系统
        if IS_WINDOWS:
            try:
                # 尝试导入win32print模块
                import win32print
                self.win32print = win32print

                # 获取默认打印机
                self.default_printer = win32print.GetDefaultPrinter()

                # 获取打印机列表
                printer_list = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)
                for printer in printer_list:
                    printer_name = printer[2]
                    self.printers[printer_name] = {'name': printer_name}

                logger.info(f"Windows打印系统初始化成功，默认打印机: {self.default_printer}, 发现 {len(self.printers)} 个打印机")
            except ImportError:
                logger.warning("未找到win32print模块，Windows打印功能将不可用")
            except Exception as e:
                logger.error(f"初始化Windows打印系统失败: {str(e)}")

        # Linux/macOS系统
        elif CUPS_AVAILABLE:
            try:
                self.conn = cups.Connection()
                self.printers = self.conn.getPrinters()
                self.default_printer = self.conn.getDefault()
                logger.info(f"成功连接到CUPS服务，默认打印机: {self.default_printer}, 发现 {len(self.printers)} 个打印机")
            except Exception as e:
                logger.error(f"连接CUPS服务失败: {str(e)}")
                self.conn = None
                self.printers = {}
        else:
            logger.warning("未找到可用的打印系统，打印功能将不可用")

    def get_default_printer(self):
        """获取默认打印机名称"""
        # 直接返回初始化时获取的默认打印机
        if self.default_printer:
            return self.default_printer

        # Windows系统
        if IS_WINDOWS:
            try:
                import win32print
                return win32print.GetDefaultPrinter()
            except ImportError:
                logger.error("未找到win32print模块，无法获取默认打印机")
                return None
            except Exception as e:
                logger.error(f"获取Windows默认打印机失败: {str(e)}")
                return None

        # Linux/macOS系统
        elif CUPS_AVAILABLE and self.conn:
            try:
                return self.conn.getDefault()
            except Exception as e:
                logger.error(f"获取CUPS默认打印机失败: {str(e)}")
                return None

        return None

    def get_all_printers(self):
        """获取所有可用打印机列表"""
        return list(self.printers.keys())

    def print_file(self, file_path, printer_name=None, job_title="打印任务"):
        """
        打印文件

        Args:
            file_path (str): 要打印的文件路径
            printer_name (str, optional): 打印机名称，如果为None则使用默认打印机
            job_title (str, optional): 打印任务标题

        Returns:
            int: 打印任务ID，如果打印失败则返回None
        """
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return None

        # 如果未指定打印机，使用默认打印机
        if not printer_name:
            printer_name = self.get_default_printer()
            if not printer_name:
                logger.error("未指定打印机且无法获取默认打印机")
                return None

        # Windows系统
        if IS_WINDOWS:
            try:
                import win32print
                import win32api

                # 检查打印机是否存在
                printer_list = win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS)
                printer_names = [printer[2] for printer in printer_list]

                if printer_name not in printer_names:
                    logger.error(f"指定的打印机不存在: {printer_name}")
                    return None

                # 打印文件
                win32print.SetDefaultPrinter(printer_name)
                win32api.ShellExecute(0, "print", file_path, None, ".", 0)

                logger.info(f"文件 {file_path} 已发送到Windows打印机 {printer_name}")
                return 1  # 返回一个虚拟的任务ID
            except ImportError:
                logger.error("未找到win32print或win32api模块，无法打印文件")
                return None
            except Exception as e:
                logger.error(f"Windows打印文件失败: {str(e)}")
                return None

        # Linux/macOS系统 (CUPS)
        elif CUPS_AVAILABLE and self.conn:
            # 检查指定的打印机是否存在
            if printer_name not in self.printers:
                logger.error(f"指定的打印机不存在: {printer_name}")
                return None

            try:
                # 打印文件
                job_id = self.conn.printFile(
                    printer_name,
                    file_path,
                    job_title,
                    {}  # 打印选项，可以根据需要设置
                )
                logger.info(f"文件 {file_path} 已发送到CUPS打印机 {printer_name}，任务ID: {job_id}")
                return job_id
            except Exception as e:
                logger.error(f"CUPS打印文件失败: {str(e)}")
                return None
        else:
            logger.error("未找到可用的打印系统，无法打印文件")
            return None

    def get_job_status(self, job_id):
        """
        获取打印任务状态

        Args:
            job_id (int): 打印任务ID

        Returns:
            dict: 打印任务状态信息
        """
        # Windows系统 - 不支持获取任务状态，返回一个模拟的状态
        if IS_WINDOWS:
            logger.info("Windows系统不支持获取打印任务状态，返回模拟状态")
            return {'state': 'processing', 'printer': self.get_default_printer()}

        # Linux/macOS系统 (CUPS)
        elif CUPS_AVAILABLE and self.conn:
            try:
                jobs = self.conn.getJobs()
                if job_id in jobs:
                    return jobs[job_id]
                # 任务不在当前队列中，可能已完成
                return {'state': 'completed'}
            except Exception as e:
                logger.error(f"获取CUPS打印任务状态失败: {str(e)}")
                return None
        else:
            logger.error("未找到可用的打印系统，无法获取打印任务状态")
            return None

# 创建打印服务单例
printer_service = PrinterService()
