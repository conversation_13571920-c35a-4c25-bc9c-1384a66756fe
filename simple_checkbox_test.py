#!/usr/bin/env python3
"""
简单的复选框识别测试
"""

import re
from typing import List, Dict, Any

def test_checkbox_extraction():
    """测试复选框提取逻辑"""
    
    # 测试文本样例
    test_texts = [
        "提前还款(加速到期)□/解除合同□",
        "是□否□",
        "有□无□",
        "同意□不同意□",
        "□已阅读条款",
        "接受□拒绝□",
        "男□女□",
        "已婚□未婚□离异□",
        "本科□硕士□博士□"
    ]
    
    print("复选框提取测试")
    print("=" * 50)
    
    for text in test_texts:
        print(f"\n原始文本: {text}")
        checkboxes = extract_checkboxes_from_text(text)
        print(f"识别到 {len(checkboxes)} 个复选框:")
        for i, checkbox in enumerate(checkboxes):
            print(f"  {i+1}. 文本: '{checkbox['text']}' 字符: '{checkbox['char']}'")

def extract_checkboxes_from_text(text: str) -> List[Dict[str, Any]]:
    """从文本中提取所有复选框选项"""
    if not text or not text.strip():
        return []
    
    checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓"]
    
    # 检查是否包含复选框字符
    if not any(char in text for char in checkbox_chars):
        return []
    
    found_checkboxes = []
    
    # 使用正则表达式找到所有复选框及其相关文本
    patterns = [
        # 模式1: 文本□ (复选框在文本后面)
        r'([^□☐☑■✓✔✗✘×☒☓/\n]+)([□☐☑■✓✔✗✘×☒☓])',
        # 模式2: □文本 (复选框在文本前面)
        r'([□☐☑■✓✔✗✘×☒☓])([^□☐☑■✓✔✗✘×☒☓/\n]+)',
    ]
    
    for pattern in patterns:
        matches = re.finditer(pattern, text)
        for match in matches:
            if pattern.startswith('([^'):  # 文本在前的模式
                checkbox_text = match.group(1).strip()
                checkbox_char = match.group(2)
            else:  # 复选框在前的模式
                checkbox_char = match.group(1)
                checkbox_text = match.group(2).strip()
            
            if checkbox_text and len(checkbox_text) > 0:
                # 清理文本
                cleaned_text = clean_checkbox_text(checkbox_text)
                if cleaned_text:
                    found_checkboxes.append({
                        'text': cleaned_text,
                        'char': checkbox_char,
                        'position': match.start(),
                        'full_match': match.group(0)
                    })
    
    # 如果正则匹配失败，尝试简单的分割方法
    if not found_checkboxes:
        found_checkboxes = extract_checkboxes_by_splitting(text)
    
    return found_checkboxes

def extract_checkboxes_by_splitting(text: str) -> List[Dict[str, Any]]:
    """通过分割方法提取复选框（备用方法）"""
    checkbox_chars = ["□", "☐", "☑", "■", "✓", "✔", "✗", "✘", "×", "☒", "☓"]
    found_checkboxes = []
    
    # 按常见分隔符分割
    separators = ['/', '、', '，', ',', '；', ';', '\n']
    parts = [text]
    
    for sep in separators:
        new_parts = []
        for part in parts:
            new_parts.extend(part.split(sep))
        parts = new_parts
    
    # 检查每个部分是否包含复选框
    for part in parts:
        part = part.strip()
        if not part:
            continue
            
        for char in checkbox_chars:
            if char in part:
                # 提取复选框文本
                clean_text = part.replace(char, '').strip()
                # 移除括号内容（如果有的话）
                clean_text = re.sub(r'\([^)]*\)', '', clean_text).strip()
                
                if clean_text and len(clean_text) > 0:
                    found_checkboxes.append({
                        'text': clean_text,
                        'char': char,
                        'position': text.find(part),
                        'full_match': part
                    })
                break
    
    return found_checkboxes

def clean_checkbox_text(text: str) -> str:
    """清理复选框文本"""
    if not text:
        return ""
    
    # 移除前后空白
    text = text.strip()
    
    # 移除常见的前缀符号
    text = re.sub(r'^[/\\、，,；;：:]+', '', text)
    text = re.sub(r'[/\\、，,；;：:]+$', '', text)
    
    # 移除多余的空白
    text = re.sub(r'\s+', ' ', text)
    
    # 移除空的括号
    text = re.sub(r'\(\s*\)', '', text)
    
    return text.strip()

if __name__ == "__main__":
    test_checkbox_extraction()
