/* 批量处理页面样式 */
:root {
    /* 主色调 */
    --primary-color: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #3f37c9;
    
    /* 辅助色调 */
    --secondary-color: #4cc9f0;
    --accent-color: #560bad;
    
    /* 功能色调 */
    --success-color: #06d6a0;
    --warning-color: #ffd166;
    --danger-color: #ef476f;
    --info-color: #118ab2;
    
    /* 中性色调 */
    --dark-color: #1a1a2e;
    --gray-dark: #2d3748;
    --gray: #4a5568;
    --gray-light: #a0aec0;
    --light-color: #f8f9fa;
    
    /* 布局变量 */
    --border-radius-sm: 4px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --box-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --box-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: #f5f7fa;
    color: var(--gray-dark);
    line-height: 1.6;
}

/* 容器样式 */
.batch-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 导航栏样式 */
.batch-nav {
    background-color: white;
    box-shadow: var(--box-shadow-sm);
    padding: 0.8rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.batch-logo {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

.batch-logo i {
    font-size: 1.5rem;
}

.batch-nav-links {
    display: flex;
    gap: 1rem;
}

.batch-nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: var(--gray);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.batch-nav-link:hover {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.batch-nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

/* 主要内容区域 */
.batch-main {
    flex: 1;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* 页面标题 */
.batch-header {
    margin-bottom: 2rem;
    text-align: center;
}

.batch-header h1 {
    font-size: 2rem;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.batch-header p {
    color: var(--gray);
    font-size: 1.1rem;
}

/* 上传区域 */
.batch-upload-section {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
}

#batch-drop-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    border: 2px dashed var(--primary-light);
    border-radius: var(--border-radius);
    background-color: rgba(67, 97, 238, 0.05);
    transition: var(--transition);
    margin-bottom: 1.5rem;
}

#batch-drop-area.highlight {
    background-color: rgba(67, 97, 238, 0.1);
    border-color: var(--primary-color);
}

#batch-drop-area i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

#batch-drop-area p {
    margin: 0.5rem 0;
    color: var(--gray);
}

.batch-file-label {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    margin: 1rem 0;
    font-weight: 500;
}

.batch-file-label:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

#batch-file-info {
    font-size: 0.9rem;
    color: var(--gray-light);
}

/* 选项区域 */
.batch-options {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.batch-option {
    flex: 1;
    min-width: 200px;
}

.batch-option label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.batch-option input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.batch-option span {
    font-size: 1rem;
    color: var(--gray-dark);
}

/* OCR引擎选择框样式 */
.batch-select {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-light);
    background-color: white;
    color: var(--gray-dark);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    outline: none;
    min-width: 180px;
}

.batch-select:hover, .batch-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
}

.batch-select option {
    padding: 0.5rem;
}

/* 提交按钮 */
.batch-submit-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1.5rem;
    background-color: var(--success-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.batch-submit-btn:hover {
    background-color: #05b589;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

/* 文件列表和进度区域 */
.batch-files-section {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

@media (min-width: 992px) {
    .batch-files-section {
        grid-template-columns: 1fr 2fr;
    }
}

/* 进度区域 */
.batch-progress-section {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
}

.batch-progress-section h2 {
    font-size: 1.5rem;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.batch-progress-container {
    position: relative;
    height: 12px;
    background-color: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.batch-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.5s ease;
    border-radius: 6px;
}

#batch-progress-text {
    position: absolute;
    top: -20px;
    right: 0;
    font-weight: 600;
    color: var(--primary-color);
}

.batch-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.batch-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.batch-stat:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-sm);
}

.batch-stat i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.batch-stat:nth-child(1) i {
    color: var(--info-color);
}

.batch-stat:nth-child(2) i {
    color: var(--success-color);
}

.batch-stat:nth-child(3) i {
    color: var(--primary-color);
}

.batch-stat:nth-child(4) i {
    color: var(--warning-color);
}

.batch-stat:nth-child(5) i {
    color: var(--danger-color);
}

.batch-stat span {
    font-size: 0.9rem;
    color: var(--gray);
}

.batch-stat strong {
    font-size: 1.2rem;
    color: var(--dark-color);
}

/* 文件列表区域 */
.batch-files-list {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
}

.batch-files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.batch-files-header h2 {
    font-size: 1.5rem;
    color: var(--dark-color);
}

.batch-download-all-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.batch-download-all-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.batch-download-all-btn:disabled {
    background-color: var(--gray-light);
    cursor: not-allowed;
    transform: none;
}

.batch-table-container {
    overflow-x: auto;
}

.batch-table {
    width: 100%;
    border-collapse: collapse;
}

.batch-table th,
.batch-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.batch-table th {
    font-weight: 600;
    color: var(--gray-dark);
    background-color: #f8f9fa;
}

.batch-table tr:hover {
    background-color: rgba(67, 97, 238, 0.05);
}

/* 状态标签 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-pending {
    background-color: var(--warning-color);
    color: #806000;
}

.status-processing {
    background-color: var(--primary-light);
    color: white;
}

.status-completed {
    background-color: var(--success-color);
    color: white;
}

.status-failed {
    background-color: var(--danger-color);
    color: white;
}

/* 文件进度条 */
.file-progress-container {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.file-progress-container > div:first-child {
    font-size: 0.8rem;
    color: var(--gray);
    text-align: right;
}

.file-progress-bar {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.file-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0%;
    transition: width 0.5s ease;
}

/* 文件操作按钮 */
.file-actions {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.file-action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    background-color: #f8f9fa;
    color: var(--gray-dark);
    cursor: pointer;
    transition: var(--transition);
}

.file-action-btn:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-sm);
}

.file-action-btn.download {
    background-color: var(--success-color);
    color: white;
}

.file-action-btn.download:hover {
    background-color: #05b589;
}

.file-action-btn:disabled {
    background-color: #e9ecef;
    color: var(--gray-light);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 页脚 */
.batch-footer {
    background-color: white;
    padding: 1.5rem;
    text-align: center;
    color: var(--gray);
    font-size: 0.9rem;
    margin-top: 2rem;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .batch-main {
        padding: 1.5rem;
    }
    
    .batch-nav {
        padding: 0.8rem 1rem;
    }
    
    .batch-nav-link span {
        display: none;
    }
    
    .batch-nav-link i {
        font-size: 1.2rem;
    }
    
    .batch-upload-section,
    .batch-progress-section,
    .batch-files-list {
        padding: 1rem;
    }
    
    #batch-drop-area {
        padding: 2rem 1rem;
    }
    
    .batch-table th:nth-child(4),
    .batch-table td:nth-child(4) {
        display: none;
    }
}

@media (max-width: 576px) {
    .batch-main {
        padding: 1rem;
    }
    
    .batch-header h1 {
        font-size: 1.5rem;
    }
    
    .batch-header p {
        font-size: 1rem;
    }
    
    .batch-options {
        flex-direction: column;
        gap: 1rem;
    }
    
    .batch-stat {
        padding: 0.75rem;
    }
    
    .batch-table th:nth-child(1),
    .batch-table td:nth-child(1) {
        display: none;
    }
    
    .batch-files-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .batch-download-all-btn {
        width: 100%;
        justify-content: center;
    }
} 