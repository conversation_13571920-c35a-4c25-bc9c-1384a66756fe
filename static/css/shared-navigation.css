/* 共享的侧边导航样式 */
/* 用于touchscreen和calculator页面的一致性浮动菜单 */

/* 侧边导航样式 - 触摸屏优化 */
.side-navigation {
    position: fixed;
    top: 0;
    left: 0;
    width: 200px;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 50px 0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 1000;
}

.nav-buttons {
    display: flex;
    flex-direction: column;
    gap: 40px;
    width: 100%;
    align-items: center;
}

.nav-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 140px;
    height: 140px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-sizing: border-box;

    /* 触摸优化 */
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    user-select: none;
}

.nav-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.nav-button:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.nav-button.active {
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.4);
}

.nav-button.active:hover {
    background: rgba(255, 255, 255, 0.35);
}

.nav-button i {
    font-size: 40px;
    margin-bottom: 10px;
    display: block;
}

.nav-button span {
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

/* 响应式设计 - 平板 */
@media (max-width: 768px) {
    body {
        display: flex;
        flex-direction: column;
    }

    .side-navigation {
        position: relative;
        top: auto;
        left: auto;
        width: 100%;
        height: auto;
        padding: 15px 0;
        border-right: none;
        border-bottom: 3px solid #4a90e2;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .nav-buttons {
        flex-direction: row;
        justify-content: center;
        gap: 20px;
        padding: 0 15px;
    }

    .nav-button {
        /* 平板固定尺寸 */
        width: 120px;
        height: 80px;
        padding: 10px 15px;
        font-size: 1em;
        box-sizing: border-box;
    }

    .nav-button i {
        font-size: 2em;
        margin-bottom: 5px;
    }

    .nav-button span {
        font-size: 0.9em;
    }

    .container {
        margin: 0 30px 30px 30px;
    }
}

/* 响应式设计 - 手机 */
@media (max-width: 480px) {
    .side-navigation {
        padding: 10px 0;
    }

    .nav-buttons {
        gap: 10px;
        padding: 0 10px;
    }

    .nav-button {
        /* 手机固定尺寸 */
        width: 100px;
        height: 70px;
        padding: 8px 10px;
        font-size: 0.9em;
        box-sizing: border-box;
    }

    .nav-button i {
        font-size: 1.8em;
        margin-bottom: 3px;
    }

    .nav-button span {
        font-size: 0.8em;
    }

    .container {
        margin: 0 15px 15px 15px;
    }
}
