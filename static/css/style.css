/* 全局样式 */
:root {
    /* 主色调 */
    --primary-color: #4361ee;
    --primary-light: #4895ef;
    --primary-dark: #3f37c9;
    
    /* 辅助色调 */
    --secondary-color: #4cc9f0;
    --accent-color: #560bad;
    
    /* 功能色调 */
    --success-color: #06d6a0;
    --warning-color: #ffd166;
    --danger-color: #ef476f;
    --info-color: #118ab2;
    
    /* 中性色调 */
    --dark-color: #1a1a2e;
    --gray-dark: #2d3748;
    --gray: #4a5568;
    --gray-light: #a0aec0;
    --light-color: #f8f9fa;
    
    /* 布局变量 */
    --border-radius-sm: 4px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --box-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --box-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-dark);
    background-color: #f5f7fa;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 导航栏样式 */
.main-nav {
    background-color: white;
    box-shadow: var(--box-shadow-sm);
    padding: 0.8rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

.nav-logo i {
    font-size: 1.5rem;
}

.nav-links {
    display: flex;
    gap: 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: var(--gray);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.nav-link:hover {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

/* 头部样式 */
header {
    text-align: center;
    margin: 2rem 0;
    padding: 0 1rem;
}

header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

header p {
    font-size: 1.1rem;
    color: var(--gray);
}

/* 主要内容区域 */
main {
    flex: 1;
    max-width: 1000px;
    width: 100%;
    margin: 0 auto;
    padding: 0 1rem 2rem;
}

/* 文件上传区域 */
.upload-container {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
}

.file-drop-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    border: 2px dashed var(--primary-light);
    border-radius: var(--border-radius);
    background-color: rgba(67, 97, 238, 0.05);
    transition: var(--transition);
    margin-bottom: 1.5rem;
}

.file-drop-area.highlight {
    background-color: rgba(67, 97, 238, 0.1);
    border-color: var(--primary-color);
}

.drop-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.file-drop-area p {
    margin: 0.5rem 0;
    color: var(--gray);
}

.file-input-label {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    margin: 1rem 0;
    font-weight: 500;
}

.file-input-label:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

#file-input {
    display: none;
}

.file-info {
    font-size: 0.9rem;
    color: var(--gray-light);
}

/* 选项区域 */
.options {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.option-group {
    flex: 1;
    min-width: 200px;
}

.option-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--gray-dark);
}

.option-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    background-color: white;
    font-size: 1rem;
    color: var(--gray-dark);
    transition: var(--transition);
}

.option-group select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
}

/* 模板选择容器的过渡效果 */
#manual-template-container {
    transition: all 0.3s ease;
    opacity: 1;
    max-height: 100px;
    overflow: hidden;
}

#manual-template-container[style*="display: none"] {
    opacity: 0;
    max-height: 0;
}

/* 提交按钮 */
.submit-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1.5rem;
    background-color: var(--success-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.submit-btn:hover {
    background-color: #05b589;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.submit-btn:disabled {
    background-color: var(--gray-light);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 进度条 */
.progress-container {
    margin-top: 1.5rem;
}

.progress-bar {
    position: relative;
    height: 12px;
    background-color: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    width: 0;
    transition: width 0.5s ease;
}

.progress.error {
    background: linear-gradient(90deg, var(--danger-color), #ff758f);
}

#progress-text {
    text-align: center;
    font-size: 0.9rem;
    color: var(--gray);
    margin-top: 0.5rem;
}

/* 状态信息样式 */
.status-info {
    margin-top: 1.5rem;
    padding: 1rem;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    box-shadow: var(--box-shadow-sm);
}

.status-info p {
    margin: 0;
    font-size: 0.95rem;
    color: var(--gray-dark);
}

#status-stage {
    font-weight: bold;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

/* 状态变化动画 */
.status-changed {
    animation: highlight-status 1s ease;
}

@keyframes highlight-status {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); color: var(--accent-color); }
    100% { transform: scale(1); }
}

/* 结果页面样式 */
.result-container {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
}

.result-header {
    margin-bottom: 2rem;
    text-align: center;
}

.error-message {
    background-color: rgba(239, 71, 111, 0.1);
    color: var(--danger-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin: 1.5rem 0;
    border-left: 4px solid var(--danger-color);
}

.success-message {
    background-color: rgba(6, 214, 160, 0.1);
    color: var(--success-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin: 1.5rem 0;
    border-left: 4px solid var(--success-color);
}

.preview-container {
    margin-bottom: 2rem;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    background-color: var(--light-color);
}

.preview-item {
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    transition: var(--transition);
}

.preview-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--box-shadow);
}

.preview-item:last-child {
    margin-bottom: 0;
}

.preview-item strong {
    display: block;
    margin-bottom: 1rem;
    color: var(--dark-color);
    font-size: 1.1rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.download-section {
    text-align: center;
    margin: 2rem 0;
}

.download-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.download-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.back-section {
    text-align: center;
    margin: 1.5rem 0;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--gray-light);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.back-btn:hover {
    background-color: var(--gray);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

/* 页脚样式 */
footer {
    background-color: white;
    padding: 1.5rem;
    text-align: center;
    color: var(--gray);
    font-size: 0.9rem;
    margin-top: 2rem;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-nav {
        padding: 0.8rem 1rem;
    }
    
    .nav-link span {
        display: none;
    }
    
    .nav-link i {
        font-size: 1.2rem;
    }
    
    .upload-container,
    .result-container {
        padding: 1.5rem;
    }
    
    .file-drop-area {
        padding: 2rem 1rem;
    }
    
    .options {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 576px) {
    header h1 {
        font-size: 1.5rem;
    }
    
    header p {
        font-size: 1rem;
    }
    
    .upload-container,
    .result-container {
        padding: 1rem;
    }
    
    .preview-item {
        padding: 1rem;
    }
    
    .download-btn,
    .back-btn {
        width: 100%;
        justify-content: center;
    }
}

/* 文件列表样式 */
.file-list-container {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: var(--border-radius);
}

.file-list-container h3 {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--gray-dark);
}

.file-list-container h3 small {
    font-size: 0.8rem;
    color: var(--gray);
    font-weight: normal;
}

.sortable-file-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    cursor: move;
    transition: var(--transition);
}

.file-item:hover {
    background-color: #f1f5f9;
}

.file-item.sortable-ghost {
    opacity: 0.5;
    background-color: var(--primary-light);
    border-color: var(--primary-color);
}

.file-item-icon {
    margin-right: 0.75rem;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.file-item-name {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-item-actions {
    display: flex;
    gap: 0.5rem;
}

.file-item-actions button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--gray);
    transition: var(--transition);
    font-size: 1rem;
    padding: 0.25rem;
}

.file-item-actions button:hover {
    color: var(--danger-color);
}

.file-item-handle {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 0.5rem;
    color: var(--gray-light);
    cursor: grab;
}

.file-item-handle:active {
    cursor: grabbing;
}

/* 下载模板卡片 */
.template-download-container {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
}

.template-download-container h3 {
    color: var(--primary-color);
    margin-bottom: 0.8rem;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.template-download-container p {
    color: var(--gray);
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.template-select-group {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.template-select {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid var(--gray-light);
    border-radius: var(--border-radius);
    background-color: white;
    font-size: 1rem;
    color: var(--gray-dark);
    outline: none;
    transition: var(--transition);
}

.template-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
}

.download-template-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.download-template-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.download-template-btn:disabled {
    background-color: var(--gray-light);
    cursor: not-allowed;
    transform: none;
}

@media (max-width: 768px) {
    .template-select-group {
        flex-direction: column;
    }

    .download-template-btn {
        width: 100%;
    }
}

/* OCR文字确认弹窗样式 */
.confirmation-modal, .touchscreen-confirmation-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.confirmation-modal-content, .touchscreen-confirmation-modal-content {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease-out;
}

.touchscreen-confirmation-modal-content {
    max-width: 900px;
    font-size: 1.1rem;
}

.confirmation-modal-header, .touchscreen-confirmation-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.touchscreen-confirmation-modal-header {
    padding: 25px 30px;
}

.confirmation-modal-header h3, .touchscreen-confirmation-modal-header h2 {
    margin: 0;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.touchscreen-confirmation-modal-header h2 {
    font-size: 1.6rem;
}

.confirmation-close-btn, .touchscreen-confirmation-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.confirmation-close-btn:hover, .touchscreen-confirmation-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.confirmation-modal-body, .touchscreen-confirmation-modal-body {
    padding: 25px;
}

.touchscreen-confirmation-modal-body {
    padding: 30px;
}

.help-text {
    background: #e7f3ff;
    border-left: 4px solid #007bff;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 0 5px 5px 0;
}

.help-text p {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #007bff;
    font-weight: 500;
}

.text-preview-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
}

.text-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #dee2e6;
}

.text-preview-header h3, .text-preview-header h4 {
    margin: 0;
    color: #495057;
    font-size: 1.1rem;
}

.text-stats {
    font-size: 0.9rem;
    color: #6c757d;
}

.text-content-preview {
    background: white;
    border: 1px solid #ced4da;
    border-radius: 5px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #495057;
}

.confirmation-modal .form-control, .touchscreen-confirmation-modal .form-control {
    width: 100%;
    min-height: 200px;
    padding: 15px;
    border: 2px solid #ced4da;
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    resize: vertical;
    transition: border-color 0.3s;
    box-sizing: border-box;
}

.touchscreen-confirmation-modal .form-control {
    min-height: 250px;
    font-size: 16px;
}

.confirmation-modal .form-control:focus, .touchscreen-confirmation-modal .form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.confirmation-modal-footer, .touchscreen-confirmation-modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 15px;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 0 0 15px 15px;
}

.touchscreen-confirmation-modal-footer {
    padding: 25px 30px;
    gap: 20px;
}

.modal-loading {
    text-align: center;
    padding: 40px;
}

.modal-loading .spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border-left-color: #667eea;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.btn-large {
    padding: 15px 25px;
    font-size: 1.1rem;
    min-width: 140px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    border: 2px solid #6c757d;
    color: #6c757d;
}

.btn-outline:hover {
    background: #6c757d;
    color: white;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 弹窗响应式设计 */
@media (max-width: 768px) {
    .confirmation-modal-content, .touchscreen-confirmation-modal-content {
        width: 95%;
        margin: 10px;
        max-height: 95vh;
    }

    .confirmation-modal-footer, .touchscreen-confirmation-modal-footer {
        flex-direction: column;
        gap: 10px;
    }

    .confirmation-modal-footer .btn, .touchscreen-confirmation-modal-footer .btn {
        width: 100%;
        justify-content: center;
    }

    .text-preview-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
