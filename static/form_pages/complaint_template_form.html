<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>complaint_template.docx - 在线编辑</title>
    <style>

/* 文档表单样式 */
.document-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}



/* 文档结构表单样式 */
.document-form-section {
  margin-top: 20px;
}

.form-title {
  background: #f5f5f5;
  margin: 0 0 20px 0;
  padding: 15px 20px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.document-content {
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 30px;
  font-family: "Microsoft YaHei", "SimSun", serif;
  line-height: 1.8;
  font-size: 14px;
}

/* 文档段落样式 */
.document-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin: 20px 0;
  line-height: 1.5;
}

.document-paragraph {
  margin: 15px 0;
  text-indent: 2em;
  line-height: 1.8;
}

/* 文档表格样式 */
.document-table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
  border: 1px solid #000;
}

.document-table td {
  border: 1px solid #000;
  padding: 8px 12px;
  vertical-align: top;
  line-height: 1.6;
}

/* 多行输入框样式 */

.inline-textarea, .cell-textarea {
  border: 1px solid #007bff;
  border-radius: 3px;
  padding: 4px 6px;
  font-size: inherit;
  font-family: inherit;
  color: #007bff;
  font-weight: 500;
  resize: both;
  min-width: 120px;
  min-height: 24px;
  background: rgba(0, 123, 255, 0.02);
  line-height: 1.4;
  overflow: hidden;
  transition: border-color 0.3s, background-color 0.3s;
}

.inline-textarea:focus, .cell-textarea:focus {
  outline: none;
  border-color: #0056b3;
  background: rgba(0, 123, 255, 0.05);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

/* 自动调整高度的样式 */
.auto-resize {
  overflow-y: hidden;
  resize: both;
}

/* 段落中的多行输入框 */
.document-paragraph .inline-textarea {
  min-width: 150px;
  max-width: calc(100% - 20px);
  width: auto;
}

/* 表格中的多行输入框 */
.document-table .cell-textarea {
  width: 100%;
  min-width: 80px;
  max-width: 100%;
  box-sizing: border-box;
}

/* 表格单元格样式调整 */
.document-table td {
  position: relative;
  overflow: visible;
}

/* 防止输入框超出表格边界 */
.document-table .cell-textarea {
  max-width: calc(100% - 8px);
}



.inline-checkbox, .cell-checkbox {
  margin: 0 3px;
  transform: scale(1.2);
  accent-color: #007bff;
}



.form-section {
  margin-bottom: 30px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
}

.section-title {
  background: #f5f5f5;
  margin: 0;
  padding: 15px 20px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e0e0e0;
}

.section-content {
  padding: 20px;
}

.form-field {
  margin-bottom: 20px;
}

.field-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #e74c3c;
}

input[type="text"],
input[type="number"],
input[type="date"],
input[type="tel"],
input[type="email"],
select,
textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

textarea {
  resize: vertical;
  min-height: 100px;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-wrapper input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkbox-label {
  margin: 0;
  cursor: pointer;
}

.form-actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
  text-align: center;
  display: flex;
  gap: 15px;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}

.btn-outline {
  background: transparent;
  color: #6c757d;
  border: 1px solid #6c757d;
}

.btn-outline:hover {
  background: #6c757d;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .document-form {
    margin: 10px;
    padding: 15px;
  }
  
  .section-content {
    padding: 15px;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">complaint_template.docx - 在线编辑</h1>
        <form id="documentForm" class="document-form">
<div class="document-form-section">
  <h3 class="form-title">文档编辑</h3>
  <div class="document-content">
    <div class="document-title">起诉状</div>
    <div class="document-title">原告：</div>
    <div class="document-title">被告：</div>
    <div class="document-title">诉讼请求：</div>
    <div class="document-title">事实与理由：</div>
    <div class="document-title">证据：</div>
    <div class="document-paragraph">日期：</div>
  </div>
</div>

<div class="form-actions">
  <button type="button" id="generateBtn" class="btn btn-primary">生成Word文档</button>
  <button type="reset" class="btn btn-outline">重置表单</button>
</div>
</form>
    </div>
    
    <script>

// 文档表单JavaScript
class DocumentFormHandler {
  constructor() {
    this.form = document.getElementById('documentForm');
    this.generateBtn = document.getElementById('generateBtn');

    this.init();
  }
  
  init() {
    // 绑定事件
    if (this.generateBtn) {
      this.generateBtn.addEventListener('click', () => this.generateDocument());
    }

    // 表单验证
    this.form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.generateDocument();
    });

    // 初始化自动调整高度的文本框
    this.initAutoResizeTextareas();
  }

  // 初始化自动调整高度的文本框
  initAutoResizeTextareas() {
    const textareas = this.form.querySelectorAll('textarea.auto-resize');

    textareas.forEach(textarea => {
      // 设置初始高度
      this.adjustTextareaHeight(textarea);

      // 监听输入事件
      textarea.addEventListener('input', () => {
        this.adjustTextareaHeight(textarea);
      });

      // 监听粘贴事件
      textarea.addEventListener('paste', () => {
        setTimeout(() => {
          this.adjustTextareaHeight(textarea);
        }, 10);
      });

      // 监听键盘事件
      textarea.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          setTimeout(() => {
            this.adjustTextareaHeight(textarea);
          }, 10);
        }
      });
    });
  }

  // 调整文本框高度
  adjustTextareaHeight(textarea) {
    // 重置高度以获取正确的scrollHeight
    textarea.style.height = 'auto';

    // 计算所需高度
    const minHeight = 24; // 最小高度
    const maxHeight = 200; // 最大高度
    const scrollHeight = textarea.scrollHeight;

    // 设置新高度
    const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
    textarea.style.height = newHeight + 'px';

    // 如果内容超过最大高度，显示滚动条
    if (scrollHeight > maxHeight) {
      textarea.style.overflowY = 'auto';
    } else {
      textarea.style.overflowY = 'hidden';
    }
  }
  
  // 收集表单数据
  collectFormData() {
    const data = {};

    // 收集所有多行输入框
    const textareas = this.form.querySelectorAll('textarea');
    textareas.forEach(textarea => {
      if (textarea.name) {
        data[textarea.name] = textarea.value;
      }
    });

    // 处理复选框
    const checkboxes = this.form.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
      data[checkbox.name] = checkbox.checked;
    });

    return data;
  }
  

  
  // 生成Word文档
  async generateDocument() {
    const data = this.collectFormData();
    
    // 验证必填字段
    if (!this.validateForm()) {
      return;
    }
    
    try {
      this.generateBtn.disabled = true;
      this.generateBtn.textContent = '生成中...';
      
      const response = await fetch('/api/generate-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          template_path: "/Users/<USER>/my_git/complaint-llm/static/templates/complaint_template.docx",
          form_data: data
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.download_url) {
          // 下载文件
          window.location.href = result.download_url;
        }
      } else {
        throw new Error('文档生成失败');
      }
    } catch (error) {
      alert('生成失败: ' + error.message);
    } finally {
      this.generateBtn.disabled = false;
      this.generateBtn.textContent = '生成Word文档';
    }
  }
  
  // 表单验证
  validateForm() {
    const requiredFields = this.form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        field.style.borderColor = '#e74c3c';
        isValid = false;
      } else {
        field.style.borderColor = '#ddd';
      }
    });
    
    if (!isValid) {
      alert('请填写所有必填字段');
    }
    
    return isValid;
  }
  

}

// 初始化表单处理器
document.addEventListener('DOMContentLoaded', () => {
  new DocumentFormHandler();
});
    </script>
</body>
</html>