// 批量处理页面的JavaScript代码
document.addEventListener('DOMContentLoaded', function() {
    // 元素引用
    const batchForm = document.getElementById('batch-upload-form');
    const fileInput = document.getElementById('batch-file-input');
    const dropArea = document.getElementById('batch-drop-area');
    const fileInfo = document.getElementById('batch-file-info');
    const filesContainer = document.getElementById('batch-files-container');
    const fileList = document.getElementById('file-list');
    const downloadAllBtn = document.getElementById('download-all-btn');
    const submitBtn = document.querySelector('.batch-submit-btn');
    
    // 统计元素
    const totalFilesEl = document.getElementById('total-files');
    const completedFilesEl = document.getElementById('completed-files');
    const processingFilesEl = document.getElementById('processing-files');
    const pendingFilesEl = document.getElementById('pending-files');
    const failedFilesEl = document.getElementById('failed-files');
    
    // 进度条元素
    const progressBar = document.getElementById('batch-progress-bar');
    const progressText = document.getElementById('batch-progress-text');
    
    // 任务状态
    let taskId = null;
    let files = [];
    let stats = {
        total: 0,
        completed: 0,
        processing: 0,
        pending: 0,
        failed: 0
    };
    
    // 轮询间隔（毫秒）
    const POLLING_INTERVAL = 2000;
    
    // 初始化显示任务列表容器
    filesContainer.style.display = 'grid';
    
    // 检查是否有正在进行的任务
    checkActiveTask();
    
    // 拖放功能
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        dropArea.classList.add('highlight');
    }
    
    function unhighlight() {
        dropArea.classList.remove('highlight');
    }
    
    // 处理文件拖放
    dropArea.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const droppedFiles = dt.files;
        
        // 创建一个新的FileList对象
        const dataTransfer = new DataTransfer();
        
        // 如果已经有选择的文件，先添加它们
        if (fileInput.files.length > 0) {
            Array.from(fileInput.files).forEach(file => {
                dataTransfer.items.add(file);
            });
        }
        
        // 添加新拖放的文件
        Array.from(droppedFiles).forEach(file => {
            dataTransfer.items.add(file);
        });
        
        // 更新文件输入
        fileInput.files = dataTransfer.files;
        updateFileInfo();
        updateFileList();
    }
    
    // 文件选择变化时更新信息
    fileInput.addEventListener('change', function() {
        updateFileInfo();
        updateFileList();
    });
    
    function updateFileInfo() {
        if (fileInput.files.length > 0) {
            fileInfo.textContent = `已选择 ${fileInput.files.length} 个文件`;
        } else {
            fileInfo.textContent = '支持的格式：JPG, PNG, PDF, DOC, DOCX';
        }
    }
    
    // 更新文件列表显示
    function updateFileList() {
        if (fileInput.files.length > 0) {
            // 清空文件列表
            fileList.innerHTML = '';
            
            // 重置统计信息
            stats = {
                total: fileInput.files.length,
                completed: 0,
                processing: 0,
                pending: fileInput.files.length,
                failed: 0
            };
            updateStats();
            
            // 添加文件到列表
            files = Array.from(fileInput.files).map((file, index) => {
                return {
                    id: index + 1,
                    name: file.name,
                    status: 'pending',
                    progress: 0,
                    downloadUrl: null
                };
            });
            
            // 渲染文件列表
            renderFileList();
        } else {
            // 清空文件列表但保持容器可见
            fileList.innerHTML = '';
            stats = {
                total: 0,
                completed: 0,
                processing: 0,
                pending: 0,
                failed: 0
            };
            updateStats();
            updateProgressBar();
        }
    }
    
    // 表单提交处理
    batchForm.addEventListener('submit', handleSubmit);
    
    function handleSubmit(e) {
        e.preventDefault();
        
        if (fileInput.files.length === 0) {
            alert('请选择至少一个文件');
            return;
        }
        
        // 禁用提交按钮和上传区域
        disableUploadArea();
        
        // 创建FormData对象
        const formData = new FormData();
        
        // 添加文件
        Array.from(fileInput.files).forEach(file => {
            formData.append('files[]', file);
        });
        
        // 添加OCR引擎选项
        const ocrEngineSelect = document.getElementById('batch-ocr-engine');
        if (ocrEngineSelect) {
            formData.append('ocr_engine', ocrEngineSelect.value);
        }
        
        // 更新所有文件状态为等待中
        files.forEach(file => {
            file.status = 'pending';
            file.progress = 0;
            file.downloadUrl = null;
        });
        
        // 更新统计信息
        stats = {
            total: files.length,
            completed: 0,
            processing: 0,
            pending: files.length,
            failed: 0
        };
        updateStats();
        renderFileList();
        updateProgressBar();
        
        // 发送请求
        fetch('/upload-batch', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 保存任务ID
                taskId = data.task_id;
                
                // 开始轮询任务状态
                startPolling();
            } else {
                alert('上传失败: ' + data.error);
                enableUploadArea();
            }
        })
        .catch(error => {
            console.error('上传错误:', error);
            alert('上传出错，请重试');
            enableUploadArea();
        });
    }
    
    // 禁用上传区域
    function disableUploadArea() {
        submitBtn.disabled = true;
        fileInput.disabled = true;
        dropArea.classList.add('disabled');
        
        // 修改按钮文本为"处理中"
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中';
        
        // 添加禁用样式
        const style = document.createElement('style');
        style.id = 'disabled-upload-style';
        style.textContent = `
            #batch-drop-area.disabled {
                opacity: 0.6;
                cursor: not-allowed;
                pointer-events: none;
            }
            .batch-submit-btn:disabled {
                background-color: #cccccc !important;
                cursor: not-allowed !important;
                transform: none !important;
            }
        `;
        document.head.appendChild(style);
    }
    
    // 启用上传区域
    function enableUploadArea() {
        submitBtn.disabled = false;
        fileInput.disabled = false;
        dropArea.classList.remove('disabled');
        
        // 恢复按钮文本
        submitBtn.innerHTML = '<i class="fas fa-play"></i> 开始处理';
        
        // 移除禁用样式
        const disabledStyle = document.getElementById('disabled-upload-style');
        if (disabledStyle) {
            document.head.removeChild(disabledStyle);
        }
    }
    
    // 检查是否有正在进行的任务
    function checkActiveTask() {
        // 检查本地存储中是否有任务ID
        const savedTaskId = localStorage.getItem('batch_task_id');
        if (savedTaskId) {
            // 尝试获取任务状态
            fetch(`/task-status/${savedTaskId}`)
                .then(response => response.json())
                .then(data => {
                    if (data && (data.status === 'processing' || data.status === 'pending')) {
                        // 有正在进行的任务，禁用上传区域
                        taskId = savedTaskId;
                        disableUploadArea();
                        
                        // 恢复任务列表
                        restoreTaskList(data);
                        
                        // 更新任务状态
                        updateTaskStatus(data);
                        
                        // 开始轮询
                        startPolling();
                    } else {
                        // 任务已完成或不存在，清除本地存储
                        localStorage.removeItem('batch_task_id');
                    }
                })
                .catch(error => {
                    console.error('检查任务状态出错:', error);
                    localStorage.removeItem('batch_task_id');
                });
        }
    }
    
    // 恢复任务列表
    function restoreTaskList(taskData) {
        // 清空当前文件列表
        files = [];
        
        // 从任务数据中恢复文件列表
        if (taskData.results && taskData.results.length > 0) {
            // 添加已完成的文件
            taskData.results.forEach((result, index) => {
                files.push({
                    id: index + 1,
                    name: result.file,
                    status: result.success ? 'completed' : 'failed',
                    progress: 100,
                    downloadUrl: result.download_url
                });
            });
        }
        
        // 添加当前处理的文件
        if (taskData.current_file && !files.some(f => f.name === taskData.current_file)) {
            files.push({
                id: files.length + 1,
                name: taskData.current_file,
                status: 'processing',
                progress: taskData.current_progress || 50,
                downloadUrl: null
            });
        }
        
        // 添加待处理的文件（如果有）
        if (taskData.pending_files && taskData.pending_files.length > 0) {
            taskData.pending_files.forEach((fileName, index) => {
                if (!files.some(f => f.name === fileName)) {
                    files.push({
                        id: files.length + 1,
                        name: fileName,
                        status: 'pending',
                        progress: 0,
                        downloadUrl: null
                    });
                }
            });
        }
        
        // 更新统计信息
        stats = {
            total: files.length,
            completed: files.filter(f => f.status === 'completed').length,
            processing: files.filter(f => f.status === 'processing').length,
            pending: files.filter(f => f.status === 'pending').length,
            failed: files.filter(f => f.status === 'failed').length
        };
        
        // 更新UI
        updateStats();
        renderFileList();
        updateProgressBar();
        
        // 检查是否可以启用批量下载按钮
        downloadAllBtn.disabled = stats.completed === 0;
    }
    
    // 开始轮询任务状态
    function startPolling() {
        // 保存任务ID到本地存储
        if (taskId) {
            localStorage.setItem('batch_task_id', taskId);
        }
        
        // 立即执行一次
        pollTaskStatus();
        
        // 设置定时器
        const intervalId = setInterval(() => {
            pollTaskStatus().then(isCompleted => {
                if (isCompleted) {
                    clearInterval(intervalId);
                    // 任务完成，启用上传区域
                    enableUploadArea();
                    // 清除本地存储中的任务ID
                    localStorage.removeItem('batch_task_id');
                }
            });
        }, POLLING_INTERVAL);
    }
    
    // 轮询任务状态
    async function pollTaskStatus() {
        if (!taskId) return true;
        
        try {
            const response = await fetch(`/task-status/${taskId}`);
            const data = await response.json();
            
            // 更新任务状态
            updateTaskStatus(data);
            
            // 检查任务是否完成
            const isCompleted = data.status === 'completed' || data.status === 'failed';
            
            // 如果任务失败，显示错误信息
            if (data.status === 'failed') {
                alert('批量处理任务失败: ' + (data.error || '未知错误'));
                enableUploadArea();
            }
            
            return isCompleted;
        } catch (error) {
            console.error('获取任务状态出错:', error);
            return false;
        }
    }
    
    // 更新任务状态
    function updateTaskStatus(taskData) {
        console.log("更新任务状态:", taskData);
        
        // 更新文件状态
        if (taskData.results && taskData.results.length > 0) {
            taskData.results.forEach(result => {
                const fileIndex = files.findIndex(f => f.name === result.file);
                if (fileIndex !== -1) {
                    files[fileIndex].status = result.success ? 'completed' : 'failed';
                    files[fileIndex].progress = 100;
                    files[fileIndex].downloadUrl = result.download_url;
                    console.log(`文件 ${result.file} 的下载URL: ${result.download_url}`);
                }
            });
        }
        
        // 更新当前处理的文件
        if (taskData.current_file) {
            const fileIndex = files.findIndex(f => f.name === taskData.current_file);
            if (fileIndex !== -1) {
                files[fileIndex].status = 'processing';
                files[fileIndex].progress = taskData.current_progress || 50;
            }
        }
        
        // 计算统计信息
        stats.completed = files.filter(f => f.status === 'completed').length;
        stats.failed = files.filter(f => f.status === 'failed').length;
        stats.processing = files.filter(f => f.status === 'processing').length;
        stats.pending = files.filter(f => f.status === 'pending').length;
        
        // 更新UI
        updateStats();
        renderFileList();
        updateProgressBar();
        
        // 检查是否可以启用批量下载按钮
        downloadAllBtn.disabled = stats.completed === 0;
    }
    
    // 更新统计信息
    function updateStats() {
        totalFilesEl.textContent = stats.total;
        completedFilesEl.textContent = stats.completed;
        processingFilesEl.textContent = stats.processing;
        pendingFilesEl.textContent = stats.pending;
        failedFilesEl.textContent = stats.failed;
    }
    
    // 更新总进度条
    function updateProgressBar() {
        const progress = Math.round((stats.completed + stats.failed) / stats.total * 100) || 0;
        progressBar.style.width = `${progress}%`;
        progressText.textContent = `${progress}%`;
    }
    
    // 渲染文件列表
    function renderFileList() {
        fileList.innerHTML = '';
        
        if (files.length === 0) {
            // 如果没有文件，显示提示信息
            const row = document.createElement('tr');
            const cell = document.createElement('td');
            cell.colSpan = 5;
            cell.textContent = '暂无任务，请上传文件开始处理';
            cell.style.textAlign = 'center';
            cell.style.padding = '2rem';
            cell.style.color = 'var(--gray)';
            row.appendChild(cell);
            fileList.appendChild(row);
            return;
        }
        
        files.forEach(file => {
            const row = document.createElement('tr');
            
            // 序号列
            const idCell = document.createElement('td');
            idCell.textContent = file.id;
            row.appendChild(idCell);
            
            // 文件名列
            const nameCell = document.createElement('td');
            nameCell.textContent = file.name;
            row.appendChild(nameCell);
            
            // 状态列
            const statusCell = document.createElement('td');
            const statusBadge = document.createElement('span');
            statusBadge.className = `status-badge status-${file.status}`;
            
            let statusIcon = '';
            let statusText = '';
            
            switch (file.status) {
                case 'pending':
                    statusIcon = '<i class="fas fa-clock"></i>';
                    statusText = '等待中';
                    break;
                case 'processing':
                    statusIcon = '<i class="fas fa-spinner fa-spin"></i>';
                    statusText = '处理中';
                    break;
                case 'completed':
                    statusIcon = '<i class="fas fa-check"></i>';
                    statusText = '已完成';
                    break;
                case 'failed':
                    statusIcon = '<i class="fas fa-times"></i>';
                    statusText = '失败';
                    break;
            }
            
            statusBadge.innerHTML = `${statusIcon} ${statusText}`;
            statusCell.appendChild(statusBadge);
            row.appendChild(statusCell);
            
            // 进度列
            const progressCell = document.createElement('td');
            const progressContainer = document.createElement('div');
            progressContainer.className = 'file-progress-container';
            
            const progressValue = document.createElement('div');
            progressValue.textContent = `${file.progress}%`;
            progressContainer.appendChild(progressValue);
            
            const progressBar = document.createElement('div');
            progressBar.className = 'file-progress-bar';
            
            const progress = document.createElement('div');
            progress.className = 'file-progress';
            progress.style.width = `${file.progress}%`;
            
            progressBar.appendChild(progress);
            progressContainer.appendChild(progressBar);
            progressCell.appendChild(progressContainer);
            row.appendChild(progressCell);
            
            // 操作列
            const actionsCell = document.createElement('td');
            const actionsContainer = document.createElement('div');
            actionsContainer.className = 'file-actions';
            
            // 下载按钮
            const downloadBtn = document.createElement('button');
            downloadBtn.className = 'file-action-btn download';
            downloadBtn.innerHTML = '<i class="fas fa-download"></i>';
            downloadBtn.title = '下载';
            downloadBtn.disabled = !file.downloadUrl;
            
            if (file.downloadUrl) {
                console.log(`为文件 ${file.name} 设置下载链接: ${file.downloadUrl}`);
                downloadBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log(`点击下载文件: ${file.name}, URL: ${file.downloadUrl}`);
                    
                    // 创建一个隐藏的a标签来下载文件
                    const downloadLink = document.createElement('a');
                    downloadLink.href = file.downloadUrl;
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    document.body.removeChild(downloadLink);
                });
            }
            
            actionsContainer.appendChild(downloadBtn);
            actionsCell.appendChild(actionsContainer);
            row.appendChild(actionsCell);
            
            fileList.appendChild(row);
        });
    }
    
    // 批量下载按钮点击事件
    downloadAllBtn.addEventListener('click', handleBatchDownload);
    
    function handleBatchDownload() {
        // 获取所有已完成的文件的下载链接
        const downloadUrls = files
            .filter(file => file.status === 'completed' && file.downloadUrl)
            .map(file => file.downloadUrl);
        
        console.log("批量下载URLs:", downloadUrls);
        
        if (downloadUrls.length === 0) {
            alert('没有可下载的文件');
            return;
        }
        
        // 请求批量下载
        fetch('/download-batch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ urls: downloadUrls })
        })
        .then(response => response.json())
        .then(data => {
            console.log("批量下载响应:", data);
            if (data.success && data.download_url) {
                // 创建一个隐藏的a标签来下载文件
                const downloadLink = document.createElement('a');
                downloadLink.href = data.download_url;
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            } else {
                alert('批量下载失败: ' + (data.error || '未知错误'));
            }
        })
        .catch(error => {
            console.error('批量下载错误:', error);
            alert('批量下载出错，请重试');
        });
    }
}); 