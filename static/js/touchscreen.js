/* static/js/touchscreen.js */
/* 本文件包含触摸屏界面的交互逻辑 */

document.addEventListener('DOMContentLoaded', function() {
    // 禁止右键菜单
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault(); // 阻止默认右键菜单
        return false; // 确保在所有浏览器中都能正常工作
    });

    // 自定义通知弹窗函数
    window.showNotification = function(title, message, autoClose = true, autoCloseDelay = 3000, customButtons = null) {
        // 创建遮罩层
        const overlay = document.createElement('div');
        overlay.className = 'notification-overlay';

        // 创建通知容器
        const notification = document.createElement('div');
        notification.className = 'notification';

        // 创建通知头部
        const header = document.createElement('div');
        header.className = 'notification-header';

        // 创建标题
        const titleEl = document.createElement('h3');
        titleEl.innerHTML = `<i class="fas fa-info-circle"></i> ${title}`;

        // 添加标题到头部
        header.appendChild(titleEl);

        // 创建通知内容
        const body = document.createElement('div');
        body.className = 'notification-body';
        body.textContent = message;

        // 创建通知底部
        const footer = document.createElement('div');
        footer.className = 'notification-footer';

        // 关闭通知的函数
        function closeNotification() {
            if (overlay.parentNode) {
                document.body.removeChild(overlay);
            }
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }

        // 如果有自定义按钮，使用自定义按钮
        if (customButtons && Array.isArray(customButtons)) {
            customButtons.forEach(buttonConfig => {
                const btn = document.createElement('button');
                btn.className = buttonConfig.className || 'notification-btn';
                btn.innerHTML = buttonConfig.text;
                btn.addEventListener('click', function() {
                    if (buttonConfig.onClick) {
                        buttonConfig.onClick();
                    }
                    if (buttonConfig.closeOnClick !== false) {
                        closeNotification();
                    }
                });
                footer.appendChild(btn);
            });
        } else {
            // 创建默认确认按钮
            const confirmBtn = document.createElement('button');
            confirmBtn.className = 'notification-btn';
            confirmBtn.textContent = '确定';
            confirmBtn.addEventListener('click', function() {
                closeNotification();
            });
            footer.appendChild(confirmBtn);
        }

        // 组装通知
        notification.appendChild(header);
        notification.appendChild(body);
        notification.appendChild(footer);

        // 添加到页面
        document.body.appendChild(overlay);
        document.body.appendChild(notification);

        // 如果设置了自动关闭，则在指定时间后自动关闭
        if (autoClose) {
            setTimeout(closeNotification, autoCloseDelay);
        }

        // 返回关闭函数，以便外部可以手动关闭
        return closeNotification;
    };

    // 页面加载时检查是否已有照片，并相应地显示或隐藏拍照和扫码按钮
    setTimeout(() => {
        if (files && files.length > 0) {
            toggleCameraButtons(false);
        } else {
            toggleCameraButtons(true);
        }
    }, 500); // 延迟一点执行，确保DOM已完全加载
    const fileInput = document.getElementById('file-input');
    const cameraPlaceholder = document.getElementById('camera-placeholder');
    const startCameraBtn = document.getElementById('start-camera-btn');
    const cameraView = document.getElementById('camera-view');
    const photoPreview = document.getElementById('photo-preview');
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const captureBtn = document.getElementById('capture-btn');
    const cancelCameraBtn = document.getElementById('cancel-camera-btn');
    const savePhotoBtn = document.getElementById('save-photo-btn');
    const retakeBtn = document.getElementById('retake-btn');
    const fileListContainer = document.getElementById('file-list-container');
    const fileList = document.getElementById('file-list');
    const fileInfo = document.getElementById('file-info');
    const submitBtn = document.getElementById('submit-btn');
    const uploadForm = document.getElementById('upload-form');
    const addMorePhotosBtn = document.getElementById('add-more-photos-btn');
    // 移除了分类模式选择，现在始终使用手动模式
    const templateSelect = document.getElementById('template-select');
    // 移除了旧的模板下载选择器
    const progressContainer = document.getElementById('progress-container');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const statusStage = document.getElementById('status-stage');

    // 二维码扫描相关元素
    const qrUploadBtn = document.getElementById('qr-upload-btn');
    const qrModal = document.getElementById('qr-modal');
    const closeBtn = document.querySelector('.close-btn');
    const sessionIdEl = document.getElementById('session-id');
    const connectionStatus = document.getElementById('connection-status');
    const qrCodeImage = document.getElementById('qr-code-image');
    const mobileFilesContainer = document.getElementById('mobile-files-container');
    const mobileFilesList = document.getElementById('mobile-files-list');

    // 现在始终使用手动模式，无需设置分类模式

    let files = []; // 用于存储文件对象和顺序
    let sortable;
    let stream = null; // 存储摄像头流
    let countdownTimer = null; // 用于存储倒计时定时器

    // --- 摄像头和拍照逻辑 ---

    // 图像增强函数
    function enhanceImage(context, width, height) {
        try {
            // 获取图像数据
            const imageData = context.getImageData(0, 0, width, height);
            const data = imageData.data;

            // 应用锐化和对比度增强
            for (let i = 0; i < data.length; i += 4) {
                // 增加对比度
                data[i] = Math.min(255, Math.max(0, (data[i] - 128) * 1.2 + 128)); // 红色通道
                data[i + 1] = Math.min(255, Math.max(0, (data[i + 1] - 128) * 1.2 + 128)); // 绿色通道
                data[i + 2] = Math.min(255, Math.max(0, (data[i + 2] - 128) * 1.2 + 128)); // 蓝色通道
            }

            // 将处理后的图像数据放回canvas
            context.putImageData(imageData, 0, 0);

            console.log('图像增强处理完成');
        } catch (error) {
            console.error('图像增强处理失败:', error);
        }
    }

    // 启动摄像头
    function startCamera() {
        // 显示摄像头视图，隐藏占位符
        cameraPlaceholder.style.display = 'none';
        cameraView.style.display = 'flex';
        photoPreview.style.display = 'none';

        // 确保容器高度一致，防止页面跳动
        const cameraArea = document.getElementById('camera-area');
        if (cameraArea) {
            cameraArea.style.minHeight = '500px'; // 增加高度以适应更大的视频和按钮
        }

        // 应用90度顺时针旋转到视频元素
        video.style.transform = 'rotate(90deg)';
        video.style.transformOrigin = 'center center';

        // 确保视频在旋转后正确显示，并调整尺寸
        video.style.maxWidth = '48%';  // 减少尺寸，比之前小1/4
        //video.style.maxHeight = '50vh'; // 高度设为屏幕的1/2
        video.style.objectFit = 'contain';
        video.style.margin = '0'; // 移除自动边距

        // 获取摄像头访问权限
        navigator.mediaDevices.getUserMedia({
            video: {
                facingMode: 'environment', // 默认使用后置摄像头
                width: { ideal: 3264 },    // 请求最高分辨率 (4K)
                height: { ideal: 2448 },   // 请求最高分辨率 (4K)
                aspectRatio: { ideal: 4/3 },          // 理想的宽高比
                frameRate: { ideal: 15},    // 帧率设置
                resizeMode: "none",                   // 不进行缩放处理
                advanced: [
                    { zoom: 1 },                      // 不进行缩放
                    { exposureMode: "manual" },       // 手动曝光模式
                    { focusMode: "continuous" }       // 连续对焦模式
                ]
            },
            audio: false
        })
        .then(function(mediaStream) {
            stream = mediaStream;
            video.srcObject = mediaStream;

            // 获取并记录摄像头的实际能力
            const videoTrack = mediaStream.getVideoTracks()[0];
            const capabilities = videoTrack.getCapabilities();
            const settings = videoTrack.getSettings();

            console.log('摄像头能力:', capabilities);
            console.log('当前摄像头设置:', settings);
            console.log(`实际分辨率: ${settings.width}x${settings.height}, 帧率: ${settings.frameRate}`);

            video.onloadedmetadata = function() {
                video.play();
            };
        })
        .catch(function(err) {
            console.error("摄像头访问失败:", err);
            showNotification('摄像头错误', '无法访问您的摄像头。请确认您已授予摄像头权限，或尝试使用其他浏览器。', false);
            // 恢复到默认视图
            resetCameraUI();
        });
    }

    // 停止摄像头
    function stopCamera() {
        if (stream) {
            stream.getTracks().forEach(track => {
                track.stop();
            });
            stream = null;
            video.srcObject = null;
        }
    }

    // 拍照
    function capturePhoto() {
        if (!stream) return;

        // 获取视频的实际分辨率
        const videoWidth = video.videoWidth;
        const videoHeight = video.videoHeight;

        console.log(`捕获照片，视频分辨率: ${videoWidth}x${videoHeight}`);

        // 由于旋转90度，交换宽度和高度
        canvas.width = videoHeight;
        canvas.height = videoWidth;

        // 在canvas上绘制当前视频帧，并应用90度顺时针旋转
        const context = canvas.getContext('2d');

        // 保存当前状态
        context.save();

        // 旋转90度顺时针（π/2弧度）
        context.translate(canvas.width/2, canvas.height/2);
        context.rotate(Math.PI/2);
        context.translate(-videoWidth/2, -videoHeight/2);

        // 绘制旋转后的图像
        context.drawImage(video, 0, 0, videoWidth, videoHeight);

        // 恢复状态
        context.restore();

        // 应用图像增强处理
        enhanceImage(context, canvas.width, canvas.height);

        // 显示预览界面
        cameraView.style.display = 'none';
        photoPreview.style.display = 'flex';

        // 保持容器高度一致
        const cameraArea = document.getElementById('camera-area');
        if (cameraArea) {
            cameraArea.style.minHeight = '500px'; // 保持与摄像头视图相同的高度
        }
    }

    // 保存照片到文件列表
    function savePhoto() {
        // 将Canvas转换为Blob
        canvas.toBlob(function(blob) {
            // 创建唯一文件名
            const fileName = `诉状照片_${new Date().toISOString().replace(/[:.]/g, '-')}.jpg`;

            // 创建File对象
            const file = new File([blob], fileName, { type: 'image/jpeg' });

            // 生成唯一ID并添加到文件列表
            const fileId = Date.now() + '-' + Math.random().toString(36).substring(2, 9);
            files.push({ id: fileId, file: file });

            // 更新界面
            renderFileList();
            updateFileInfo();

            // 重置摄像头UI
            resetCameraUI();

            // 如果是第一张照片，应该显示文件列表
            if (files.length === 1) {
                fileListContainer.style.display = 'block';
            }
        }, 'image/jpeg', 0.95); // 图片质量提高到95%，确保最高图像质量
    }

    // 重置摄像头UI到初始状态
    function resetCameraUI() {
        stopCamera();
        cameraPlaceholder.style.display = 'flex';
        cameraView.style.display = 'none';
        photoPreview.style.display = 'none';

        // 重置容器高度
        const cameraArea = document.getElementById('camera-area');
        if (cameraArea) {
            cameraArea.style.minHeight = '250px'; // 恢复默认高度
        }
    }

    // 事件监听 - 开始拍照
    if (startCameraBtn) {
        startCameraBtn.addEventListener('click', function() {
            startCamera();
        });
    }

    if (cameraPlaceholder) {
        cameraPlaceholder.addEventListener('click', function(e) {
            // 检查点击的元素是否是二维码按钮或其子元素
            if (e.target.closest('#qr-upload-btn')) {
                // 如果是二维码按钮，不启动摄像头
                return;
            }
            // 否则启动摄像头
            startCamera();
        });
    }

    // 事件监听 - 拍照按钮
    if (captureBtn) {
        captureBtn.addEventListener('click', function() {
            capturePhoto();
        });
    }

    // 事件监听 - 取消拍照
    if (cancelCameraBtn) {
        cancelCameraBtn.addEventListener('click', function() {
            resetCameraUI();
        });
    }

    // 事件监听 - 保存照片
    if (savePhotoBtn) {
        savePhotoBtn.addEventListener('click', function() {
            savePhoto();
        });
    }

    // 事件监听 - 重新拍照
    if (retakeBtn) {
        retakeBtn.addEventListener('click', function() {
            // 返回到摄像头视图
            photoPreview.style.display = 'none';
            startCamera(); // 重新启动摄像头
        });
    }

    // 事件监听 - 添加更多照片
    if (addMorePhotosBtn) {
        addMorePhotosBtn.addEventListener('click', function() {
            startCamera();
        });
    }

    // --- 文件列表管理 ---

    // 渲染文件列表
    function renderFileList() {
        fileList.innerHTML = ''; // 清空列表
        if (files.length > 0) {
            fileListContainer.style.display = 'block';

            // 当有照片时，隐藏拍照和扫码按钮
            toggleCameraButtons(false);

            files.forEach((fileData, index) => {
                const listItem = document.createElement('div');
                listItem.classList.add('file-item');
                listItem.setAttribute('data-id', fileData.id);

                // 创建照片预览容器
                const previewContainer = document.createElement('div');
                previewContainer.classList.add('file-preview');

                // 创建照片预览图
                const imgPreview = document.createElement('img');
                imgPreview.src = URL.createObjectURL(fileData.file);
                imgPreview.alt = fileData.file.name;
                imgPreview.onload = function() {
                    URL.revokeObjectURL(this.src); // 释放创建的URL
                };

                // 创建信息栏
                const infoBar = document.createElement('div');
                infoBar.classList.add('file-info');

                // 简化的文件名显示（只显示序号）
                const fileName = document.createElement('span');
                fileName.classList.add('file-name');
                fileName.textContent = `照片 ${index + 1}`;
                fileName.title = fileData.file.name; // 鼠标悬停显示完整名称

                const removeBtn = document.createElement('button');
                removeBtn.classList.add('remove-file-btn');
                removeBtn.innerHTML = '<i class="fas fa-times-circle"></i>';
                removeBtn.setAttribute('aria-label', `移除照片 ${index + 1}`);
                removeBtn.onclick = (e) => {
                    e.stopPropagation(); // 防止触发父元素的事件
                    removeFile(fileData.id);
                };

                // 组装元素
                previewContainer.appendChild(imgPreview);
                infoBar.appendChild(fileName);
                listItem.appendChild(previewContainer);
                listItem.appendChild(infoBar);
                listItem.appendChild(removeBtn);
                fileList.appendChild(listItem);
            });

            // 初始化或更新 SortableJS
            if (sortable) {
                sortable.destroy();
            }
            sortable = new Sortable(fileList, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                onEnd: function (evt) {
                    // 更新 files 数组的顺序
                    const movedItem = files.splice(evt.oldIndex, 1)[0];
                    files.splice(evt.newIndex, 0, movedItem);
                    // 重新渲染以更新序号
                    renderFileList();
                },
            });

        } else {
            fileListContainer.style.display = 'none';

            // 当没有照片时，显示拍照和扫码按钮
            toggleCameraButtons(true);
        }
    }

    // 控制拍照和扫码按钮的显示/隐藏
    function toggleCameraButtons(show) {
        const cameraButtons = document.querySelector('.camera-buttons');
        if (cameraButtons) {
            if (show) {
                cameraButtons.style.display = 'flex';
            } else {
                cameraButtons.style.display = 'none';
            }
        }
    }

    // 获取文件类型对应的 FontAwesome 图标
    function getFileIcon(fileType) {
        if (fileType === 'image/jpeg' || fileType === 'image/png' || fileType === 'image/jpg') {
            return 'fa-file-image';
        } else if (fileType === 'application/pdf') {
            return 'fa-file-pdf';
        } else if (fileType === 'application/msword' || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
            return 'fa-file-word';
        } else {
            return 'fa-file-alt'; // 默认图标
        }
    }

    // 移除文件
    function removeFile(fileId) {
        files = files.filter(fileData => fileData.id !== fileId);
        renderFileList();
        updateFileInfo();

        // 如果删除后没有照片了，显示拍照和扫码按钮
        if (files.length === 0) {
            toggleCameraButtons(true);
        }
    }

    // 更新文件信息区域
    function updateFileInfo() {
        if (files.length > 0) {
            fileInfo.textContent = `已拍摄 ${files.length} 张照片。可拖动照片调整顺序。`;

            // 检查是否可以提交
            const canSubmit = checkCanSubmit();
            submitBtn.disabled = !canSubmit;
        } else {
            fileInfo.textContent = '点击上方相机图标开始拍照';
            submitBtn.disabled = true;
        }
    }

    // 检查是否可以提交
    function checkCanSubmit() {
        // 必须有文件
        if (files.length === 0) return false;

        // 必须选择模板
        return selectedTemplatePathInput && selectedTemplatePathInput.value;
    }

    // --- 表单提交逻辑 ---
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault(); // 阻止默认表单提交

        if (files.length === 0) {
            showNotification('提示', '请至少拍摄一张照片！', false);
            return;
        }

        // 检查是否选择了模板
        if (!selectedTemplatePathInput || !selectedTemplatePathInput.value) {
            showNotification('提示', '请先选择模板类型！', false);
            return;
        }

        // 创建 FormData 对象
        const formData = new FormData();

        // 添加文件（按当前排序）
        files.forEach(fileData => {
            formData.append('files[]', fileData.file, fileData.file.name);
        });

        // 添加其他表单数据
        formData.append('ocr_engine', document.getElementById('ocr-engine').value);

        // 始终使用手动模式，并添加选择的模板
        formData.append('classification_mode', 'manual');
        formData.append('template_type', selectedTemplatePathInput.value);

        // 显示进度条
        progressContainer.style.display = 'block';
        progressBar.style.width = '0%';
        progressText.textContent = '正在上传文件...';
        statusStage.textContent = '文件上传中';
        submitBtn.disabled = true; // 禁用提交按钮

        // 确保进度条容器有一致的上下外边距
        progressContainer.style.margin = '30px 0';

        // 隐藏上传区域，让用户专注于处理进度
        const uploadContainer = document.querySelector('.upload-container');
        if (uploadContainer) {
            uploadContainer.style.display = 'none';
        }

        // 使用 XMLHttpRequest 发送请求，以便跟踪上传进度
        const xhr = new XMLHttpRequest();

        // 监听上传进度
        xhr.upload.addEventListener('progress', function(event) {
            if (event.lengthComputable) {
                const percentComplete = Math.round((event.loaded / event.total) * 50); // 上传占 50% 进度
                progressBar.style.width = percentComplete + '%';
                progressText.textContent = `正在上传照片... ${percentComplete * 2}%`;
            }
        }, false);

        // 监听请求完成（包括成功或失败）
        xhr.addEventListener('load', function() {
            if (xhr.status >= 200 && xhr.status < 300) {
                // 上传成功，开始状态查询
                progressText.textContent = '照片上传完成，正在处理...';
                statusStage.textContent = '服务端处理中';

                // 处理完成后清空文件列表
                resetFileList();

                // 定期查询处理状态
                let lastStatus = '';
                let errorCount = 0;
                const statusCheckInterval = setInterval(function() {
                    console.log('查询状态...');
                    fetch('/status', {
                        method: 'GET',
                        headers: {
                            'Cache-Control': 'no-cache, no-store, must-revalidate',
                            'Pragma': 'no-cache',
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                            'X-Timestamp': new Date().getTime() // 添加时间戳防止缓存
                        },
                        credentials: 'same-origin',
                        cache: 'no-store' // 禁用缓存
                    })
                    .then(response => {
                        console.log('状态响应:', response);
                        if (!response.ok) {
                            throw new Error(`状态查询错误: ${response.status} ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('收到状态数据:', data);
                        errorCount = 0; // 重置错误计数

                        // 检查状态是否发生变化
                        if (data.status !== lastStatus) {
                            console.log(`状态变化: ${lastStatus} -> ${data.status}`);
                            lastStatus = data.status;
                        }

                        // 更新状态显示
                        progressText.textContent = `${data.message}`;
                        statusStage.textContent = data.status;

                        // 根据状态设置进度条
                        switch(data.status) {
                            case '开始':
                                progressBar.style.width = '10%';
                                break;
                            case '上传':
                                progressBar.style.width = '30%';
                                break;
                            case 'OCR':
                            case 'OCR中':
                                progressBar.style.width = '50%';
                                break;
                            case '等待确认':
                                // OCR完成，等待用户确认
                                progressBar.style.width = '60%';
                                clearInterval(statusCheckInterval);

                                console.log('检测到等待确认状态，显示确认弹窗（触摸屏模式）');

                                // 显示确认弹窗
                                if (data.ocr_text && data.task_id) {
                                    showTouchscreenConfirmationModal(data.ocr_text, data.task_id);
                                } else {
                                    console.error('缺少OCR文本或任务ID');
                                    showNotification('错误', '无法显示确认弹窗，请重新开始', false);
                                }
                                break;
                            case 'LLM':
                            case 'LLM处理':
                                progressBar.style.width = '70%';
                                break;
                            case '生成文档':
                                progressBar.style.width = '90%';
                                break;
                            case '文档编辑':
                                // 文档编辑状态 - 自动跳转到编辑页面（触摸屏版本）
                                progressBar.style.width = '85%';
                                clearInterval(statusCheckInterval);

                                console.log('检测到文档编辑状态，准备跳转到编辑页面（触摸屏模式）');

                                // 设置触摸屏来源标识
                                sessionStorage.setItem('source', 'touchscreen');
                                sessionStorage.setItem('touchscreen_mode', 'true');

                                // 检查是否有编辑URL
                                if (data.edit_url) {
                                    console.log('使用状态中的edit_url跳转:', data.edit_url);
                                    setTimeout(() => {
                                        // 在同一标签页中跳转，保持触摸屏导航
                                        window.location.href = data.edit_url;
                                    }, 500); // 短暂延迟让用户看到状态变化
                                } else {
                                    console.log('未找到edit_url，尝试从初始响应获取');
                                    // 尝试从初始响应中获取编辑URL
                                    try {
                                        const response = JSON.parse(xhr.responseText);
                                        if (response.edit_url) {
                                            console.log('使用初始响应中的edit_url:', response.edit_url);
                                            sessionStorage.setItem('source', 'touchscreen');
                                            setTimeout(() => {
                                                window.location.href = response.edit_url;
                                            }, 500);
                                            return;
                                        }
                                    } catch (error) {
                                        console.log('解析初始响应失败:', error);
                                    }

                                    // 如果都没有找到，显示错误
                                    console.error('未找到编辑页面URL');
                                    showNotification('错误', '无法跳转到编辑页面，请重新开始', false);
                                }
                                break;
                            case '完成':
                                progressBar.style.width = '100%';
                                clearInterval(statusCheckInterval);

                                // 处理完成后显示下载按钮
                                console.log('处理完成，显示下载按钮');

                                // 更新进度条区域，显示下载按钮
                                progressText.textContent = '处理完成！';
                                statusStage.textContent = '处理成功';

                                // 隐藏上传区域，强制用户在当前结果上操作
                                const uploadContainer = document.querySelector('.upload-container');
                                if (uploadContainer) {
                                    uploadContainer.style.display = 'none';
                                }

                                // 如果有下载链接，创建下载按钮
                                if (data.download_url) {
                                    // 创建下载按钮容器
                                    const downloadContainer = document.createElement('div');
                                    downloadContainer.className = 'download-container';

                                    // 创建倒计时容器
                                    const countdownContainer = document.createElement('div');
                                    countdownContainer.className = 'countdown-container';
                                    countdownContainer.innerHTML = '<i class="fas fa-clock"></i> <span id="countdown-timer">90</span> 秒后自动返回首页';
                                    progressContainer.appendChild(countdownContainer);

                                    // 启动倒计时
                                    startCountdown(90);

                                    // 创建下载按钮（改为显示二维码）
                                    const downloadBtn = document.createElement('button');
                                    downloadBtn.className = 'download-btn';
                                    downloadBtn.innerHTML = '<i class="fas fa-qrcode"></i> 扫码下载';

                                    // 创建二维码对话框
                                    const qrCodeModal = document.createElement('div');
                                    qrCodeModal.className = 'qr-code-modal';
                                    qrCodeModal.style.display = 'none';

                                    const qrCodeModalContent = document.createElement('div');
                                    qrCodeModalContent.className = 'qr-code-modal-content';

                                    const qrCodeModalHeader = document.createElement('div');
                                    qrCodeModalHeader.className = 'qr-code-modal-header';
                                    qrCodeModalHeader.innerHTML = '<h3>扫描二维码下载文档</h3>';

                                    const qrCodeModalClose = document.createElement('span');
                                    qrCodeModalClose.className = 'qr-code-modal-close';
                                    qrCodeModalClose.innerHTML = '&times;';
                                    qrCodeModalClose.addEventListener('click', function() {
                                        qrCodeModal.style.display = 'none';
                                    });

                                    const qrCodeContainer = document.createElement('div');
                                    qrCodeContainer.className = 'qr-code-container';

                                    const qrCodeImage = document.createElement('img');
                                    qrCodeImage.className = 'qr-code-image';
                                    qrCodeImage.alt = '下载二维码';

                                    const qrCodeInstructions = document.createElement('div');
                                    qrCodeInstructions.className = 'qr-code-instructions';
                                    qrCodeInstructions.innerHTML = '<p>1. 使用手机扫描上方二维码</p><p>2. 在手机上打开链接下载文档</p>';

                                    // 组装二维码对话框
                                    qrCodeModalHeader.appendChild(qrCodeModalClose);
                                    qrCodeContainer.appendChild(qrCodeImage);
                                    qrCodeModalContent.appendChild(qrCodeModalHeader);
                                    qrCodeModalContent.appendChild(qrCodeContainer);
                                    qrCodeModalContent.appendChild(qrCodeInstructions);
                                    qrCodeModal.appendChild(qrCodeModalContent);
                                    document.body.appendChild(qrCodeModal);

                                    // 添加下载按钮点击事件
                                    downloadBtn.addEventListener('click', function() {
                                        // 显示加载中状态
                                        downloadBtn.disabled = true;
                                        downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成二维码...';

                                        // 调用生成二维码API
                                        fetch(`/generate-download-qr-code?url=${encodeURIComponent(data.download_url)}`)
                                            .then(response => response.json())
                                            .then(qrData => {
                                                if (qrData.success) {
                                                    // 设置二维码图片
                                                    qrCodeImage.src = qrData.qr_code_url;

                                                    // 显示二维码对话框
                                                    qrCodeModal.style.display = 'block';
                                                } else {
                                                    showNotification('二维码生成失败', qrData.message, false);
                                                }
                                            })
                                            .catch(error => {
                                                console.error('生成二维码失败:', error);
                                                showNotification('错误', '生成二维码失败，请重试', false);
                                            })
                                            .finally(() => {
                                                // 恢复按钮状态
                                                downloadBtn.disabled = false;
                                                downloadBtn.innerHTML = '<i class="fas fa-qrcode"></i> 扫码下载';
                                            });
                                    });

                                    // 点击对话框外部关闭
                                    window.addEventListener('click', function(event) {
                                        if (event.target === qrCodeModal) {
                                            qrCodeModal.style.display = 'none';
                                        }
                                    });

                                    // 创建打印按钮
                                    const printBtn = document.createElement('button');
                                    printBtn.className = 'print-btn';
                                    printBtn.innerHTML = '<i class="fas fa-print"></i> 打印文档';

                                    // 添加打印按钮点击事件
                                    printBtn.addEventListener('click', function() {
                                        // 获取完整的文件路径（从下载URL中提取）
                                        const downloadUrl = data.download_url;
                                        const filepath = downloadUrl.replace('/download/', '');
                                        // 显示打印中提示
                                        printBtn.disabled = true;
                                        printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 打印中...';

                                        // 调用打印API
                                        fetch(`/print-document/${filepath}`, {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/json'
                                            }
                                        })
                                        .then(response => response.json())
                                        .then(printResult => {
                                            if (printResult.success) {
                                                // 打印成功
                                                showNotification('打印成功', '文档已发送到打印机，请到打印机处取件');
                                            } else {
                                                // 打印失败
                                                console.error('打印失败:', printResult);
                                                if (printResult.message && printResult.message.includes('未找到默认打印机')) {
                                                    // 打印机不可用时，显示带下载按钮的通知
                                                    const customButtons = [
                                                        {
                                                            text: '<i class="fas fa-download"></i> 下载文档',
                                                            className: 'notification-btn download-btn',
                                                            onClick: function() {
                                                                // 直接下载文档
                                                                window.open(downloadUrl, '_blank');
                                                            }
                                                        },
                                                        {
                                                            text: '确定',
                                                            className: 'notification-btn',
                                                            onClick: null
                                                        }
                                                    ];
                                                    showNotification('打印失败', '系统未配置打印机，您可以下载文档到本地', false, 0, customButtons);
                                                } else {
                                                    showNotification('打印失败', `${printResult.message}`, false);
                                                }
                                            }
                                        })
                                        .catch(error => {
                                            console.error('打印请求出错:', error);
                                            showNotification('打印错误', '打印请求出错，请稍后重试', false);
                                        })
                                        .finally(() => {
                                            // 恢复按钮状态
                                            printBtn.disabled = false;
                                            printBtn.innerHTML = '<i class="fas fa-print"></i> 打印文档';
                                        });
                                    });

                                    // 创建重新开始按钮
                                    const restartBtn = document.createElement('button');
                                    restartBtn.className = 'restart-btn';
                                    restartBtn.innerHTML = '<i class="fas fa-redo"></i> 清理并重新开始';

                                    // 添加重新开始按钮点击事件
                                    restartBtn.addEventListener('click', function() {
                                        // 显示加载中状态
                                        restartBtn.disabled = true;
                                        restartBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在清理...';

                                        // 清除倒计时
                                        clearCountdown();

                                        // 调用清理API
                                        fetch('/clean-temp-files', {
                                            method: 'POST',
                                            headers: {
                                                'Content-Type': 'application/json'
                                            }
                                        })
                                        .then(response => response.json())
                                        .then(data => {
                                            if (data.success) {
                                                console.log('临时文件清理成功:', data.message);
                                            } else {
                                                console.error('临时文件清理失败:', data.message);
                                            }
                                        })
                                        .catch(error => {
                                            console.error('清理请求出错:', error);
                                        })
                                        .finally(() => {
                                            // 无论成功失败，都刷新页面
                                            window.location.reload();
                                        });
                                    });

                                    // 添加到进度条容器
                                    downloadContainer.appendChild(downloadBtn);
                                    downloadContainer.appendChild(printBtn);
                                    downloadContainer.appendChild(restartBtn);
                                    progressContainer.appendChild(downloadContainer);
                                } else {
                                    progressText.textContent = '处理完成，但未收到下载链接。';
                                }
                                break;
                            case '错误':
                                progressBar.style.width = '100%';
                                progressBar.style.backgroundColor = '#e74c3c';
                                clearInterval(statusCheckInterval);
                                handleError(data.message || '处理出错');
                                break;
                        }
                    })
                    .catch(error => {
                        console.error('状态查询错误:', error);
                        errorCount++;

                        // 连续多次错误才显示错误信息
                        if (errorCount > 3) {
                            // 显示错误信息
                            handleError(`状态查询失败: ${error.message}`);
                            clearInterval(statusCheckInterval);
                        }
                    });
                }, 1000); // 每1000毫秒查询一次状态
            } else {
                // 处理失败
                handleError(`上传失败: ${xhr.status} ${xhr.statusText}`);
            }
        });

        // 监听请求错误
        xhr.addEventListener('error', function() {
            handleError('网络错误，上传失败');
        });

        // 监听请求中止
        xhr.addEventListener('abort', function() {
            handleError('上传已中止');
        });

        // 配置并发送请求
        xhr.open('POST', uploadForm.action, true);
        xhr.send(formData);
    });

    // 处理完成后清空文件列表
    function resetFileList() {
        files = [];
        renderFileList();
        updateFileInfo();
    }

    // 处理错误
    function handleError(errorMessage) {
        console.error('处理出错:', errorMessage);
        progressBar.style.backgroundColor = '#e74c3c'; // 进度条变红
        progressBar.style.width = '100%';
        progressText.textContent = `错误：${errorMessage}`;
        statusStage.textContent = '处理失败';
        submitBtn.disabled = false; // 重新启用提交按钮

        // 清除倒计时（如果存在）
        clearCountdown();

        // 创建重新开始按钮，方便用户在出错时重新开始
        const errorRestartContainer = document.createElement('div');
        errorRestartContainer.className = 'error-restart-container';

        const restartBtn = document.createElement('button');
        restartBtn.className = 'restart-btn';
        restartBtn.innerHTML = '<i class="fas fa-redo"></i> 清理并重新开始';

        // 添加重新开始按钮点击事件
        restartBtn.addEventListener('click', function() {
            // 显示加载中状态
            restartBtn.disabled = true;
            restartBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在清理...';

            // 清除倒计时（如果存在）
            clearCountdown();

            // 调用清理API
            fetch('/clean-temp-files', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('临时文件清理成功:', data.message);
                } else {
                    console.error('临时文件清理失败:', data.message);
                }
            })
            .catch(error => {
                console.error('清理请求出错:', error);
            })
            .finally(() => {
                // 无论成功失败，都刷新页面
                window.location.reload();
            });
        });

        // 隐藏上传区域，强制用户重新开始
        const uploadContainer = document.querySelector('.upload-container');
        if (uploadContainer) {
            uploadContainer.style.display = 'none';
        }

        // 添加到进度条容器
        errorRestartContainer.appendChild(restartBtn);
        progressContainer.appendChild(errorRestartContainer);
    }

    // --- 模板选择逻辑（新的流程） ---
    // 页面加载时就加载模板层级结构
    loadTemplateHierarchy();

    // 全局变量存储模板层级数据
    let templateHierarchy = null;

    // 加载模板层级结构
    function loadTemplateHierarchy() {
        fetch('/api/template-hierarchy')
            .then(response => response.json())
            .then(data => {
                templateHierarchy = data;
                console.log('模板层级数据加载成功:', templateHierarchy);
            })
            .catch(error => {
                console.error('加载模板层级失败:', error);
            });
    }

    // 移除了旧的模板下载加载逻辑

    function populateTemplateSelect(selectElement, templates, addPlaceholder = false) {
        if (!selectElement) return;

        selectElement.innerHTML = ''; // 清空现有选项
        if (addPlaceholder) {
            const placeholder = document.createElement('option');
            placeholder.value = '';
            placeholder.textContent = '请选择模板';
            placeholder.disabled = true;
            placeholder.selected = true;
            selectElement.appendChild(placeholder);
        }

        // 确保templates是数组且有内容
        if (Array.isArray(templates) && templates.length > 0) {
            templates.forEach(template => {
                const option = document.createElement('option');
                // 使用正确的属性名
                option.value = template.filename || '';
                option.textContent = template.name || '';
                selectElement.appendChild(option);
            });
        }
    }

    // 移除了旧的模板下载逻辑

    // --- 二维码扫描功能 ---
    // 存储移动端上传的文件
    let mobileUploadedFiles = [];

    // 打开扫码上传对话框
    if (qrUploadBtn) {
        qrUploadBtn.addEventListener('click', function(e) {
            // 阻止事件冒泡，防止触发cameraPlaceholder的点击事件
            e.stopPropagation();

            // 获取二维码URL
            fetch('/generate-qr-code')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 显示二维码
                        qrCodeImage.src = data.qr_code_url;

                        // 更新会话ID
                        if (sessionIdEl && data.session_id) {
                            sessionIdEl.textContent = data.session_id;
                        }

                        // 显示对话框
                        qrModal.style.display = 'block';

                        // 建立Socket.IO连接
                        initSocketConnection(data.session_id);
                    } else {
                        showNotification('二维码生成失败', data.message, false);
                    }
                })
                .catch(error => {
                    console.error('获取二维码失败:', error);
                    showNotification('错误', '获取二维码失败，请重试', false);
                });
        });
    }

    // 关闭对话框
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            qrModal.style.display = 'none';

            // 如果有上传的文件，添加到文件列表
            if (mobileUploadedFiles.length > 0) {
                addMobileFilesToUploadList(mobileUploadedFiles);
            }
        });
    }

    // 点击对话框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target === qrModal) {
            qrModal.style.display = 'none';

            // 如果有上传的文件，添加到文件列表
            if (mobileUploadedFiles.length > 0) {
                addMobileFilesToUploadList(mobileUploadedFiles);
            }
        }
    });

    // 初始化Socket.IO连接
    function initSocketConnection(sessionId) {
        // 创建Socket连接
        const socket = io();

        // 更新连接状态
        connectionStatus.textContent = '正在连接...';

        // 连接成功
        socket.on('connect', function() {
            connectionStatus.textContent = '已连接';

            // 加入会话
            socket.emit('join_session', { session_id: sessionId, client_type: 'pc' });

            // 监听移动端加入
            socket.on('mobile_joined', function() {
                connectionStatus.textContent = '手机已连接';
            });

            // 监听移动端上传文件
            socket.on('file_uploaded', function(data) {
                // 显示接收到的文件
                const fileData = data.file_data;

                // 存储文件数据
                mobileUploadedFiles.push({
                    name: fileData.filename,
                    type: fileData.file_type,
                    size: fileData.file_size,
                    dataUrl: fileData.data
                });

                // 更新文件列表
                updateMobileFilesList();

                // 更新进度信息
                if (fileData.total_files > 1) {
                    connectionStatus.textContent = `已连接 - 接收文件 ${fileData.current_file}/${fileData.total_files}`;
                }

                // 显示文件列表容器
                mobileFilesContainer.style.display = 'block';

                // 发送上传成功响应
                socket.emit('upload_response', {
                    session_id: sessionId,
                    success: true,
                    message: '文件已成功上传到触摸屏端'
                });
            });
        });

        // 连接错误
        socket.on('connect_error', function() {
            connectionStatus.textContent = '连接失败';
        });

        // 连接断开
        socket.on('disconnect', function() {
            connectionStatus.textContent = '连接已断开';
        });
    }

    // 更新移动端上传的文件列表
    function updateMobileFilesList() {
        if (!mobileFilesList) return;

        mobileFilesList.innerHTML = '';

        mobileUploadedFiles.forEach((fileData, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'mobile-file-item';

            const filePreview = document.createElement('div');
            filePreview.className = 'mobile-file-preview';

            const img = document.createElement('img');
            img.src = fileData.dataUrl;
            img.alt = fileData.name;

            const fileInfo = document.createElement('div');
            fileInfo.className = 'mobile-file-info';
            fileInfo.textContent = `照片 ${index + 1}`;
            fileInfo.title = fileData.name;

            filePreview.appendChild(img);
            fileItem.appendChild(filePreview);
            fileItem.appendChild(fileInfo);
            mobileFilesList.appendChild(fileItem);
        });
    }

    // 将移动端上传的文件添加到上传列表
    function addMobileFilesToUploadList(mobileFiles) {
        if (!mobileFiles || mobileFiles.length === 0) return;

        // 将Base64数据URL转换为Blob对象
        mobileFiles.forEach((fileData) => {
            // 从dataUrl中提取base64数据
            const base64Data = fileData.dataUrl.split(',')[1];
            const byteCharacters = atob(base64Data);
            const byteArrays = [];

            for (let i = 0; i < byteCharacters.length; i++) {
                byteArrays.push(byteCharacters.charCodeAt(i));
            }

            const byteArray = new Uint8Array(byteArrays);
            const blob = new Blob([byteArray], { type: fileData.type });

            // 创建File对象
            const file = new File([blob], fileData.name, { type: fileData.type });

            // 生成唯一ID并添加到文件列表
            const fileId = Date.now() + '-' + Math.random().toString(36).substring(2, 9);
            files.push({ id: fileId, file: file });
        });

        // 清空移动端上传的文件列表
        mobileUploadedFiles = [];

        // 更新界面
        renderFileList();
        updateFileInfo();

        // 显示文件列表
        if (files.length > 0) {
            fileListContainer.style.display = 'block';
        }
    }

    // --- 倒计时功能 ---

    // 启动倒计时
    function startCountdown(seconds) {
        // 清除可能存在的旧倒计时
        clearCountdown();

        // 获取倒计时显示元素
        const countdownElement = document.getElementById('countdown-timer');
        if (!countdownElement) return;

        // 设置初始时间
        let remainingTime = seconds;
        countdownElement.textContent = remainingTime;

        // 创建倒计时定时器
        countdownTimer = setInterval(function() {
            remainingTime--;

            // 更新显示
            if (countdownElement) {
                countdownElement.textContent = remainingTime;
            }

            // 检查是否倒计时结束
            if (remainingTime <= 0) {
                clearInterval(countdownTimer);

                console.log('倒计时结束，自动清理并返回首页');

                // 调用清理API
                fetch('/clean-temp-files', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('临时文件清理成功:', data.message);
                    } else {
                        console.error('临时文件清理失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('清理请求出错:', error);
                })
                .finally(() => {
                    // 无论成功失败，都刷新页面
                    window.location.href = '/touchscreen';
                });
            }
        }, 1000);
    }

    // 清除倒计时
    function clearCountdown() {
        if (countdownTimer) {
            clearInterval(countdownTimer);
            countdownTimer = null;
            console.log('倒计时已清除');
        }
    }

    // --- 卡片式模板选择逻辑 ---
    const templateConfirm = document.getElementById('template-confirm');
    const uploadContainer = document.getElementById('upload-container');
    const selectedTemplatePathInput = document.getElementById('selected-template-path');
    const selectedTemplateNameSpan = document.getElementById('selected-template-name');

    // 获取容器元素
    const categoriesContainer = document.getElementById('categories-container');
    const templatesContainer = document.getElementById('templates-container');
    const templateCardsGrid = document.getElementById('template-cards-grid');
    const backToCategoriesBtn = document.getElementById('back-to-categories');

    // 当前状态
    let currentBatch = '第一批';
    let currentCategory = null;
    let currentSubcategory = null;

    // 批次选择逻辑
    const batchTabs = document.querySelectorAll('.batch-tab');
    batchTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 更新活跃状态
            batchTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // 更新当前批次
            currentBatch = this.dataset.batch;
            console.log('选择批次:', currentBatch);

            // 重置状态
            currentCategory = null;
            currentSubcategory = null;

            // 显示分类
            showCategories();
        });
    });

    // 常用模板点击事件
    const commonTemplateCards = document.querySelectorAll('.common-template');
    commonTemplateCards.forEach(card => {
        card.addEventListener('click', function() {
            const templatePath = this.dataset.template;
            const templateName = this.querySelector('h4').textContent;
            selectTemplate(templatePath, templateName);
        });
    });

    // 返回分类按钮
    if (backToCategoriesBtn) {
        backToCategoriesBtn.addEventListener('click', function() {
            showCategories();
        });
    }

    // 打印选中模板按钮
    const printSelectedTemplateBtn = document.getElementById('print-selected-template-btn');
    if (printSelectedTemplateBtn) {
        printSelectedTemplateBtn.addEventListener('click', function() {
            if (selectedTemplatePathInput && selectedTemplatePathInput.value) {
                printSelectedTemplate(selectedTemplatePathInput.value);
            } else {
                showNotification('提示', '请先选择模板！', false);
            }
        });
    }

    // 继续上传按钮
    const continueToUploadBtn = document.getElementById('continue-to-upload-btn');
    if (continueToUploadBtn) {
        continueToUploadBtn.addEventListener('click', function() {
            if (uploadContainer) {
                uploadContainer.style.display = 'block';
                uploadContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        });
    }

    // 直接编辑模板按钮
    const directEditTemplateBtn = document.getElementById('direct-edit-template-btn');
    if (directEditTemplateBtn) {
        directEditTemplateBtn.addEventListener('click', function() {
            const templatePath = selectedTemplatePathInput ? selectedTemplatePathInput.value : '';
            const templateName = selectedTemplateNameSpan ? selectedTemplateNameSpan.textContent : '';

            if (!templatePath) {
                showNotification('提示', '请先选择模板！', false);
                return;
            }

            directEditTemplate(templatePath, templateName);
        });
    }

    // 显示分类卡片
    function showCategories() {
        if (!templateHierarchy || !templateHierarchy[currentBatch]) {
            return;
        }

        if (!categoriesContainer) {
            return;
        }

        categoriesContainer.innerHTML = '';
        if (templatesContainer) {
            templatesContainer.style.display = 'none';
        }
        categoriesContainer.style.display = 'grid';

        const batchData = templateHierarchy[currentBatch];

        // 为每个案件大类创建卡片
        for (const categoryKey in batchData) {
            const categoryData = batchData[categoryKey];
            const categoryCard = createCategoryCard(categoryKey, categoryData);
            categoriesContainer.appendChild(categoryCard);
        }
    }

    // 创建分类卡片
    function createCategoryCard(categoryKey, categoryData) {
        const card = document.createElement('div');
        card.className = 'category-card';
        card.dataset.category = categoryKey;

        // 计算模板总数
        let totalTemplates = 0;
        for (const subcategoryKey in categoryData) {
            totalTemplates += categoryData[subcategoryKey].length;
        }

        // 设置图标
        let icon = 'fas fa-gavel';
        if (categoryKey === '民事') icon = 'fas fa-balance-scale';
        else if (categoryKey === '刑事') icon = 'fas fa-handcuffs';
        else if (categoryKey === '行政') icon = 'fas fa-landmark';
        else if (categoryKey === '通用') icon = 'fas fa-file-alt';

        card.innerHTML = `
            <div class="category-icon">
                <i class="${icon}"></i>
            </div>
            <div class="category-name">${categoryKey}</div>
            <div class="category-count">${totalTemplates} 个模板</div>
        `;

        // 添加点击事件
        card.addEventListener('click', function() {
            currentCategory = categoryKey;
            showSubcategories(categoryData);
        });

        return card;
    }

    // 显示子分类模板
    function showSubcategories(categoryData) {
        templateCardsGrid.innerHTML = '';
        categoriesContainer.style.display = 'none';
        templatesContainer.style.display = 'block';

        // 为每个子分类的模板创建卡片
        for (const subcategoryKey in categoryData) {
            const templates = categoryData[subcategoryKey];

            // 添加子分类标题
            const subcategoryTitle = document.createElement('div');
            subcategoryTitle.className = 'subcategory-title';
            subcategoryTitle.innerHTML = `<h4><i class="fas fa-folder"></i> ${subcategoryKey}</h4>`;
            templateCardsGrid.appendChild(subcategoryTitle);

            // 添加模板卡片
            templates.forEach(template => {
                const templateCard = createTemplateCard(template, subcategoryKey);
                templateCardsGrid.appendChild(templateCard);
            });
        }
    }

    // 创建模板卡片
    function createTemplateCard(template, subcategoryKey) {
        const card = document.createElement('div');
        card.className = 'template-card';
        card.dataset.template = template.file;

        // 设置图标
        let icon = getTemplateIcon(template.name, subcategoryKey);

        card.innerHTML = `
            <div class="card-icon">
                <i class="${icon}"></i>
            </div>
            <div class="card-content">
                <h4>${template.name}</h4>
                <p>${getTemplateDescription(template.name)}</p>
            </div>
        `;

        // 添加点击事件
        card.addEventListener('click', function() {
            selectTemplate(template.file, template.name);
        });

        return card;
    }

    // 获取模板图标
    function getTemplateIcon(templateName, subcategoryKey) {
        if (templateName.includes('离婚')) return 'fas fa-heart-broken';
        if (templateName.includes('交通') || templateName.includes('机动车')) return 'fas fa-car-crash';
        if (templateName.includes('买卖') || templateName.includes('合同')) return 'fas fa-handshake';
        if (templateName.includes('借贷') || templateName.includes('借款')) return 'fas fa-coins';
        if (templateName.includes('劳动')) return 'fas fa-briefcase';
        if (templateName.includes('保险')) return 'fas fa-shield-alt';
        if (templateName.includes('专利') || templateName.includes('商标')) return 'fas fa-lightbulb';
        if (templateName.includes('刑事')) return 'fas fa-handcuffs';
        if (templateName.includes('行政')) return 'fas fa-landmark';
        if (templateName.includes('房屋')) return 'fas fa-home';
        return 'fas fa-file-alt';
    }

    // 获取模板描述
    function getTemplateDescription(templateName) {
        if (templateName.includes('离婚')) return '婚姻关系解除相关纠纷';
        if (templateName.includes('交通') || templateName.includes('机动车')) return '交通事故责任纠纷';
        if (templateName.includes('买卖')) return '买卖合同相关纠纷';
        if (templateName.includes('民间借贷')) return '民间借贷纠纷';
        if (templateName.includes('金融借款')) return '金融借款合同纠纷';
        if (templateName.includes('劳动')) return '劳动关系相关纠纷';
        return '相关法律纠纷';
    }

    // 选择模板
    function selectTemplate(templatePath, templateName) {
        console.log('选择模板:', templatePath, templateName);

        // 移除所有卡片的选中状态
        document.querySelectorAll('.template-card').forEach(card => {
            card.classList.remove('selected');
        });

        // 添加当前卡片的选中状态
        const selectedCard = document.querySelector(`[data-template="${templatePath}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        // 更新隐藏字段
        if (selectedTemplatePathInput) {
            selectedTemplatePathInput.value = templatePath;
        }

        // 显示选择确认
        if (selectedTemplateNameSpan) {
            selectedTemplateNameSpan.textContent = templateName;
        }

        if (templateConfirm) {
            templateConfirm.style.display = 'block';
            // 滚动到确认区域
            templateConfirm.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        updateFileInfo(); // 更新提交按钮状态
    }

    // 直接编辑模板
    function directEditTemplate(templatePath, templateName) {
        console.log('直接编辑模板:', templatePath, templateName);

        // 显示编辑中提示
        const editBtn = document.getElementById('direct-edit-template-btn');
        if (editBtn) {
            editBtn.disabled = true;
            editBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 准备编辑...';
        }

        // 设置触摸屏模式标识
        sessionStorage.setItem('source', 'touchscreen');
        sessionStorage.setItem('touchscreen_mode', 'true');

        // 调用直接编辑API
        fetch('/direct-edit-template', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                template_path: templatePath,
                template_name: templateName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.edit_url) {
                console.log('直接编辑准备成功，跳转到编辑页面:', data.edit_url);
                // 跳转到编辑页面
                window.location.href = data.edit_url + '?touchscreen=true';
            } else {
                console.error('直接编辑准备失败:', data.error);
                showNotification('错误', data.error || '准备编辑失败，请重试', false);

                // 恢复按钮状态
                if (editBtn) {
                    editBtn.disabled = false;
                    editBtn.innerHTML = '<i class="fas fa-edit"></i> 直接编辑模板';
                }
            }
        })
        .catch(error => {
            console.error('直接编辑请求失败:', error);
            showNotification('错误', '网络错误，请重试', false);

            // 恢复按钮状态
            if (editBtn) {
                editBtn.disabled = false;
                editBtn.innerHTML = '<i class="fas fa-edit"></i> 直接编辑模板';
            }
        });
    }

    // 打印选中的模板
    function printSelectedTemplate(templatePath) {
        console.log('打印模板:', templatePath);

        // 从模板路径中提取文件名
        const fileName = templatePath.split('/').pop();

        // 显示打印中提示
        const printBtn = document.getElementById('print-selected-template-btn');
        if (printBtn) {
            printBtn.disabled = true;
            printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 打印中...';
        }

        // 调用打印API - 直接传递文件名，后端会在templates-raw目录中查找
        fetch(`/print-template/${encodeURIComponent(fileName)}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 打印成功
                showNotification('打印成功', '模板已发送到打印机，请到打印机处取件', true);
            } else {
                // 打印失败
                console.error('打印失败:', data);
                if (data.message && data.message.includes('未找到默认打印机')) {
                    // 打印机不可用时，显示带下载按钮的通知
                    const customButtons = [
                        {
                            text: '<i class="fas fa-download"></i> 下载模板',
                            className: 'notification-btn download-btn',
                            onClick: function() {
                                // 构建模板下载URL - 使用templates-raw目录中的原始文件
                                const templatePath = selectedTemplatePathInput.value;
                                const fileName = templatePath.split('/').pop();
                                const downloadUrl = `/static/templates-raw/${encodeURIComponent(fileName)}`;
                                window.open(downloadUrl, '_blank');
                            }
                        },
                        {
                            text: '确定',
                            className: 'notification-btn',
                            onClick: null
                        }
                    ];
                    showNotification('打印失败', '系统未配置打印机，您可以下载模板到本地', false, 0, customButtons);
                } else {
                    showNotification('打印失败', `${data.message}`, false);
                }
            }
        })
        .catch(error => {
            console.error('打印请求出错:', error);
            showNotification('打印错误', '打印请求出错，请稍后重试', false);
        })
        .finally(() => {
            // 恢复按钮状态
            if (printBtn) {
                printBtn.disabled = false;
                printBtn.innerHTML = '<i class="fas fa-print"></i> 打印空白模板';
            }
        });
    }

    // 显示步骤
    function showStep(stepElement) {
        if (stepElement) {
            stepElement.style.display = 'block';
        }
    }

    // 隐藏步骤
    function hideStep(stepElement) {
        if (stepElement) {
            stepElement.style.display = 'none';
        }
    }

    // 隐藏指定步骤之后的所有步骤
    function hideStepsAfter(currentStep) {
        const steps = [categoryStep, subcategoryStep, templateStep, templateConfirm];
        let foundCurrent = false;

        steps.forEach(step => {
            if (foundCurrent) {
                hideStep(step);
            }
            if (step === currentStep) {
                foundCurrent = true;
            }
        });

        // 如果隐藏了模板确认，也隐藏上传区域
        if (foundCurrent && uploadContainer) {
            uploadContainer.style.display = 'none';
        }
    }

    // 填充案件大类选择框
    function populateCategorySelect(selectElement, batchData) {
        if (!selectElement) return;

        selectElement.innerHTML = '<option value="" disabled selected>请选择案件类型</option>';

        for (const categoryKey in batchData) {
            const option = document.createElement('option');
            option.value = categoryKey;
            option.textContent = categoryKey;
            selectElement.appendChild(option);
        }
    }

    // 填充具体分类选择框
    function populateSubcategorySelect(selectElement, categoryData) {
        if (!selectElement) return;

        selectElement.innerHTML = '<option value="" disabled selected>请选择具体类型</option>';

        for (const subcategoryKey in categoryData) {
            const option = document.createElement('option');
            option.value = subcategoryKey;
            option.textContent = subcategoryKey;
            selectElement.appendChild(option);
        }
    }

    // 填充最终模板选择框
    function populateFinalTemplateSelect(selectElement, templates) {
        if (!selectElement || !Array.isArray(templates)) return;

        selectElement.innerHTML = '<option value="" disabled selected>请选择模板</option>';

        templates.forEach(template => {
            const option = document.createElement('option');
            option.value = template.file;
            option.textContent = template.name;
            selectElement.appendChild(option);
        });

        // 添加事件监听器（如果还没有添加）
        if (!selectElement.hasAttribute('data-listener-added')) {
            selectElement.addEventListener('change', function() {
                console.log('选择模板:', this.value);
                updateFileInfo(); // 更新提交按钮状态
            });
            selectElement.setAttribute('data-listener-added', 'true');
        }
    }

    // 初始化页面
    function initializePage() {
        // 设置默认批次
        currentBatch = '第一批';

        // 确保第一批标签是激活状态
        const batchTabs = document.querySelectorAll('.batch-tab');
        batchTabs.forEach(tab => {
            if (tab.dataset.batch === '第一批') {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });

        // 显示第一批的分类
        showCategories();
    }

    // --- 初始化 ---
    updateFileInfo(); // 初始时禁用提交按钮
    loadTemplateHierarchy(); // 加载模板层级结构

    // 重新定义loadTemplateHierarchy函数，加载完成后立即初始化
    function loadTemplateHierarchy() {
        fetch('/api/template-hierarchy')
            .then(response => response.json())
            .then(data => {
                templateHierarchy = data;
                console.log('模板层级数据加载成功:', templateHierarchy);
                initializePage(); // 数据加载完成后立即初始化页面
            })
            .catch(error => {
                console.error('加载模板层级失败:', error);
                // 即使加载失败，也要显示基本界面
                initializePage();
            });
    }

    // 显示触摸屏版本的OCR文字确认弹窗
    window.showTouchscreenConfirmationModal = function(ocrText, taskId) {
        // 创建弹窗HTML（触摸屏优化版本）
        const modalHtml = `
            <div id="touchscreen-confirmation-modal" class="touchscreen-confirmation-modal">
                <div class="touchscreen-confirmation-modal-content">
                    <div class="touchscreen-confirmation-modal-header">
                        <h2><i class="fas fa-check-circle"></i> 确认文字内容</h2>
                        <span class="touchscreen-confirmation-close-btn">&times;</span>
                    </div>
                    <div class="touchscreen-confirmation-modal-body">
                        <div class="help-text">
                            <p><i class="fas fa-info-circle"></i> 请仔细检查OCR识别的文字内容，如有错误可以直接修改</p>
                        </div>
                        <div class="text-preview-container">
                            <div class="text-preview-header">
                                <h3>OCR识别结果预览</h3>
                                <span class="text-stats">字符数：<strong id="touchscreen-modal-char-count">${ocrText.length}</strong></span>
                            </div>
                            <div class="text-content-preview">${ocrText.replace(/\n/g, '<br>')}</div>
                        </div>
                        <div class="form-group">
                            <label for="touchscreen-modal-confirmed-text">编辑文字内容：</label>
                            <textarea id="touchscreen-modal-confirmed-text" class="form-control" rows="8" placeholder="请在此处编辑文字内容...">${ocrText}</textarea>
                        </div>
                    </div>
                    <div class="touchscreen-confirmation-modal-footer">
                        <button type="button" id="touchscreen-modal-back-btn" class="btn btn-secondary btn-large">
                            <i class="fas fa-arrow-left"></i> 取消
                        </button>
                        <button type="button" id="touchscreen-modal-reset-btn" class="btn btn-outline btn-large">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                        <button type="button" id="touchscreen-modal-confirm-btn" class="btn btn-primary btn-large">
                            <i class="fas fa-check"></i> 确认并继续
                        </button>
                    </div>
                    <div id="touchscreen-modal-loading" class="modal-loading" style="display: none;">
                        <div class="spinner"></div>
                        <p>正在处理，请稍候...</p>
                    </div>
                </div>
            </div>
        `;

        // 添加弹窗到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取弹窗元素
        const modal = document.getElementById('touchscreen-confirmation-modal');
        const textArea = document.getElementById('touchscreen-modal-confirmed-text');
        const charCount = document.getElementById('touchscreen-modal-char-count');
        const closeBtn = modal.querySelector('.touchscreen-confirmation-close-btn');
        const backBtn = document.getElementById('touchscreen-modal-back-btn');
        const resetBtn = document.getElementById('touchscreen-modal-reset-btn');
        const confirmBtn = document.getElementById('touchscreen-modal-confirm-btn');
        const loading = document.getElementById('touchscreen-modal-loading');
        const modalBody = modal.querySelector('.touchscreen-confirmation-modal-body');
        const modalFooter = modal.querySelector('.touchscreen-confirmation-modal-footer');

        // 更新字符计数
        function updateCharCount() {
            charCount.textContent = textArea.value.length;
        }

        // 监听文本变化
        textArea.addEventListener('input', updateCharCount);

        // 关闭弹窗函数
        function closeModal() {
            if (modal && modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }

        // 显示加载状态
        function showLoading() {
            modalBody.style.display = 'none';
            modalFooter.style.display = 'none';
            loading.style.display = 'block';
        }

        // 关闭按钮事件
        closeBtn.addEventListener('click', closeModal);

        // 点击弹窗外部关闭
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });

        // 取消按钮事件
        backBtn.addEventListener('click', function() {
            if (confirm('确定要取消处理吗？')) {
                showLoading();

                fetch('/api/confirm-text', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        task_id: taskId,
                        action: 'back'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    closeModal();
                    if (data.success) {
                        // 重置进度条和状态
                        progressContainer.style.display = 'none';
                        submitBtn.disabled = false;
                        progressText.textContent = '处理中...';
                        statusStage.textContent = '就绪';
                        progressBar.style.width = '0%';

                        // 显示取消成功通知
                        showNotification('已取消', '处理已取消，可以重新开始', true);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    closeModal();
                    showNotification('错误', '取消处理失败，请刷新页面', false);
                });
            }
        });

        // 重置按钮事件
        resetBtn.addEventListener('click', function() {
            if (confirm('确定要重置为原始内容吗？')) {
                textArea.value = ocrText;
                updateCharCount();
            }
        });

        // 确认按钮事件
        confirmBtn.addEventListener('click', function() {
            const confirmedText = textArea.value.trim();
            if (!confirmedText) {
                showNotification('提示', '文字内容不能为空！', false);
                return;
            }

            showLoading();

            fetch('/api/confirm-text', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: taskId,
                    action: 'confirm',
                    confirmed_text: confirmedText
                })
            })
            .then(response => response.json())
            .then(data => {
                closeModal();
                if (data.success) {
                    // 继续状态查询循环
                    startTouchscreenStatusCheck();
                    // 移除确认成功的通知弹窗，直接继续处理
                } else {
                    showNotification('错误', data.error || '处理失败，请重试', false);
                    // 恢复界面
                    progressContainer.style.display = 'none';
                    submitBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                closeModal();
                showNotification('错误', '网络错误，请重试', false);
                // 恢复界面
                progressContainer.style.display = 'none';
                submitBtn.disabled = false;
            });
        });

        // 显示弹窗
        console.log('显示触摸屏确认弹窗');
        modal.style.display = 'flex';

        // 确保弹窗在最顶层
        modal.style.zIndex = '9999';
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.width = '100vw';
        modal.style.height = '100vh';

        console.log('弹窗样式设置完成:', modal.style.display, modal.style.zIndex);
    };

    // 开始触摸屏状态检查循环
    window.startTouchscreenStatusCheck = function() {
        let lastStatus = '';
        let errorCount = 0;
        const statusCheckInterval = setInterval(function() {
            console.log('查询状态...');
            fetch('/status', {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-Timestamp': new Date().getTime()
                },
                credentials: 'same-origin',
                cache: 'no-store'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`状态查询错误: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('收到状态数据:', data);
                errorCount = 0;

                if (data.status !== lastStatus) {
                    console.log(`状态变化: ${lastStatus} -> ${data.status}`);
                    lastStatus = data.status;

                    statusStage.classList.add('status-changed');
                    setTimeout(() => {
                        statusStage.classList.remove('status-changed');
                    }, 1000);
                }

                progressText.textContent = `${data.message}`;
                statusStage.textContent = data.status;

                // 根据状态设置进度条
                switch(data.status) {
                    case '开始':
                        progressBar.style.width = '10%';
                        break;
                    case '上传':
                        progressBar.style.width = '30%';
                        break;
                    case 'OCR':
                    case 'OCR中':
                        progressBar.style.width = '50%';
                        break;
                    case 'LLM':
                    case 'LLM处理':
                        progressBar.style.width = '70%';
                        break;
                    case '生成文档':
                        progressBar.style.width = '90%';
                        break;
                    case '文档编辑':
                        progressBar.style.width = '85%';
                        clearInterval(statusCheckInterval);
                        if (data.edit_url) {
                            setTimeout(() => {
                                window.location.href = data.edit_url + '?touchscreen=true';
                            }, 500);
                        }
                        break;
                    case '完成':
                        progressBar.style.width = '100%';
                        clearInterval(statusCheckInterval);
                        setTimeout(() => {
                            window.location.href = '/result?touchscreen=true';
                        }, 1000);
                        break;
                }
            })
            .catch(error => {
                console.error('状态查询失败:', error);
                errorCount++;
                if (errorCount >= 5) {
                    clearInterval(statusCheckInterval);
                    showNotification('错误', '状态查询失败，请刷新页面重试', false);
                }
            });
        }, 2000);
    };
});