# -*- coding: utf-8 -*-
"""
脚本功能：
遍历指定目录（及其子目录）下的所有 .docx 文件，
查找并 **删除** Word 文档中存在的 {{...}} 形式的占位符。
修改会直接保存在原文件中。

**警告：此脚本会直接修改文件！请务必在运行前备份你的原始 .docx 文件。**

依赖库：
需要安装 python-docx 库： pip install python-docx

使用方法：
1. **备份！备份！备份！** 你的 Word 模板目录。
2. 确保已安装 python-docx 库。
3. 将此脚本保存为 .py 文件（例如 remove_docx_placeholders.py）。
4. 将 target_directory 修改为你的实际 Word 模板目录。
5. 确保目标目录中的所有 .docx 文件都已关闭。
6. 在终端中运行脚本（确保 conda 环境已激活）： python remove_docx_placeholders.py
7. 脚本将处理文件并报告哪些文件被修改了。
"""
import os
import re
import sys

try:
    from docx import Document
    from docx.opc.exceptions import PackageNotFoundError
except ImportError:
    print("错误：缺少必需的库 'python-docx'。")
    print("请在你的 conda 环境 'complaint' 中运行 'pip install python-docx' 或 'conda install -c conda-forge python-docx' 进行安装。")
    sys.exit(1) # 退出脚本

def remove_placeholders_in_paragraph(paragraphs, pattern):
    """在段落列表中查找并删除占位符"""
    modified = False
    for para in paragraphs:
        if pattern.search(para.text):
            inline = para.runs
            # 为了处理跨 run 的占位符，合并 run 的文本进行查找替换
            # 注意：这可能会丢失部分格式，尤其是占位符内部或边缘的格式
            full_text = "".join(r.text for r in inline)
            if pattern.search(full_text):
                new_text = pattern.sub('', full_text)
                # 清空段落现有内容，然后添加修改后的文本
                # 这种方法比较简单，但可能丢失原有格式
                for i in range(len(inline)-1, -1, -1):
                    p = para._p
                    p.remove(inline[i]._r)
                if new_text: # 如果替换后还有内容，则添加
                    para.add_run(new_text)
                modified = True
                # # 另一种尝试（可能效果不好）：直接修改 text 属性
                # original_text = para.text
                # para.text = pattern.sub('', original_text)
                # if para.text != original_text:
                #     modified = True
    return modified

def remove_docx_placeholders(directory):
    """
    遍历目录查找并删除 .docx 文件中的 {{...}} 占位符。

    Args:
        directory (str): 需要扫描和修改的目录路径。

    Returns:
        None: 直接修改文件并打印结果到控制台。
    """
    placeholder_pattern = re.compile(r'\{\{.*?\}\}')
    files_processed = 0
    files_modified = 0

    print("="*40)
    print(" ** 警告：此脚本将直接修改 .docx 文件！ **")
    print(" ** 请确保你已经备份了目标目录！ **")
    print(f" ** 目标目录: {directory} **")
    print("="*40)
    input("按 Enter 键继续，或按 Ctrl+C 取消...")

    print(f"\n开始扫描并处理目录: {directory} 中的 .docx 文件\n")

    for root, _, files in os.walk(directory):
        for filename in files:
            if filename.lower().endswith('.docx') and not filename.startswith('~$'): # 忽略 Word 临时文件
                file_path = os.path.join(root, filename)
                print(f"--- 正在处理文件: {file_path} ---")
                files_processed += 1
                file_modified_flag = False

                try:
                    document = Document(file_path)

                    # --- 处理文档主体部分 ---
                    # 处理段落
                    if remove_placeholders_in_paragraph(document.paragraphs, placeholder_pattern):
                        file_modified_flag = True

                    # 处理表格
                    for table in document.tables:
                        for row in table.rows:
                            for cell in row.cells:
                                if remove_placeholders_in_paragraph(cell.paragraphs, placeholder_pattern):
                                    file_modified_flag = True

                    # --- 处理页眉和页脚 ---
                    for section in document.sections:
                        # 处理默认页眉
                        if remove_placeholders_in_paragraph(section.header.paragraphs, placeholder_pattern):
                            file_modified_flag = True
                        # 处理默认页脚
                        if remove_placeholders_in_paragraph(section.footer.paragraphs, placeholder_pattern):
                            file_modified_flag = True
                        # 可能还有首页/奇偶页不同的页眉页脚，也一并处理
                        if section.different_first_page_header_footer:
                           if remove_placeholders_in_paragraph(section.first_page_header.paragraphs, placeholder_pattern):
                               file_modified_flag = True
                           if remove_placeholders_in_paragraph(section.first_page_footer.paragraphs, placeholder_pattern):
                               file_modified_flag = True
                        if document.settings.odd_and_even_pages_header_footer:
                            if remove_placeholders_in_paragraph(section.even_page_header.paragraphs, placeholder_pattern):
                                file_modified_flag = True
                            if remove_placeholders_in_paragraph(section.even_page_footer.paragraphs, placeholder_pattern):
                                file_modified_flag = True


                    # 如果文件内容被修改了，则保存文件
                    if file_modified_flag:
                        try:
                            document.save(file_path)
                            print(f"状态: 已修改并保存。")
                            files_modified += 1
                        except Exception as save_e:
                            print(f"错误: 保存文件 {file_path} 时出错: {save_e}")
                    else:
                        print(f"状态: 未找到占位符或无需修改。")

                except PackageNotFoundError:
                    print(f"错误: 文件不是有效的 Word (.docx) 格式，可能已损坏。已跳过。")
                except Exception as e:
                    print(f"错误: 处理文件时发生未知错误: {e}")
                finally:
                    print("-" * 20) # 文件处理分隔线


    print("\n处理完成。")
    print(f"总共处理了 {files_processed} 个 .docx 文件。")
    print(f"其中 {files_modified} 个文件被修改。")

# --- 配置 ---
# 设置你要扫描和修改的 Word 模板文件目录
target_directory = './'
# --- 配置结束 ---

# 检查目录是否存在
if not os.path.isdir(target_directory):
    print(f"错误：目录 '{target_directory}' 不存在或不是一个有效的目录。请检查路径是否正确。")
else:
    # 执行删除函数
    remove_docx_placeholders(target_directory)

