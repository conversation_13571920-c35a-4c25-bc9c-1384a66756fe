<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>要素式诉状规范通 - 批量处理</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/batch.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='lib/fontawesome/css/all.min.css') }}">
</head>
<body>
    <div class="batch-container">
        <!-- 导航栏 -->
        <nav class="batch-nav">
            <div class="batch-logo">
                <i class="fas fa-file-contract"></i>
                <span>要素式诉状规范通</span>
            </div>
            <div class="batch-nav-links">
                <a href="/" class="batch-nav-link">
                    <i class="fas fa-home"></i>
                    <span>单个处理</span>
                </a>
                <a href="/batch" class="batch-nav-link active">
                    <i class="fas fa-layer-group"></i>
                    <span>批量处理</span>
                </a>
                <!--
                <a href="/touchscreen" class="batch-nav-link">
                    <i class="fas fa-tablet-alt"></i>
                    <span>触摸屏版</span>
                </a>
                -->
            </div>
        </nav>

        <!-- 主要内容 -->
        <main class="batch-main">
            <div class="batch-header">
                <h1>批量文档处理</h1>
                <p>一次上传多个文档，自动识别并格式化起诉状内容</p>
            </div>

            <!-- 上传区域 -->
            <section class="batch-upload-section">
                <form id="batch-upload-form" enctype="multipart/form-data">
                    <div id="batch-drop-area">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>拖放文件到此处或</p>
                        <label for="batch-file-input" class="batch-file-label">选择文件</label>
                        <input type="file" id="batch-file-input" name="files[]" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display:none;">
                        <p id="batch-file-info">支持的格式：JPG, PNG, PDF, DOC, DOCX</p>
                    </div>
                    <div class="batch-options" style="display: none;">
                        <div class="batch-option">
                            <label for="batch-ocr-engine">
                                <i class="fas fa-cogs"></i>
                                <span>OCR引擎：</span>
                                <select name="ocr_engine" id="batch-ocr-engine" class="batch-select">
                                    <option value="baidu" {% if default_ocr_engine == 'baidu' %}selected{% endif %} selected>百度云OCR</option>
                                    <option value="tesseract" {% if default_ocr_engine == 'tesseract' %}selected{% endif %}>Tesseract OCR</option>
                                    <option value="olm" {% if default_ocr_engine == 'olm' %}selected{% endif %}>OLM OCR</option>
                                    <option value="paddleocr" {% if default_ocr_engine == 'paddle' or default_ocr_engine == 'paddleocr' %}selected{% endif %}>PaddleOCR</option>
                                </select>
                            </label>
                        </div>
                    </div>
                    <button type="submit" class="batch-submit-btn">
                        <i class="fas fa-play"></i>
                        开始处理
                    </button>
                </form>
            </section>

            <!-- 文件列表和进度 -->
            <section id="batch-files-container" class="batch-files-section">
                <div class="batch-progress-section">
                    <h2>处理进度</h2>
                    <div class="batch-progress-container">
                        <div id="batch-progress-bar" class="batch-progress"></div>
                        <span id="batch-progress-text">0%</span>
                    </div>
                    <div class="batch-stats">
                        <div class="batch-stat">
                            <i class="fas fa-file-alt"></i>
                            <span>总文件数: <strong id="total-files">0</strong></span>
                        </div>
                        <div class="batch-stat">
                            <i class="fas fa-check-circle"></i>
                            <span>已完成: <strong id="completed-files">0</strong></span>
                        </div>
                        <div class="batch-stat">
                            <i class="fas fa-spinner"></i>
                            <span>处理中: <strong id="processing-files">0</strong></span>
                        </div>
                        <div class="batch-stat">
                            <i class="fas fa-clock"></i>
                            <span>等待中: <strong id="pending-files">0</strong></span>
                        </div>
                        <div class="batch-stat">
                            <i class="fas fa-times-circle"></i>
                            <span>失败: <strong id="failed-files">0</strong></span>
                        </div>
                    </div>
                </div>

                <div class="batch-files-list">
                    <div class="batch-files-header">
                        <h2>文件列表</h2>
                        <button id="download-all-btn" class="batch-download-all-btn" disabled>
                            <i class="fas fa-download"></i>
                            批量下载
                        </button>
                    </div>
                    <div class="batch-table-container">
                        <table class="batch-table">
                            <thead>
                                <tr>
                                    <th width="5%">#</th>
                                    <th width="40%">文件名</th>
                                    <th width="15%">状态</th>
                                    <th width="30%">进度</th>
                                    <th width="10%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="file-list">
                                <!-- 文件列表将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        </main>

        <!-- 页脚 -->
        <footer class="batch-footer">
        </footer>
    </div>

    <script src="{{ url_for('static', filename='js/batch.js') }}"></script>
</body>
</html>
