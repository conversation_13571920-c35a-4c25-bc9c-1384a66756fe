<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>司法计算器 - 专业法律计算工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/shared-navigation.css') }}">
    <link rel="stylesheet" href="{{ url_for('calculator_static', filename='css/calculator.css') }}">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 侧边导航 -->
    <nav class="side-navigation">
        <div class="nav-buttons">
            <a href="/touchscreen" class="nav-button">
                <i class="fas fa-file-alt"></i>
                <span>诉状转换</span>
            </a>
            <a href="/calculator" class="nav-button active">
                <i class="fas fa-calculator"></i>
                <span>司法计算器</span>
            </a>
            <a href="/mediator" class="nav-button">
                <i class="fas fa-handshake"></i>
                <span>多元调解</span>
            </a>
        </div>
    </nav>

    <div class="container">
        <!-- 头部 -->
        <header>
            <h1><img src="{{ url_for('static', filename='images/P.R.C_Court_Insignia.svg.png') }}" class="header-logo" alt="法徽"> 司法计算器</h1>
            <p>计算结果仅供参考，实际收费以当地最新政策为准。</p>
        </header>

        <!-- 主导航 -->
        <nav class="main-nav">
            <button class="nav-btn active" data-module="litigation">
                <i class="fas fa-gavel"></i>
                <span>诉讼费计算</span>
            </button>
            <button class="nav-btn" data-module="compensation">
                <i class="fas fa-hand-holding-usd"></i>
                <span>赔偿计算</span>
            </button>
            <button class="nav-btn" data-module="financial">
                <i class="fas fa-chart-line"></i>
                <span>金融计算</span>
            </button>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 诉讼费计算模块 -->
            <div id="litigation-module" class="calculator-module active">
                <div class="module-header">
                    <h2><i class="fas fa-gavel"></i> 诉讼费计算</h2>
                    <p>根据最新诉讼费收费标准计算各类案件费用</p>
                </div>

                <div class="calculator-tabs">
                    <button class="tab-btn active" data-tab="case-fee">案件受理费</button>
                    <button class="tab-btn" data-tab="preservation-fee">诉讼保全费</button>
                    <button class="tab-btn" data-tab="execution-fee">执行申请费</button>
                </div>

                <!-- 案件受理费计算 -->
                <div id="case-fee" class="tab-content active">
                    <div class="input-section">
                        <div class="input-group">
                            <label for="case-type">案件类型</label>
                            <select id="case-type" class="touch-input">
                                <option value="property">财产案件</option>
                                <option value="non-property">非财产案件</option>
                                <option value="labor">劳动争议案件</option>
                                <option value="administrative">行政案件</option>
                                <option value="intellectual">知识产权案件</option>
                            </select>
                        </div>

                        <div class="input-group" id="amount-group">
                            <label for="case-amount">标的额（元）</label>
                            <input type="number" id="case-amount" class="touch-input" placeholder="请输入案件标的额" min="0" step="0.01">
                        </div>

                        <div class="input-group" id="non-property-group" style="display: none;">
                            <label for="non-property-type">非财产案件类型</label>
                            <select id="non-property-type" class="touch-input">
                                <option value="divorce">离婚案件</option>
                                <option value="other-marriage">其他婚姻家庭案件</option>
                                <option value="intellectual-property">知识产权案件</option>
                                <option value="labor-dispute">劳动争议案件</option>
                                <option value="other">其他非财产案件</option>
                            </select>
                        </div>

                        <button class="calculate-btn" onclick="calculateCaseFee()">
                            <i class="fas fa-calculator"></i> 计算受理费
                        </button>
                    </div>

                    <div class="result-section" id="case-fee-result" style="display: none;">
                        <h3><i class="fas fa-receipt"></i> 计算结果</h3>
                        <div class="result-item">
                            <span class="result-label">案件受理费：</span>
                            <span class="result-value" id="case-fee-amount">0.00 元</span>
                        </div>
                        <div class="calculation-details" id="case-fee-details">
                            <!-- 计算详情将在这里显示 -->
                        </div>
                        <div class="result-actions">
                            <button class="action-btn" onclick="clearResult('case-fee')">
                                <i class="fas fa-refresh"></i> 重新计算
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 诉讼保全费计算 -->
                <div id="preservation-fee" class="tab-content">
                    <div class="input-section">
                        <div class="input-group">
                            <label for="preservation-type">保全类型</label>
                            <select id="preservation-type" class="touch-input">
                                <option value="property">财产保全</option>
                                <option value="evidence">证据保全</option>
                                <option value="behavior">行为保全</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label for="preservation-amount">保全标的额（元）</label>
                            <input type="number" id="preservation-amount" class="touch-input" placeholder="请输入保全标的额" min="0" step="0.01">
                        </div>

                        <button class="calculate-btn" onclick="calculatePreservationFee()">
                            <i class="fas fa-calculator"></i> 计算保全费
                        </button>
                    </div>

                    <div class="result-section" id="preservation-fee-result" style="display: none;">
                        <h3><i class="fas fa-receipt"></i> 计算结果</h3>
                        <div class="result-item">
                            <span class="result-label">诉讼保全费：</span>
                            <span class="result-value" id="preservation-fee-amount">0.00 元</span>
                        </div>
                        <div class="calculation-details" id="preservation-fee-details">
                            <!-- 计算详情将在这里显示 -->
                        </div>
                        <div class="result-actions">
                            <button class="action-btn" onclick="clearResult('preservation-fee')">
                                <i class="fas fa-refresh"></i> 重新计算
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 执行申请费计算 -->
                <div id="execution-fee" class="tab-content">
                    <div class="input-section">
                        <div class="input-group">
                            <label for="execution-amount">执行标的额（元）</label>
                            <input type="number" id="execution-amount" class="touch-input" placeholder="请输入执行标的额" min="0" step="0.01">
                        </div>

                        <button class="calculate-btn" onclick="calculateExecutionFee()">
                            <i class="fas fa-calculator"></i> 计算执行费
                        </button>
                    </div>

                    <div class="result-section" id="execution-fee-result" style="display: none;">
                        <h3><i class="fas fa-receipt"></i> 计算结果</h3>
                        <div class="result-item">
                            <span class="result-label">执行申请费：</span>
                            <span class="result-value" id="execution-fee-amount">0.00 元</span>
                        </div>
                        <div class="calculation-details" id="execution-fee-details">
                            <!-- 计算详情将在这里显示 -->
                        </div>
                        <div class="result-actions">
                            <button class="action-btn" onclick="clearResult('execution-fee')">
                                <i class="fas fa-refresh"></i> 重新计算
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 赔偿计算模块 -->
            <div id="compensation-module" class="calculator-module">
                <div class="module-header">
                    <h2><i class="fas fa-hand-holding-usd"></i> 赔偿计算</h2>
                    <p>交通事故、工伤、人身损害等赔偿标准计算</p>
                </div>

                <div class="calculator-tabs">
                    <button class="tab-btn active" data-tab="traffic-compensation">交通事故赔偿</button>
                    <button class="tab-btn" data-tab="work-injury">工伤赔偿</button>
                    <button class="tab-btn" data-tab="personal-injury">人身损害赔偿</button>
                </div>

                <!-- 交通事故赔偿计算 -->
                <div id="traffic-compensation" class="tab-content active">
                    <div class="input-section">
                        <div class="input-group">
                            <label for="accident-province">事故发生地</label>
                            <select id="accident-province" class="touch-input">
                                <option value="beijing">北京市</option>
                                <option value="shanghai">上海市</option>
                                <option value="tianjin">天津市</option>
                                <option value="chongqing">重庆市</option>
                                <option value="guangdong">广东省</option>
                                <option value="jiangsu">江苏省</option>
                                <option value="zhejiang">浙江省</option>
                                <option value="shandong">山东省</option>
                                <option value="fujian">福建省</option>
                                <option value="liaoning">辽宁省</option>
                                <option value="hunan">湖南省</option>
                                <option value="hubei">湖北省</option>
                                <option value="anhui">安徽省</option>
                                <option value="jiangxi">江西省</option>
                                <option value="hainan">海南省</option>
                                <option value="hebei">河北省</option>
                                <option value="sichuan">四川省</option>
                                <option value="shaanxi">陕西省</option>
                                <option value="ningxia">宁夏回族自治区</option>
                                <option value="shanxi">山西省</option>
                                <option value="henan">河南省</option>
                                <option value="xizang">西藏自治区</option>
                                <option value="jilin">吉林省</option>
                                <option value="heilongjiang">黑龙江省</option>
                                <option value="guangxi">广西壮族自治区</option>
                                <option value="xinjiang">新疆维吾尔自治区</option>
                                <option value="qinghai">青海省</option>
                                <option value="yunnan">云南省</option>
                                <option value="guizhou">贵州省</option>
                                <option value="gansu">甘肃省</option>
                                <option value="neimenggu">内蒙古自治区</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label for="victim-type">受害人类型</label>
                            <select id="victim-type" class="touch-input">
                                <option value="urban">城镇居民</option>
                                <option value="rural">农村居民</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label for="victim-age">受害人年龄</label>
                            <input type="number" id="victim-age" class="touch-input" placeholder="请输入年龄" min="0" max="120">
                        </div>

                        <div class="compensation-items">
                            <h4>赔偿项目</h4>
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="medical-fee" checked>
                                    <span class="checkmark"></span>
                                    医疗费
                                </label>
                                <input type="number" id="medical-amount" class="touch-input small" placeholder="金额" min="0" step="0.01">
                            </div>

                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="lost-work-fee">
                                    <span class="checkmark"></span>
                                    误工费
                                </label>
                                <input type="number" id="lost-work-days" class="touch-input small" placeholder="天数" min="0">
                                <input type="number" id="daily-income" class="touch-input small" placeholder="日收入" min="0" step="0.01">
                            </div>

                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="nursing-fee">
                                    <span class="checkmark"></span>
                                    护理费
                                </label>
                                <input type="number" id="nursing-days" class="touch-input small" placeholder="天数" min="0">
                            </div>

                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="nutrition-fee">
                                    <span class="checkmark"></span>
                                    营养费
                                </label>
                                <input type="number" id="nutrition-days" class="touch-input small" placeholder="天数" min="0">
                            </div>

                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="disability-compensation">
                                    <span class="checkmark"></span>
                                    残疾赔偿金
                                </label>
                                <select id="disability-level" class="touch-input small">
                                    <option value="0">请选择伤残等级</option>
                                    <option value="1">一级伤残</option>
                                    <option value="2">二级伤残</option>
                                    <option value="3">三级伤残</option>
                                    <option value="4">四级伤残</option>
                                    <option value="5">五级伤残</option>
                                    <option value="6">六级伤残</option>
                                    <option value="7">七级伤残</option>
                                    <option value="8">八级伤残</option>
                                    <option value="9">九级伤残</option>
                                    <option value="10">十级伤残</option>
                                </select>
                            </div>

                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="death-compensation">
                                    <span class="checkmark"></span>
                                    死亡赔偿金
                                </label>
                            </div>
                        </div>

                        <button class="calculate-btn" onclick="calculateTrafficCompensation()">
                            <i class="fas fa-calculator"></i> 计算赔偿金额
                        </button>
                    </div>

                    <div class="result-section" id="traffic-compensation-result" style="display: none;">
                        <h3><i class="fas fa-receipt"></i> 计算结果</h3>
                        <div class="result-item">
                            <span class="result-label">赔偿总额：</span>
                            <span class="result-value" id="traffic-compensation-amount">0.00 元</span>
                        </div>
                        <div class="calculation-details" id="traffic-compensation-details">
                            <!-- 计算详情将在这里显示 -->
                        </div>
                        <div class="result-actions">
                            <button class="action-btn" onclick="clearResult('traffic-compensation')">
                                <i class="fas fa-refresh"></i> 重新计算
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 工伤赔偿计算 -->
                <div id="work-injury" class="tab-content">
                    <div class="input-section">
                        <div class="input-group">
                            <label for="injury-province">工伤发生地</label>
                            <select id="injury-province" class="touch-input">
                                <option value="beijing">北京市</option>
                                <option value="shanghai">上海市</option>
                                <option value="tianjin">天津市</option>
                                <option value="chongqing">重庆市</option>
                                <option value="guangdong">广东省</option>
                                <option value="jiangsu">江苏省</option>
                                <option value="zhejiang">浙江省</option>
                                <option value="shandong">山东省</option>
                                <option value="fujian">福建省</option>
                                <option value="liaoning">辽宁省</option>
                                <option value="hunan">湖南省</option>
                                <option value="hubei">湖北省</option>
                                <option value="anhui">安徽省</option>
                                <option value="jiangxi">江西省</option>
                                <option value="hainan">海南省</option>
                                <option value="hebei">河北省</option>
                                <option value="sichuan">四川省</option>
                                <option value="shaanxi">陕西省</option>
                                <option value="ningxia">宁夏回族自治区</option>
                                <option value="shanxi">山西省</option>
                                <option value="henan">河南省</option>
                                <option value="xizang">西藏自治区</option>
                                <option value="jilin">吉林省</option>
                                <option value="heilongjiang">黑龙江省</option>
                                <option value="guangxi">广西壮族自治区</option>
                                <option value="xinjiang">新疆维吾尔自治区</option>
                                <option value="qinghai">青海省</option>
                                <option value="yunnan">云南省</option>
                                <option value="guizhou">贵州省</option>
                                <option value="gansu">甘肃省</option>
                                <option value="neimenggu">内蒙古自治区</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label for="monthly-wage">月平均工资（元）</label>
                            <input type="number" id="monthly-wage" class="touch-input" placeholder="请输入月平均工资" min="0" step="0.01">
                        </div>

                        <div class="input-group">
                            <label for="injury-type">工伤类型</label>
                            <select id="injury-type" class="touch-input">
                                <option value="disability">伤残</option>
                                <option value="death">工亡</option>
                            </select>
                        </div>

                        <div class="input-group" id="injury-disability-group">
                            <label for="injury-disability-level">伤残等级</label>
                            <select id="injury-disability-level" class="touch-input">
                                <option value="1">一级伤残</option>
                                <option value="2">二级伤残</option>
                                <option value="3">三级伤残</option>
                                <option value="4">四级伤残</option>
                                <option value="5">五级伤残</option>
                                <option value="6">六级伤残</option>
                                <option value="7">七级伤残</option>
                                <option value="8">八级伤残</option>
                                <option value="9">九级伤残</option>
                                <option value="10">十级伤残</option>
                            </select>
                        </div>

                        <button class="calculate-btn" onclick="calculateWorkInjuryCompensation()">
                            <i class="fas fa-calculator"></i> 计算工伤赔偿
                        </button>
                    </div>

                    <div class="result-section" id="work-injury-result" style="display: none;">
                        <h3><i class="fas fa-receipt"></i> 计算结果</h3>
                        <div class="result-item">
                            <span class="result-label">工伤赔偿总额：</span>
                            <span class="result-value" id="work-injury-amount">0.00 元</span>
                        </div>
                        <div class="calculation-details" id="work-injury-details">
                            <!-- 计算详情将在这里显示 -->
                        </div>
                        <div class="result-actions">
                            <button class="action-btn" onclick="clearResult('work-injury')">
                                <i class="fas fa-refresh"></i> 重新计算
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 人身损害赔偿计算 -->
                <div id="personal-injury" class="tab-content">
                    <div class="input-section">
                        <div class="input-group">
                            <label for="personal-injury-province">损害发生地</label>
                            <select id="personal-injury-province" class="touch-input">
                                <option value="beijing">北京市</option>
                                <option value="shanghai">上海市</option>
                                <option value="tianjin">天津市</option>
                                <option value="chongqing">重庆市</option>
                                <option value="guangdong">广东省</option>
                                <option value="jiangsu">江苏省</option>
                                <option value="zhejiang">浙江省</option>
                                <option value="shandong">山东省</option>
                                <option value="fujian">福建省</option>
                                <option value="liaoning">辽宁省</option>
                                <option value="hunan">湖南省</option>
                                <option value="hubei">湖北省</option>
                                <option value="anhui">安徽省</option>
                                <option value="jiangxi">江西省</option>
                                <option value="hainan">海南省</option>
                                <option value="hebei">河北省</option>
                                <option value="sichuan">四川省</option>
                                <option value="shaanxi">陕西省</option>
                                <option value="ningxia">宁夏回族自治区</option>
                                <option value="shanxi">山西省</option>
                                <option value="henan">河南省</option>
                                <option value="xizang">西藏自治区</option>
                                <option value="jilin">吉林省</option>
                                <option value="heilongjiang">黑龙江省</option>
                                <option value="guangxi">广西壮族自治区</option>
                                <option value="xinjiang">新疆维吾尔自治区</option>
                                <option value="qinghai">青海省</option>
                                <option value="yunnan">云南省</option>
                                <option value="guizhou">贵州省</option>
                                <option value="gansu">甘肃省</option>
                                <option value="neimenggu">内蒙古自治区</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label for="personal-victim-type">受害人类型</label>
                            <select id="personal-victim-type" class="touch-input">
                                <option value="urban">城镇居民</option>
                                <option value="rural">农村居民</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label for="personal-victim-age">受害人年龄</label>
                            <input type="number" id="personal-victim-age" class="touch-input" placeholder="请输入年龄" min="0" max="120">
                        </div>

                        <div class="input-group">
                            <label for="personal-medical-fee">医疗费（元）</label>
                            <input type="number" id="personal-medical-fee" class="touch-input" placeholder="请输入医疗费" min="0" step="0.01">
                        </div>

                        <button class="calculate-btn" onclick="calculatePersonalInjuryCompensation()">
                            <i class="fas fa-calculator"></i> 计算人身损害赔偿
                        </button>
                    </div>

                    <div class="result-section" id="personal-injury-result" style="display: none;">
                        <h3><i class="fas fa-receipt"></i> 计算结果</h3>
                        <div class="result-item">
                            <span class="result-label">人身损害赔偿总额：</span>
                            <span class="result-value" id="personal-injury-amount">0.00 元</span>
                        </div>
                        <div class="calculation-details" id="personal-injury-details">
                            <!-- 计算详情将在这里显示 -->
                        </div>
                        <div class="result-actions">
                            <button class="action-btn" onclick="clearResult('personal-injury')">
                                <i class="fas fa-refresh"></i> 重新计算
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 金融计算模块 -->
            <div id="financial-module" class="calculator-module">
                <div class="module-header">
                    <h2><i class="fas fa-chart-line"></i> 金融计算</h2>
                    <p>利息、违约金、律师费等金融相关计算</p>
                </div>

                <div class="calculator-tabs">
                    <button class="tab-btn active" data-tab="interest-calculator">利息计算</button>
                    <button class="tab-btn" data-tab="delay-interest-calculator">迟延履行利息</button>
                    <button class="tab-btn" data-tab="penalty-calculator">违约金计算</button>
                    <button class="tab-btn" data-tab="lawyer-fee-calculator">律师费计算</button>
                </div>

                <!-- 利息计算 -->
                <div id="interest-calculator" class="tab-content active">
                    <div class="input-section">
                        <div class="input-group">
                            <label for="interest-principal">本金（元）</label>
                            <input type="number" id="interest-principal" class="touch-input" placeholder="请输入本金金额" min="0" step="0.01">
                        </div>

                        <div class="input-group">
                            <label for="interest-rate">年利率（%）</label>
                            <input type="number" id="interest-rate" class="touch-input" placeholder="请输入年利率" min="0" max="36" step="0.01">
                        </div>

                        <div class="input-group">
                            <label for="interest-start-date">起始日期</label>
                            <input type="date" id="interest-start-date" class="touch-input">
                        </div>

                        <div class="input-group">
                            <label for="interest-end-date">结束日期</label>
                            <input type="date" id="interest-end-date" class="touch-input">
                        </div>

                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="compound-interest">
                                <span class="checkmark"></span>
                                复利计算
                            </label>
                        </div>

                        <button class="calculate-btn" onclick="calculateInterest()">
                            <i class="fas fa-calculator"></i> 计算利息
                        </button>
                    </div>

                    <div class="result-section" id="interest-result" style="display: none;">
                        <h3><i class="fas fa-receipt"></i> 计算结果</h3>
                        <div class="result-item">
                            <span class="result-label">利息总额：</span>
                            <span class="result-value" id="interest-amount">0.00 元</span>
                        </div>
                        <div class="calculation-details" id="interest-details">
                            <!-- 计算详情将在这里显示 -->
                        </div>
                        <div class="result-actions">
                            <button class="action-btn" onclick="clearResult('interest')">
                                <i class="fas fa-refresh"></i> 重新计算
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 迟延履行利息计算 -->
                <div id="delay-interest-calculator" class="tab-content">
                    <div class="input-section">
                        <div class="input-group">
                            <label for="delay-principal">债务本金（元）</label>
                            <input type="number" id="delay-principal" class="touch-input" placeholder="请输入债务本金" min="0" step="0.01">
                        </div>

                        <div class="input-group">
                            <label for="delay-start-date">迟延履行起始日期</label>
                            <input type="date" id="delay-start-date" class="touch-input">
                        </div>

                        <div class="input-group">
                            <label for="delay-end-date">计算截止日期</label>
                            <input type="date" id="delay-end-date" class="touch-input">
                        </div>

                        <button class="calculate-btn" onclick="calculateDelayInterest()">
                            <i class="fas fa-calculator"></i> 计算迟延履行利息
                        </button>
                    </div>

                    <div class="result-section" id="delay-interest-result" style="display: none;">
                        <h3><i class="fas fa-receipt"></i> 计算结果</h3>
                        <div class="result-item">
                            <span class="result-label">迟延履行利息：</span>
                            <span class="result-value" id="delay-interest-amount">0.00 元</span>
                        </div>
                        <div class="calculation-details" id="delay-interest-details">
                            <!-- 计算详情将在这里显示 -->
                        </div>
                        <div class="result-actions">
                            <button class="action-btn" onclick="clearResult('delay-interest')">
                                <i class="fas fa-refresh"></i> 重新计算
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 违约金计算 -->
                <div id="penalty-calculator" class="tab-content">
                    <div class="input-section">
                        <div class="input-group">
                            <label for="penalty-principal">合同金额（元）</label>
                            <input type="number" id="penalty-principal" class="touch-input" placeholder="请输入合同金额" min="0" step="0.01">
                        </div>

                        <div class="input-group">
                            <label for="penalty-rate">违约金比例（%）</label>
                            <input type="number" id="penalty-rate" class="touch-input" placeholder="请输入违约金比例" min="0" max="100" step="0.01">
                        </div>

                        <div class="input-group">
                            <label for="penalty-days">违约天数</label>
                            <input type="number" id="penalty-days" class="touch-input" placeholder="请输入违约天数" min="0">
                        </div>

                        <button class="calculate-btn" onclick="calculatePenalty()">
                            <i class="fas fa-calculator"></i> 计算违约金
                        </button>
                    </div>

                    <div class="result-section" id="penalty-result" style="display: none;">
                        <h3><i class="fas fa-receipt"></i> 计算结果</h3>
                        <div class="result-item">
                            <span class="result-label">违约金：</span>
                            <span class="result-value" id="penalty-amount">0.00 元</span>
                        </div>
                        <div class="calculation-details" id="penalty-details">
                            <!-- 计算详情将在这里显示 -->
                        </div>
                        <div class="result-actions">
                            <button class="action-btn" onclick="clearResult('penalty')">
                                <i class="fas fa-refresh"></i> 重新计算
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 律师费计算 -->
                <div id="lawyer-fee-calculator" class="tab-content">
                    <div class="input-section">
                        <div class="input-group">
                            <label for="lawyer-case-type">案件类型</label>
                            <select id="lawyer-case-type" class="touch-input">
                                <option value="civil">民事案件</option>
                                <option value="criminal">刑事案件</option>
                                <option value="administrative">行政案件</option>
                                <option value="arbitration">仲裁案件</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label for="lawyer-amount">标的额（元）</label>
                            <input type="number" id="lawyer-amount" class="touch-input" placeholder="请输入案件标的额" min="0" step="0.01">
                        </div>

                        <button class="calculate-btn" onclick="calculateLawyerFee()">
                            <i class="fas fa-calculator"></i> 计算律师费
                        </button>
                    </div>

                    <div class="result-section" id="lawyer-fee-result" style="display: none;">
                        <h3><i class="fas fa-receipt"></i> 计算结果</h3>
                        <div class="result-item">
                            <span class="result-label">律师费：</span>
                            <span class="result-value" id="lawyer-fee-amount">0.00 元</span>
                        </div>
                        <div class="calculation-details" id="lawyer-fee-details">
                            <!-- 计算详情将在这里显示 -->
                        </div>
                        <div class="result-actions">
                            <button class="action-btn" onclick="clearResult('lawyer-fee')">
                                <i class="fas fa-refresh"></i> 重新计算
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->

    <script src="{{ url_for('calculator_static', filename='js/litigation-fees.js') }}"></script>
    <script src="{{ url_for('calculator_static', filename='js/compensation.js') }}"></script>
    <script src="{{ url_for('calculator_static', filename='js/financial.js') }}"></script>
    <script src="{{ url_for('calculator_static', filename='js/calculator.js') }}"></script>
</body>
</html>
