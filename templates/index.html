<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>要素式诉状规范通 - 单个处理</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='lib/fontawesome/css/all.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='lib/sortablejs/Sortable.min.css') }}">
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <nav class="main-nav">
            <div class="nav-logo">
                <i class="fas fa-file-contract"></i>
                <span>要素式诉状规范通</span>
            </div>
            <div class="nav-links">
                <a href="/" class="nav-link active">
                    <i class="fas fa-home"></i>
                    <span>单个处理</span>
                </a>
                <a href="/batch" class="nav-link">
                    <i class="fas fa-layer-group"></i>
                    <span>批量处理</span>
                </a>
                <!--
                <a href="/touchscreen" class="nav-link">
                    <i class="fas fa-tablet-alt"></i>
                    <span>触摸屏版</span>
                </a>
                -->
            </div>
        </nav>

        <header>
            <h1>单个诉状处理</h1>
            <p>上传一个或多个文件，按顺序合并并分析诉状内容</p>
        </header>

        <main>
            <div class="upload-container">
                <form id="upload-form" action="/upload" method="post" enctype="multipart/form-data">
                    <div class="file-drop-area" id="drop-area">
                        <i class="fas fa-cloud-upload-alt drop-icon"></i>
                        <p>拖放文件到这里或</p>
                        <label for="file-input" class="file-input-label">
                            <i class="fas fa-file-upload"></i>
                            选择文件
                        </label>
                        <input type="file" id="file-input" name="files[]" accept=".jpg,.jpeg,.png,.pdf,.doc,.docx" multiple>
                        <p class="file-info" id="file-info">支持的格式：JPG, PNG, PDF, DOC, DOCX</p>
                        <p class="file-info">支持多文件上传，将按照顺序合并处理</p>

                        <!-- 新增: 扫码上传按钮 -->
                        <div class="qr-upload-container">
                            <button type="button" id="qr-upload-btn" class="qr-upload-btn">
                                <i class="fas fa-qrcode"></i>
                                扫码上传
                            </button>
                            <p class="qr-upload-info">使用手机扫描二维码快速上传图片</p>
                        </div>
                    </div>

                    <!-- 文件列表和排序 -->
                    <div class="file-list-container" id="file-list-container" style="display: none;">
                        <h3><i class="fas fa-list"></i> 文件列表 <small>(可拖动排序)</small></h3>
                        <div id="file-list" class="sortable-file-list"></div>
                    </div>

                    <div class="options">
                        <div class="option-group" style="display: none;">
                            <label>
                                <i class="fas fa-cogs"></i>
                                OCR引擎：
                            </label>
                            <select name="ocr_engine" id="ocr-engine">
                                <option value="baidu" {% if default_ocr_engine == 'baidu' %}selected{% endif %} selected>百度云OCR</option>
                                <option value="tesseract" {% if default_ocr_engine == 'tesseract' %}selected{% endif %}>Tesseract OCR</option>
                                <option value="olm" {% if default_ocr_engine == 'olm' %}selected{% endif %}>OLM OCR</option>
                                <option value="paddleocr" {% if default_ocr_engine == 'paddle' or default_ocr_engine == 'paddleocr' %}selected{% endif %}>PaddleOCR</option>
                            </select>
                        </div>

                        <div class="option-group">
                            <label>
                                <i class="fas fa-sitemap"></i>
                                分类模式：
                            </label>
                            <select id="classification-mode" name="classification_mode">
                                <option value="auto" selected>自动分类</option>
                                <option value="manual">手动选择</option>
                            </select>
                        </div>

                        <div class="option-group" id="manual-template-container" style="display: none;">
                            <label>
                                <i class="fas fa-file-alt"></i>
                                文档模板：
                            </label>
                            <select id="template-select" name="template_type">
                                <!-- 模板选项将通过JavaScript动态加载 -->
                            </select>
                        </div>
                    </div>

                    <button type="submit" id="submit-btn" class="submit-btn">
                        <i class="fas fa-play"></i>
                        开始处理
                    </button>
                </form>
            </div>

            <!-- 二维码扫描对话框 -->
            <div id="qr-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-qrcode"></i> 扫码上传</h3>
                        <span class="close-btn">&times;</span>
                    </div>
                    <div class="modal-scroll-container">
                        <div class="modal-body">
                            <div class="qr-code-container">
                                <img id="qr-code-image" src="" alt="二维码">
                            </div>
                            <div class="qr-code-instructions">
                                <p>1. 使用手机扫描上方二维码</p>
                                <p>2. 在手机上选择要上传的图片</p>
                                <p>3. 确认上传后，图片将自动出现在此页面</p>
                            </div>
                            <div class="session-info">
                                <p>会话ID: <span id="session-id">{{ session_id }}</span></p>
                                <p>连接状态: <span id="connection-status">等待连接...</span></p>
                            </div>
                        </div>
                        <div class="mobile-files-container" id="mobile-files-container" style="display: none;">
                            <h4>已接收的文件</h4>
                            <div id="mobile-files-list" class="mobile-files-list"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他处理方式 -->
            <div class="alternative-methods-container">
                <h3><i class="fas fa-tools"></i> 其他处理方式</h3>

                <!-- 在线表单编辑 -->
                <div class="method-card">
                    <div class="method-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="method-content">
                        <h4>在线表单编辑</h4>
                        <p>使用在线表单直接填写起诉状信息，无需上传图片，支持实时预览</p>
                        <a href="/web-forms" class="method-btn">
                            <i class="fas fa-external-link-alt"></i>
                            开始在线编辑
                        </a>
                    </div>
                </div>

                <!-- 下载模板 -->
                <div class="method-card">
                    <div class="method-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="method-content">
                        <h4>下载模板</h4>
                        <p>下载Word模板文件进行手动填写</p>
                        <div class="template-select-group">
                            <select id="template-download-select" class="template-select">
                                <option value="" disabled selected>请选择模板</option>
                                <!-- 模板选项将通过JavaScript动态加载 -->
                            </select>
                            <button id="download-template-btn" class="download-template-btn" disabled>
                                <i class="fas fa-download"></i>
                                下载模板
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="progress-container" id="progress-container" style="display: none;">
                <div class="progress-bar">
                    <div class="progress" id="progress-bar"></div>
                </div>
                <p id="progress-text">处理中...</p>
                <div class="status-info">
                    <p><i class="fas fa-info-circle"></i> 当前状态: <span id="status-stage">就绪</span></p>
                </div>
            </div>
        </main>

        <footer>
        </footer>
    </div>

    <script src="{{ url_for('static', filename='lib/sortablejs/Sortable.min.js') }}"></script>
    <script src="{{ url_for('static', filename='lib/socket.io.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- 新增CSS样式 -->
    <style>
        /* 扫码上传按钮样式 */
        .qr-upload-container {
            margin-top: 15px;
            text-align: center;
        }

        .qr-upload-btn {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .qr-upload-btn:hover {
            background-color: #45a049;
        }

        .qr-upload-info {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }

        /* 模态对话框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            overflow-y: auto;
        }

        .modal-content {
            position: relative;
            background-color: #fefefe;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 400px;
            max-height: 90vh; /* 最大高度为视口高度的90% */
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            animation: modalFadeIn 0.3s;
            display: flex;
            flex-direction: column; /* 使用弹性布局，便于内容区域自适应 */
            overflow: hidden; /* 隐藏溢出内容 */
        }

        @keyframes modalFadeIn {
            from {opacity: 0; transform: translateY(-20px);}
            to {opacity: 1; transform: translateY(0);}
        }

        .modal-header {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
        }

        .close-btn {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close-btn:hover {
            color: #333;
        }

        /* 新增滚动容器 */
        .modal-scroll-container {
            flex: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .modal-body {
            padding: 20px;
            flex-grow: 0; /* 不再自动增长 */
            overflow-y: visible; /* 移除滚动 */
        }

        .qr-code-container {
            text-align: center;
            padding: 10px;
            margin-bottom: 15px;
        }

        .qr-code-container img {
            max-width: 200px;
            border: 1px solid #ddd;
            padding: 5px;
        }

        .qr-code-instructions {
            margin: 15px 0;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }

        .qr-code-instructions p {
            margin: 5px 0;
            font-size: 0.9rem;
        }

        .session-info {
            font-size: 0.8rem;
            color: #666;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }

        .session-info p {
            margin: 3px 0;
        }

        /* 移动端上传的文件列表 */
        .mobile-files-container {
            margin-top: 0;
            padding: 15px 20px;
            border-top: 1px solid #eee;
            flex-shrink: 0; /* 防止被压缩 */
            overflow-y: visible; /* 移除独立滚动 */
        }

        .mobile-files-container h4 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 1rem;
            color: #333;
            position: sticky;
            top: 0;
            background-color: #fff;
            padding: 5px 0;
            z-index: 1;
        }

        .mobile-files-list {
            overflow-y: visible; /* 改为visible，因为父容器已经有滚动条 */
            max-height: none; /* 移除高度限制，让父容器控制 */
        }

        .mobile-file-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #eee;
        }

        .mobile-file-item:last-child {
            border-bottom: none;
        }

        .mobile-file-icon {
            margin-right: 10px;
            color: #2196F3;
        }

        .mobile-file-info {
            flex: 1;
        }

        .mobile-file-name {
            font-size: 0.9rem;
            font-weight: bold;
        }

        .mobile-file-size {
            font-size: 0.8rem;
            color: #666;
        }

        /* 模态对话框响应式样式 */
        @media screen and (max-width: 480px) {
            .modal-content {
                width: 95%;
                margin: 5% auto;
            }

            .qr-code-container img {
                max-width: 180px;
            }
        }

        @media screen and (max-height: 600px) {
            .modal-content {
                margin: 2% auto;
                max-height: 96vh;
            }

            .modal-body {
                padding: 15px;
            }

            .qr-code-container {
                padding: 5px;
                margin-bottom: 10px;
            }

            .qr-code-instructions {
                margin: 10px 0;
                padding: 8px;
            }
        }

        /* 改进视觉反馈 */
        .mobile-file-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }

        .mobile-file-item:hover {
            background-color: #f9f9f9;
        }

        /* 添加文件计数器 */
        .files-counter {
            position: absolute;
            top: 15px;
            right: 15px;
            background-color: #2196F3;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        /* 其他处理方式样式 */
        .alternative-methods-container {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .alternative-methods-container h3 {
            margin-top: 0;
            margin-bottom: 20px;
            color: #333;
            font-size: 1.2rem;
        }

        .method-card {
            display: flex;
            align-items: center;
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .method-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .method-card:last-child {
            margin-bottom: 0;
        }

        .method-icon {
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
        }

        .method-icon i {
            font-size: 24px;
            color: white;
        }

        .method-content {
            flex: 1;
        }

        .method-content h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 1.1rem;
        }

        .method-content p {
            margin: 0 0 15px 0;
            color: #666;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .method-btn {
            display: inline-block;
            padding: 8px 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.9rem;
            transition: opacity 0.2s;
        }

        .method-btn:hover {
            opacity: 0.9;
            color: white;
            text-decoration: none;
        }

        .template-select-group {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .template-select {
            flex: 1;
            min-width: 200px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .download-template-btn {
            padding: 8px 16px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.2s;
        }

        .download-template-btn:hover:not(:disabled) {
            background: #218838;
        }

        .download-template-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .method-card {
                flex-direction: column;
                text-align: center;
            }

            .method-icon {
                margin-right: 0;
                margin-bottom: 15px;
            }

            .template-select-group {
                flex-direction: column;
                width: 100%;
            }

            .template-select {
                width: 100%;
                min-width: auto;
            }

            .download-template-btn {
                width: 100%;
            }
        }
    </style>
</body>
</html>
