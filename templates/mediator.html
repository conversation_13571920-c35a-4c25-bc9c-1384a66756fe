<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <title>多元调解 - 公众号服务</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/shared-navigation.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='lib/fontawesome/css/all.min.css') }}">
    <style>
        /* 多元调解页面专用样式 */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            overflow-x: hidden;
        }

        .container {
            margin-left: 220px;
            padding: 30px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        header {
            text-align: center;
            margin-bottom: 40px;
        }

        header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header-logo {
            width: 50px;
            height: 50px;
        }

        header p {
            color: #7f8c8d;
            font-size: 1.2em;
            margin: 0;
        }

        .mediator-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
            border: 3px solid #4a90e2;
        }

        .mediator-card h2 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .mediator-card h2 i {
            color: #4a90e2;
        }

        .qr-code-container {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 2px dashed #4a90e2;
        }

        .qr-code-image {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .instructions {
            margin-top: 30px;
            text-align: left;
        }

        .instructions h3 {
            color: #2c3e50;
            font-size: 1.3em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .instructions h3 i {
            color: #4a90e2;
        }

        .instructions ul {
            list-style: none;
            padding: 0;
        }

        .instructions li {
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .instructions li:last-child {
            border-bottom: none;
        }

        .instructions li i {
            color: #4a90e2;
            width: 20px;
        }

        .service-info {
            background: #e8f4fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #4a90e2;
        }

        .service-info h4 {
            color: #2c3e50;
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .service-info p {
            color: #5a6c7d;
            margin: 0;
            line-height: 1.6;
        }

        /* 响应式设计 - 平板 */
        @media (max-width: 768px) {
            .container {
                margin-left: 0;
                margin-top: 0;
                padding: 20px;
            }

            header h1 {
                font-size: 2em;
            }

            .mediator-card {
                padding: 30px 20px;
            }

            .mediator-card h2 {
                font-size: 1.5em;
            }
        }

        /* 响应式设计 - 手机 */
        @media (max-width: 480px) {
            .container {
                padding: 15px;
            }

            header h1 {
                font-size: 1.8em;
                flex-direction: column;
                gap: 10px;
            }

            .header-logo {
                width: 40px;
                height: 40px;
            }

            .mediator-card {
                padding: 25px 15px;
            }

            .mediator-card h2 {
                font-size: 1.3em;
                flex-direction: column;
                gap: 5px;
            }

            .instructions h3 {
                font-size: 1.1em;
            }
        }
    </style>
</head>
<body oncontextmenu="return false;">
    <!-- 侧边导航 -->
    <nav class="side-navigation">
        <div class="nav-buttons">
            <a href="/touchscreen" class="nav-button">
                <i class="fas fa-file-alt"></i>
                <span>诉状转换</span>
            </a>
            <a href="/calculator" class="nav-button">
                <i class="fas fa-calculator"></i>
                <span>司法计算器</span>
            </a>
            <a href="/mediator" class="nav-button active">
                <i class="fas fa-handshake"></i>
                <span>多元调解</span>
            </a>
        </div>
    </nav>

    <div class="container">
        <header>
            <h1>
                <img src="{{ url_for('static', filename='images/P.R.C_Court_Insignia.svg.png') }}" class="header-logo" alt="法徽">
                多元调解平台
            </h1>
            <p>便民服务 · 高效调解 · 化解纠纷</p>
        </header>

        <main>
            <div class="mediator-card">
                <h2>
                    <i class="fas fa-handshake"></i>
                    多元调解服务
                </h2>

                <div class="qr-code-container">
                    <img src="{{ url_for('static', filename='images/party-1011.png') }}" alt="多元调解公众号二维码" class="qr-code-image">
                </div>

                <div class="instructions">
                    <h3>
                        <i class="fas fa-mobile-alt"></i>
                        使用指南
                    </h3>
                    <ul>
                        <li>
                            <i class="fas fa-qrcode"></i>
                            使用微信扫描上方二维码
                        </li>
                        <li>
                            <i class="fas fa-user-plus"></i>
                            关注"多元调解"公众号
                        </li>
                        <li>
                            <i class="fas fa-comments"></i>
                            在线申请调解服务
                        </li>
                        <li>
                            <i class="fas fa-clock"></i>
                            等待调解员联系安排
                        </li>
                    </ul>
                </div>

                <div class="service-info">
                    <h4>
                        <i class="fas fa-info-circle"></i>
                        服务说明
                    </h4>
                    <p>
                        多元调解平台为您提供专业的纠纷调解服务，包括民事纠纷、商事纠纷、劳动争议等各类案件的调解。
                        我们拥有经验丰富的调解员团队，致力于为当事人提供高效、便民的纠纷解决方案。
                    </p>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
