<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端上传 - 要素式诉状规范通</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='lib/fontawesome/css/all.min.css') }}">
    <style>
        /* 移动端专用样式 */
        body {
            padding: 10px;
            background-color: #f5f5f5;
        }
        .mobile-container {
            max-width: 100%;
            padding: 15px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            font-size: 1.5rem;
            margin-bottom: 5px;
            color: #333;
        }
        .header p {
            font-size: 0.9rem;
            color: #666;
        }
        .mobile-upload-container {
            margin: 15px 0;
        }
        .mobile-file-input {
            display: block;
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 2px dashed #2196F3;
            border-radius: 8px;
            text-align: center;
            background-color: #e3f2fd;
        }
        .preview-container {
            margin-top: 15px;
            display: none;
        }
        .preview-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        .preview-item {
            position: relative;
            border-radius: 8px;
            border: 1px solid #ddd;
            overflow: hidden;
            aspect-ratio: 1;
        }
        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .preview-item .remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background-color: rgba(0,0,0,0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .preview-info {
            margin-top: 10px;
            font-size: 0.9rem;
            color: #666;
            text-align: center;
            padding: 5px;
            background-color: #f0f0f0;
            border-radius: 4px;
        }
        .upload-controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .source-btn {
            flex: 1;
            padding: 12px;
            background-color: #9C27B0;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 0.9rem;
            cursor: pointer;
        }
        .source-btn:hover {
            background-color: #7B1FA2;
        }
        .upload-btn {
            display: block;
            width: 100%;
            padding: 12px;
            margin-top: 15px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            font-size: 1rem;
            cursor: pointer;
        }
        .upload-btn:disabled {
            background-color: #bbb;
            cursor: not-allowed;
        }
        .upload-btn i {
            margin-right: 8px;
        }
        .session-info {
            margin-top: 20px;
            padding: 10px;
            border-radius: 8px;
            background-color: #f0f0f0;
            font-size: 0.85rem;
            color: #555;
        }
        .success-message {
            display: none;
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f5e9;
            border-radius: 8px;
            color: #2e7d32;
        }
        .upload-progress {
            margin-top: 10px;
            display: none;
        }
        .progress-bar-outer {
            width: 100%;
            height: 8px;
            background-color: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-bar-inner {
            height: 100%;
            width: 0;
            background-color: #4CAF50;
            transition: width 0.3s ease;
        }
        .progress-text {
            font-size: 0.8rem;
            color: #666;
            text-align: center;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <div class="header">
            <h1><i class="fas fa-file-upload"></i> 移动端上传</h1>
            <p>请选择要上传的图片文件</p>
        </div>

        <div class="mobile-upload-container">
            <form id="mobile-upload-form">
                <!-- 隐藏实际的文件输入框 -->
                <input type="file" id="mobile-file" name="files[]" class="mobile-file-input" accept="image/*" multiple style="display: none;">

                <!-- 自定义上传按钮 -->
                <div class="upload-controls">
                    <button type="button" class="source-btn" id="camera-btn">
                        <i class="fas fa-camera"></i> 使用相机
                    </button>
                    <button type="button" class="source-btn" id="gallery-btn">
                        <i class="fas fa-images"></i> 从相册选择
                    </button>
                </div>

                <div class="preview-container" id="preview-container">
                    <div class="preview-grid" id="preview-grid"></div>
                    <div class="preview-info" id="preview-info"></div>
                </div>

                <div class="upload-progress" id="upload-progress">
                    <div class="progress-bar-outer">
                        <div class="progress-bar-inner" id="progress-bar"></div>
                    </div>
                    <div class="progress-text" id="progress-text">准备上传...</div>
                </div>

                <button type="submit" class="upload-btn" id="mobile-upload-btn" disabled>
                    <i class="fas fa-upload"></i> 上传图片
                </button>
            </form>
        </div>

        <div class="success-message" id="success-message">
            <i class="fas fa-check-circle" style="font-size: 2rem; color: #2e7d32; margin-bottom: 10px;"></i>
            <h3>上传成功！</h3>
            <p>图片已成功传送到电脑端</p>
        </div>

        <div class="session-info">
            <p>会话ID: <span id="session-id">{{ session_id }}</span></p>
            <p>已连接到: <span id="device-info">PC端</span></p>
        </div>
    </div>

    <script src="{{ url_for('static', filename='lib/socket.io.min.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM元素
            const fileInput = document.getElementById('mobile-file');
            const cameraBtn = document.getElementById('camera-btn');
            const galleryBtn = document.getElementById('gallery-btn');
            const previewContainer = document.getElementById('preview-container');
            const previewGrid = document.getElementById('preview-grid');
            const previewInfo = document.getElementById('preview-info');
            const uploadBtn = document.getElementById('mobile-upload-btn');
            const uploadForm = document.getElementById('mobile-upload-form');
            const successMessage = document.getElementById('success-message');
            const sessionId = document.getElementById('session-id').textContent;
            const uploadProgress = document.getElementById('upload-progress');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');

            // 存储选择的文件
            let selectedFiles = [];

            // 相机按钮点击事件
            cameraBtn.addEventListener('click', function() {
                fileInput.setAttribute('capture', 'camera');
                fileInput.click();
            });

            // 相册按钮点击事件
            galleryBtn.addEventListener('click', function() {
                fileInput.removeAttribute('capture');
                fileInput.click();
            });

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 处理文件选择
            fileInput.addEventListener('change', function() {
                if (this.files && this.files.length > 0) {
                    // 将新选择的文件添加到选择列表中
                    const newFiles = Array.from(this.files);

                    // 添加新选择的文件
                    newFiles.forEach(file => {
                        if (!selectedFiles.some(f => f.name === file.name && f.size === file.size)) {
                            selectedFiles.push(file);
                        }
                    });

                    // 更新预览
                    updatePreview();
                }
            });

            // 更新预览
            function updatePreview() {
                if (selectedFiles.length > 0) {
                    // 清空预览网格
                    previewGrid.innerHTML = '';

                    // 添加每个文件的预览
                    selectedFiles.forEach((file, index) => {
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            const previewItem = document.createElement('div');
                            previewItem.className = 'preview-item';

                            previewItem.innerHTML = `
                                <img src="${e.target.result}" alt="${file.name}">
                                <button type="button" class="remove-btn" data-index="${index}">
                                    <i class="fas fa-times"></i>
                                </button>
                            `;

                            previewGrid.appendChild(previewItem);

                            // 添加删除按钮事件
                            const removeBtn = previewItem.querySelector('.remove-btn');
                            removeBtn.addEventListener('click', function() {
                                const idx = parseInt(this.getAttribute('data-index'));
                                removeFile(idx);
                            });
                        };

                        reader.readAsDataURL(file);
                    });

                    // 更新文件信息
                    const totalSize = selectedFiles.reduce((total, file) => total + file.size, 0);
                    previewInfo.textContent = `已选择 ${selectedFiles.length} 个文件，总大小: ${formatFileSize(totalSize)}`;

                    // 显示预览容器
                    previewContainer.style.display = 'block';

                    // 启用上传按钮
                    uploadBtn.disabled = false;
                } else {
                    // 隐藏预览容器
                    previewContainer.style.display = 'none';

                    // 禁用上传按钮
                    uploadBtn.disabled = true;
                }
            }

            // 删除文件
            function removeFile(index) {
                selectedFiles.splice(index, 1);
                updatePreview();
            }

            // 创建socket连接
            const socket = io({
                reconnection: true,         // 启用自动重连
                reconnectionAttempts: 5,    // 最多尝试重连5次
                reconnectionDelay: 1000,    // 初始重连延迟1秒
                reconnectionDelayMax: 5000, // 最大重连延迟5秒
                timeout: 20000              // 连接超时时间20秒
            });

            // 显示连接状态
            const deviceInfo = document.getElementById('device-info');

            // 处理表单提交
            uploadForm.addEventListener('submit', function(e) {
                e.preventDefault();

                if (selectedFiles.length > 0) {
                    uploadBtn.disabled = true;
                    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 上传中...';
                    uploadProgress.style.display = 'block';

                    // 上传所有文件
                    uploadFiles(0);
                }
            });

            // 递归上传文件
            function uploadFiles(index) {
                if (index >= selectedFiles.length) {
                    // 全部上传完成
                    progressBar.style.width = '100%';
                    progressText.textContent = '全部上传完成！';

                    // 显示成功消息
                    successMessage.style.display = 'block';
                    uploadBtn.innerHTML = '<i class="fas fa-check"></i> 上传成功';

                    // 3秒后重置表单
                    setTimeout(function() {
                        uploadForm.reset();
                        selectedFiles = [];
                        previewContainer.style.display = 'none';
                        successMessage.style.display = 'none';
                        uploadProgress.style.display = 'none';
                        uploadBtn.innerHTML = '<i class="fas fa-upload"></i> 上传图片';
                        uploadBtn.disabled = true;
                    }, 3000);

                    return;
                }

                const file = selectedFiles[index];

                // 更新进度
                const progress = ((index + 1) / selectedFiles.length) * 100;
                progressBar.style.width = `${progress}%`;
                progressText.textContent = `正在处理第 ${index + 1}/${selectedFiles.length} 个文件...`;

                // 对图片进行压缩处理
                compressImage(file)
                    .then(compressedDataUrl => {
                        progressText.textContent = `正在上传第 ${index + 1}/${selectedFiles.length} 个文件...`;

                        // 创建上传超时处理
                        let uploadTimeout;
                        let responseReceived = false;

                        // 设置超时处理
                        uploadTimeout = setTimeout(function() {
                            if (!responseReceived) {
                                console.log('上传响应超时，重试...');
                                progressText.textContent = `第 ${index + 1} 个文件响应超时，重试...`;

                                // 清除旧的监听器
                                socket.off('upload_response');

                                // 重新尝试上传当前文件
                                uploadFiles(index);
                            }
                        }, 10000); // 10秒超时

                        // 监听上传响应
                        socket.once('upload_response', function(data) {
                            responseReceived = true;
                            clearTimeout(uploadTimeout);

                            if (data.success) {
                                // 继续上传下一个文件
                                uploadFiles(index + 1);
                            } else {
                                // 上传失败
                                alert('上传失败: ' + data.message);
                                uploadBtn.innerHTML = '<i class="fas fa-upload"></i> 重试上传';
                                uploadBtn.disabled = false;
                            }
                        });

                        // 发送图片数据到服务器
                        socket.emit('mobile_upload', {
                            session_id: sessionId,
                            filename: file.name,
                            file_type: file.type,
                            file_size: file.size,
                            data: compressedDataUrl,
                            total_files: selectedFiles.length,
                            current_file: index + 1
                        });
                    })
                    .catch(error => {
                        console.error('图片处理错误:', error);
                        progressText.textContent = `处理第 ${index + 1} 个文件时出错`;

                        // 跳过这个文件，继续上传下一个
                        uploadFiles(index + 1);
                    });
            }

            // 图片压缩处理
            function compressImage(file) {
                return new Promise((resolve, reject) => {
                    // 如果不是图片文件或小于1MB，则不压缩
                    if (!file.type.startsWith('image/') || file.size < 1024 * 1024) {
                        const reader = new FileReader();
                        reader.onload = e => resolve(e.target.result);
                        reader.onerror = reject;
                        reader.readAsDataURL(file);
                        return;
                    }

                    // 创建图片对象
                    const img = new Image();
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        img.src = e.target.result;

                        img.onload = function() {
                            // 计算压缩比例，目标大小约为1MB
                            let maxSize = 1200; // 最大尺寸
                            let quality = 0.8;  // 初始质量

                            if (file.size > 3 * 1024 * 1024) { // 大于3MB
                                maxSize = 1000;
                                quality = 0.6;
                            } else if (file.size > 5 * 1024 * 1024) { // 大于5MB
                                maxSize = 800;
                                quality = 0.5;
                            }

                            // 计算新尺寸
                            let width = img.width;
                            let height = img.height;

                            if (width > height && width > maxSize) {
                                height = Math.round(height * maxSize / width);
                                width = maxSize;
                            } else if (height > maxSize) {
                                width = Math.round(width * maxSize / height);
                                height = maxSize;
                            }

                            // 创建Canvas进行压缩和旋转
                            const canvas = document.createElement('canvas');
                            // 交换宽高以适应90度旋转
                            canvas.width = height;
                            canvas.height = width;

                            const ctx = canvas.getContext('2d');
                            // 应用90度顺时针旋转
                            ctx.save();
                            ctx.translate(canvas.width/2, canvas.height/2);
                            ctx.rotate(Math.PI/2); // 90度顺时针
                            ctx.translate(-width/2, -height/2);
                            ctx.drawImage(img, 0, 0, width, height);
                            ctx.restore();

                            // 转换为DataURL
                            const compressedDataUrl = canvas.toDataURL(file.type, quality);

                            // 检查压缩效果
                            const compressionRatio = Math.round((1 - compressedDataUrl.length / e.target.result.length) * 100);
                            console.log(`图片压缩率: ${compressionRatio}%`);

                            resolve(compressedDataUrl);
                        };

                        img.onerror = reject;
                    };

                    reader.onerror = reject;
                    reader.readAsDataURL(file);
                });
            }

            // 连接状态管理
            socket.on('connect', function() {
                console.log('已连接到服务器');
                deviceInfo.textContent = 'PC端 (已连接)';
                deviceInfo.style.color = '#4CAF50';
                socket.emit('join_session', { session_id: sessionId, client_type: 'mobile' });
            });

            socket.on('connect_error', function(error) {
                console.error('连接错误:', error);
                deviceInfo.textContent = 'PC端 (连接错误)';
                deviceInfo.style.color = '#F44336';
            });

            socket.on('disconnect', function() {
                console.log('与服务器断开连接');
                deviceInfo.textContent = 'PC端 (已断开)';
                deviceInfo.style.color = '#FF9800';
            });

            socket.on('reconnect', function(attemptNumber) {
                console.log(`重连成功，尝试次数: ${attemptNumber}`);
                deviceInfo.textContent = 'PC端 (已重连)';
                deviceInfo.style.color = '#4CAF50';
                socket.emit('join_session', { session_id: sessionId, client_type: 'mobile' });
            });

            socket.on('reconnect_attempt', function(attemptNumber) {
                console.log(`尝试重连，第 ${attemptNumber} 次`);
                deviceInfo.textContent = `PC端 (重连中 ${attemptNumber}/5)`;
                deviceInfo.style.color = '#FF9800';
            });

            socket.on('reconnect_failed', function() {
                console.log('重连失败');
                deviceInfo.textContent = 'PC端 (重连失败)';
                deviceInfo.style.color = '#F44336';

                // 显示重连按钮
                const sessionInfo = document.querySelector('.session-info');
                const reconnectBtn = document.createElement('button');
                reconnectBtn.textContent = '手动重连';
                reconnectBtn.style.marginTop = '10px';
                reconnectBtn.style.padding = '5px 10px';
                reconnectBtn.addEventListener('click', function() {
                    socket.connect();
                    this.textContent = '正在重连...';
                    this.disabled = true;
                });
                sessionInfo.appendChild(reconnectBtn);
            });
        });
    </script>
</body>
</html>