<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档编辑预览 - {{ template_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 1.8rem;
        }
        
        .header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
        
        .preview-edit-notice {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border: none;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .preview-edit-notice h3 {
            color: #d63384;
            margin-bottom: 10px;
        }
        
        .preview-edit-notice p {
            color: #6f42c1;
            margin: 0;
        }
        
        .document-form {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .form-section {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            background: #fafbfc;
        }
        
        .section-title {
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }
        
        .form-field {
            margin-bottom: 20px;
        }
        
        .form-field label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            display: block;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 6px;
            padding: 10px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .auto-resize {
            min-height: 80px;
            resize: vertical;
        }
        
        .preview-edit-actions {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
            text-align: center;
        }
        
        .preview-edit-actions .btn {
            margin: 0 10px;
            padding: 12px 25px;
            font-weight: 600;
            border-radius: 25px;
            border: none;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .preview-edit-actions .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading .spinner-border {
            width: 3rem;
            height: 3rem;
        }
        
        .alert {
            border-radius: 8px;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .preview-edit-actions .btn {
                display: block;
                width: 100%;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1><i class="fas fa-edit"></i> 文档编辑预览</h1>
            <p>模板：{{ template_name }}</p>
        </div>
        
        <!-- 加载状态 -->
        <div id="loading" class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-3">正在处理，请稍候...</p>
        </div>
        
        <!-- 错误提示 -->
        <div id="errorAlert" class="alert alert-danger" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorMessage"></span>
        </div>
        
        <!-- 成功提示 -->
        <div id="successAlert" class="alert alert-success" style="display: none;">
            <i class="fas fa-check-circle"></i>
            <span id="successMessage"></span>
        </div>
        
        <!-- 表单内容 -->
        <div id="formContainer">
            {{ form_html|safe }}
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        const previewId = '{{ preview_id }}';
        const formData = {{ form_data|safe }};
        
        // DOM元素
        const loading = document.getElementById('loading');
        const errorAlert = document.getElementById('errorAlert');
        const successAlert = document.getElementById('successAlert');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeForm();
            bindEvents();
        });
        
        function initializeForm() {
            // 自动调整文本域高度
            const textareas = document.querySelectorAll('.auto-resize');
            textareas.forEach(textarea => {
                autoResize(textarea);
                textarea.addEventListener('input', () => autoResize(textarea));
            });
        }
        
        function bindEvents() {
            // 预览按钮
            const previewBtn = document.getElementById('previewBtn');
            if (previewBtn) {
                previewBtn.addEventListener('click', previewDocument);
            }
            
            // 生成按钮
            const generateBtn = document.getElementById('generateBtn');
            if (generateBtn) {
                generateBtn.addEventListener('click', generateDocument);
            }
            
            // 跳过编辑按钮
            const skipEditBtn = document.getElementById('skipEditBtn');
            if (skipEditBtn) {
                skipEditBtn.addEventListener('click', skipEdit);
            }
        }
        
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        }
        
        function collectFormData() {
            const form = document.getElementById('documentForm');
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // 处理复选框
            const checkboxes = form.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                data[checkbox.name] = checkbox.checked;
            });
            
            return data;
        }
        
        function showLoading() {
            loading.style.display = 'block';
            hideAlerts();
        }
        
        function hideLoading() {
            loading.style.display = 'none';
        }
        
        function showError(message) {
            errorMessage.textContent = message;
            errorAlert.style.display = 'block';
            successAlert.style.display = 'none';
            hideLoading();
        }
        
        function showSuccess(message) {
            successMessage.textContent = message;
            successAlert.style.display = 'block';
            errorAlert.style.display = 'none';
            hideLoading();
        }
        
        function hideAlerts() {
            errorAlert.style.display = 'none';
            successAlert.style.display = 'none';
        }
        
        async function previewDocument() {
            showLoading();
            
            try {
                const data = collectFormData();
                
                const response = await fetch('/api/preview-document', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        preview_id: previewId,
                        form_data: data
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 显示预览（这里可以扩展为模态框显示）
                    showSuccess('预览生成成功！');
                } else {
                    showError(result.error || '预览失败');
                }
            } catch (error) {
                showError('预览时发生错误: ' + error.message);
            }
        }
        
        async function generateDocument() {
            showLoading();
            
            try {
                const data = collectFormData();
                
                const response = await fetch('/api/generate-from-preview', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        preview_id: previewId,
                        form_data: data
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess('文档生成成功！正在下载...');
                    // 下载文件
                    window.location.href = result.download_url;
                } else {
                    showError(result.error || '生成文档失败');
                }
            } catch (error) {
                showError('生成文档时发生错误: ' + error.message);
            }
        }
        
        async function skipEdit() {
            if (!confirm('确定要跳过编辑，直接使用AI分析结果生成文档吗？')) {
                return;
            }
            
            showLoading();
            
            try {
                const response = await fetch(`/api/skip-preview/${previewId}`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess('文档生成成功！正在下载...');
                    // 下载文件
                    window.location.href = result.download_url;
                } else {
                    showError(result.error || '生成文档失败');
                }
            } catch (error) {
                showError('生成文档时发生错误: ' + error.message);
            }
        }
    </script>
</body>
</html>
