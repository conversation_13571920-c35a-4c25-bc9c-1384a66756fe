<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>处理结果 - 要素式诉状规范通</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/shared-navigation.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='lib/fontawesome/css/all.min.css') }}">
    {% if processing %}
    <meta http-equiv="refresh" content="2">
    <style>
        .processing-container {
            text-align: center;
            padding: 20px;
            margin-bottom: 20px;
        }
        .progress-bar {
            width: 100%;
            background-color: #f0f0f0;
            border-radius: 5px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-bar-fill {
            height: 20px;
            background-color: #4caf50;
            width: {% if status and status.progress %}{{ status.progress }}{% else %}50{% endif %}%;
            transition: width 0.5s ease-in-out;
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #09f;
            animation: spin 1s linear infinite;
            margin: 10px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    {% endif %}
    <style>
        /* 调整容器样式以适应侧边导航 */
        .container {
            margin-left: 230px; /* 为侧边导航留出空间 */
            margin-right: 30px;
            margin-top: 30px;
            margin-bottom: 30px;
            background-color: #fff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 0 30px 30px 30px;
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 0 15px 15px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 侧边导航 -->
    <nav class="side-navigation">
        <div class="nav-buttons">
            <a href="/touchscreen" class="nav-button">
                <i class="fas fa-file-alt"></i>
                <span>诉状转换</span>
            </a>
            <a href="/calculator" class="nav-button">
                <i class="fas fa-calculator"></i>
                <span>司法计算器</span>
            </a>
            <a href="/mediator" class="nav-button">
                <i class="fas fa-handshake"></i>
                <span>多元调解</span>
            </a>
        </div>
    </nav>

    <div class="container">

        <header>
            <h1>要素式诉状规范通</h1>
            <p>处理结果</p>
        </header>

        <main>
            <div class="result-container">
                {% if processing %}
                <div class="processing-container">
                    <h2>处理中...</h2>
                    <div class="spinner"></div>
                    <p>{% if status and status.message %}{{ status.message }}{% else %}正在处理您的文件{% endif %}</p>
                    <div class="progress-bar">
                        <div class="progress-bar-fill"></div>
                    </div>
                    <p class="small">页面将自动刷新更新状态</p>
                </div>
                {% else %}
                <div class="result-header">
                    <h2>处理完成</h2>
                    {% if error %}
                    <div class="error-message">
                        <p>处理过程中发生错误：{{ error }}</p>
                    </div>
                    {% else %}
                    <div class="success-message">
                        <p>文件处理成功！</p>
                    </div>
                    <div class="download-section">
                        <a href="{{ download_url }}" class="download-btn">下载Word文档</a>
                    </div>
                    {% endif %}
                <div class="back-section">
                    <a href="/" class="back-btn">返回首页</a>
                </div>
                </div>

                {% if not error %}
                <div class="result-content">
                    <h3>识别结果预览</h3>
                    <div class="preview-container">
                        {% for key, value in result.items() %}
                        <div class="preview-item">
                            <strong>{{ key }}:</strong>
                            {% if value is mapping %}
                                <p>
                                {% for subkey, subvalue in value.items() %}
                                    {{ subkey }}: {{ subvalue }}<br>
                                {% endfor %}
                                </p>
                            {% elif value is sequence and value is not string %}
                                <ol>
                                    {% for item in value %}
                                        <li>{{ item }}</li>
                                    {% endfor %}
                                </ol>
                            {% else %}
                                <p>{{ value }}</p>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>


                </div>
                {% endif %}
                {% endif %}

            </div>
        </main>

        <footer>
            <p>&copy; 2025 阜阳联通</p>
        </footer>
    </div>
</body>
</html>
