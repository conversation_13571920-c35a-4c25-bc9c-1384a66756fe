<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <title>要素式诉状规范通 - 触摸屏版</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/shared-navigation.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/touchscreen.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='lib/fontawesome/css/all.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='lib/sortablejs/Sortable.min.css') }}">
</head>
<body oncontextmenu="return false;">
    <!-- 侧边导航 -->
    <nav class="side-navigation">
        <div class="nav-buttons">
            <a href="/touchscreen" class="nav-button active">
                <i class="fas fa-file-alt"></i>
                <span>诉状转换</span>
            </a>
            <a href="/calculator" class="nav-button">
                <i class="fas fa-calculator"></i>
                <span>司法计算器</span>
            </a>
            <a href="/mediator" class="nav-button">
                <i class="fas fa-handshake"></i>
                <span>多元调解</span>
            </a>
        </div>
    </nav>

    <div class="container">
        <header>
            <h1><img src="{{ url_for('static', filename='images/P.R.C_Court_Insignia.svg.png') }}" class="header-logo" alt="法徽"> 要素式诉状规范通</h1>
            <p>请拍摄诉状文件进行处理</p>
        </header>

        <main>
            <!-- 模板选择区域 - 卡片式布局 -->
            <div class="template-selection-container">
                <h2><i class="fas fa-file-alt"></i> 第一步：选择模板类型</h2>
                <p>请选择您要生成的起诉状类型，然后再上传文档进行转换</p>

                <div class="template-layout">
                    <!-- 右侧：常用模板 -->
                    <div class="common-templates-section">
                        <h3><i class="fas fa-star"></i> 常用模板</h3>
                        <div class="template-cards-grid">
                            <div class="template-card common-template" data-template="第一批/民事/2-离婚纠纷起诉状.docx">
                                <div class="card-icon">
                                    <i class="fas fa-heart-broken"></i>
                                </div>
                                <div class="card-content">
                                    <h4>离婚纠纷</h4>
                                    <p>婚姻关系解除相关纠纷</p>
                                </div>
                            </div>

                            <div class="template-card common-template" data-template="第一批/民事/7-机动车交通事故责任纠纷起诉状.docx">
                                <div class="card-icon">
                                    <i class="fas fa-car-crash"></i>
                                </div>
                                <div class="card-content">
                                    <h4>交通事故</h4>
                                    <p>机动车交通事故责任纠纷</p>
                                </div>
                            </div>

                            <div class="template-card common-template" data-template="第一批/民事/3-买卖合同纠纷起诉状.docx">
                                <div class="card-icon">
                                    <i class="fas fa-handshake"></i>
                                </div>
                                <div class="card-content">
                                    <h4>买卖合同</h4>
                                    <p>买卖合同相关纠纷</p>
                                </div>
                            </div>

                            <div class="template-card common-template" data-template="第一批/民事/1-民间借贷纠纷起诉状.docx">
                                <div class="card-icon">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <div class="card-content">
                                    <h4>民间借贷</h4>
                                    <p>民间借贷纠纷</p>
                                </div>
                            </div>

                            <div class="template-card common-template" data-template="第一批/民事/8-劳动争议起诉状.docx">
                                <div class="card-icon">
                                    <i class="fas fa-briefcase"></i>
                                </div>
                                <div class="card-content">
                                    <h4>劳动争议</h4>
                                    <p>劳动关系相关纠纷</p>
                                </div>
                            </div>

                            <div class="template-card common-template" data-template="第一批/民事/4-金融借款合同纠纷起诉状.docx">
                                <div class="card-icon">
                                    <i class="fas fa-university"></i>
                                </div>
                                <div class="card-content">
                                    <h4>金融借款</h4>
                                    <p>金融借款合同纠纷</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 左侧：完整模板分类 -->
                    <div class="full-templates-section">
                        <h3><i class="fas fa-list"></i> 全部模板</h3>

                        <!-- 批次选择 -->
                        <div class="batch-selector">
                            <div class="batch-tabs">
                                <button type="button" class="batch-tab active" data-batch="第一批">
                                    <i class="fas fa-star"></i>
                                    第一批
                                </button>
                                <button type="button" class="batch-tab" data-batch="第二批">
                                    <i class="fas fa-plus"></i>
                                    第二批
                                </button>
                            </div>
                        </div>

                        <!-- 分类卡片容器 -->
                        <div class="categories-container" id="categories-container">
                            <!-- 动态生成分类卡片 -->
                        </div>

                        <!-- 模板卡片容器 -->
                        <div class="templates-container" id="templates-container" style="display: none;">
                            <div class="back-button">
                                <button type="button" id="back-to-categories" class="btn-back">
                                    <i class="fas fa-arrow-left"></i>
                                    返回分类
                                </button>
                            </div>
                            <div class="template-cards-grid" id="template-cards-grid">
                                <!-- 动态生成模板卡片 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 选择确认区域 -->
                <div class="template-confirm" id="template-confirm" style="display: none;">
                    <div class="selected-template-info">
                        <i class="fas fa-check-circle"></i>
                        <span>已选择模板：<strong id="selected-template-name"></strong></span>
                    </div>

                    <div class="template-actions">
                        <button type="button" id="continue-to-upload-btn" class="action-btn upload-btn">
                            <i class="fas fa-upload"></i>
                            扫描转换
                        </button>
                        <button type="button" id="print-selected-template-btn" class="action-btn print-btn">
                            <i class="fas fa-print"></i>
                            打印模板
                        </button>
                        <button type="button" id="direct-edit-template-btn" class="action-btn edit-btn">
                            <i class="fas fa-edit"></i>
                            线上编辑
                        </button>

                    </div>
                </div>

                <!-- 隐藏的表单字段 -->
                <form id="template-form" style="display: none;">
                    <input type="hidden" id="selected-template-path" name="template_type">
                </form>
            </div>

            <!-- 文档上传区域 - 只有选择模板后才显示 -->
            <div class="upload-container" id="upload-container" style="display: none;">
                <h2><i class="fas fa-camera"></i> 第二步：上传文档</h2>
                <p>请拍摄或上传需要转换的诉状文件</p>

                <form id="upload-form" action="/upload" method="post" enctype="multipart/form-data">
                    <div class="camera-area" id="camera-area">
                        <div class="camera-placeholder" id="camera-placeholder">

                            <div class="camera-buttons">
                                <button type="button" id="start-camera-btn" class="camera-btn">
                                    <i class="fas fa-camera"></i>
                                    开始拍照
                                </button>
                                <button type="button" id="qr-upload-btn" class="camera-btn qr-btn">
                                    <i class="fas fa-qrcode"></i>
                                    扫码上传
                                </button>
                            </div>
                            <p class="file-info" id="file-info">支持多次拍照或手机扫码上传，按顺序进行处理</p>
                        </div>

                        <div id="camera-view" class="camera-view" style="display: none;">
                            <video id="video" autoplay playsinline></video>
                            <div class="camera-controls">
                                <button type="button" id="capture-btn" class="capture-btn">
                                    <i class="fas fa-camera"></i>
                                    拍照
                                </button>
                                <button type="button" id="cancel-camera-btn" class="cancel-btn">
                                    <i class="fas fa-times"></i>
                                    取消
                                </button>
                            </div>
                        </div>

                        <div id="photo-preview" class="photo-preview" style="display: none;">
                            <canvas id="canvas"></canvas>
                            <div class="preview-controls">
                                <button type="button" id="save-photo-btn" class="save-btn">
                                    <i class="fas fa-check"></i>
                                    使用此照片
                                </button>
                                <button type="button" id="retake-btn" class="retake-btn">
                                    <i class="fas fa-redo"></i>
                                    重新拍摄
                                </button>
                            </div>
                        </div>

                        <input type="file" id="file-input" name="files[]" accept=".jpg,.jpeg,.png,.pdf,.doc,.docx" style="display: none;" multiple>
                    </div>

                    <div class="file-list-container" id="file-list-container" style="display: none;">
                        <h3><i class="fas fa-list"></i> 已拍摄照片 <small>(可拖动调整顺序)</small></h3>
                        <div id="file-list" class="sortable-file-list"></div>
                        <button type="button" id="add-more-photos-btn" class="add-more-btn">
                            <i class="fas fa-plus-circle"></i>
                            继续拍照
                        </button>
                    </div>

                    <div class="options">
                        <div class="option-group" style="display: none;">
                            <label>
                                <i class="fas fa-cogs"></i>
                                OCR引擎：
                            </label>
                            <select name="ocr_engine" id="ocr-engine">
                                <option value="baidu" {% if default_ocr_engine == 'baidu' %}selected{% endif %}>百度云OCR</option>
                                <option value="tesseract" {% if default_ocr_engine == 'tesseract' %}selected{% endif %}>Tesseract OCR</option>
                                <option value="olm" {% if default_ocr_engine == 'olm' %}selected{% endif %}>OLM OCR</option>
                                <option value="paddleocr" {% if default_ocr_engine == 'paddle' or default_ocr_engine == 'paddleocr' %}selected{% endif %}>PaddleOCR</option>
                            </select>
                        </div>

                        <!-- 隐藏字段，始终使用手动模式 -->
                        <input type="hidden" id="classification-mode-hidden" name="classification_mode" value="manual">
                    </div>

                    <button type="submit" id="submit-btn" class="submit-btn" disabled>
                        <i class="fas fa-play"></i>
                        开始处理
                    </button>
                </form>
            </div>

            <div class="progress-container" id="progress-container" style="display: none;">
                <div class="progress-bar">
                    <div class="progress" id="progress-bar"></div>
                </div>
                <p id="progress-text">处理中...</p>
                <div class="status-info">
                    <p><i class="fas fa-info-circle"></i> 当前状态: <span id="status-stage">准备就绪</span></p>
                </div>
            </div>


        </main>
    </div>

    <!-- 二维码扫描对话框 -->
    <div id="qr-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-qrcode"></i> 扫码上传</h3>
                <span class="close-btn">&times;</span>
            </div>
            <div class="modal-scroll-container">
                <div class="modal-body">
                    <div class="qr-code-container">
                        <img id="qr-code-image" src="" alt="二维码">
                    </div>
                    <div class="qr-code-instructions">
                        <p>1. 使用手机扫描上方二维码</p>
                        <p>2. 在手机上选择要上传的图片</p>
                        <p>3. 确认上传后，图片将自动出现在此页面</p>
                    </div>
                    <div class="session-info">
                        <p>会话ID: <span id="session-id">{{ session_id }}</span></p>
                        <p>连接状态: <span id="connection-status">等待连接...</span></p>
                    </div>
                </div>
                <div class="mobile-files-container" id="mobile-files-container" style="display: none;">
                    <h4>已接收的文件</h4>
                    <div id="mobile-files-list" class="mobile-files-list"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='lib/sortablejs/Sortable.min.js') }}"></script>
    <script src="{{ url_for('static', filename='lib/socket.io.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/touchscreen.js') }}"></script>
</body>
</html>
