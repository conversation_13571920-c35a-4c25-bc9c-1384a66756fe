<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ template_name }} - 在线编辑</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/shared-navigation.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='lib/fontawesome/css/all.min.css') }}">
    <style>
/* 基础样式 */
body {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-title {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 28px;
  font-weight: 600;
}

/* 文档表单样式 */
.document-form {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* 文档结构表单样式 */
.document-form-section {
  margin-top: 20px;
}

.form-title {
  background: #f5f5f5;
  margin: 0 0 20px 0;
  padding: 15px 20px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
}

.document-content {
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 30px;
  font-family: "Microsoft YaHei", "SimSun", serif;
  line-height: 1.8;
  font-size: 14px;
}

/* 文档段落样式 */
.document-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin: 20px 0;
  line-height: 1.5;
}

.document-paragraph {
  margin: 15px 0;
  text-indent: 2em;
  line-height: 1.8;
}

/* 文档表格样式 */
.document-table {
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
  border: 1px solid #000;
}

.document-table td {
  border: 1px solid #000;
  padding: 8px 12px;
  vertical-align: top;
  line-height: 1.6;
}

/* 多行输入框样式 */
.inline-textarea, .cell-textarea {
  border: 1px solid #007bff;
  border-radius: 3px;
  padding: 4px 6px;
  font-size: inherit;
  font-family: inherit;
  color: #007bff;
  font-weight: 500;
  resize: both;
  min-width: 120px;
  min-height: 24px;
  background: rgba(0, 123, 255, 0.02);
  line-height: 1.4;
  overflow: hidden;
  transition: border-color 0.3s, background-color 0.3s;
}

.inline-textarea:focus, .cell-textarea:focus {
  outline: none;
  border-color: #0056b3;
  background: rgba(0, 123, 255, 0.05);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
}

/* 自动调整高度的样式 */
.auto-resize {
  overflow-y: hidden;
  resize: both;
}

/* 段落中的多行输入框 */
.document-paragraph .inline-textarea {
  min-width: 150px;
  max-width: calc(100% - 20px);
  width: auto;
}

/* 表格中的多行输入框 */
.document-table .cell-textarea {
  width: 100%;
  min-width: 80px;
  max-width: 100%;
  box-sizing: border-box;
}

/* 表格单元格样式调整 */
.document-table td {
  position: relative;
  overflow: visible;
}

/* 防止输入框超出表格边界 */
.document-table .cell-textarea {
  max-width: calc(100% - 8px);
}

/* 复选框样式 */
.inline-checkbox, .cell-checkbox {
  margin: 0 3px;
  transform: scale(1.2);
  accent-color: #007bff;
}

/* 表单按钮样式 */
.form-actions {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
  text-align: center;
  display: flex;
  gap: 15px;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-outline {
  background: transparent;
  color: #6c757d;
  border: 1px solid #6c757d;
}

.btn-outline:hover {
  background: #6c757d;
  color: white;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
  border: 1px solid #ffc107;
}

.btn-warning:hover {
  background: #e0a800;
  color: #212529;
  border: 1px solid #e0a800;
}

/* 实时保存指示器 */
.real-time-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(40, 167, 69, 0.9);
  color: white;
  padding: 10px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  z-index: 1000;
  display: none;
}

.real-time-indicator.saving {
  background: rgba(255, 193, 7, 0.9);
}

/* 加载和提示样式 */
.loading {
  display: none;
  text-align: center;
  padding: 30px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  margin: 20px 0;
}

.loading .spinner-border {
  width: 3rem;
  height: 3rem;
  color: #007bff;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.alert {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
  padding: 15px;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .document-form {
    padding: 15px;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .page-title {
    font-size: 1.5rem;
  }
}



    </style>
</head>
<body>
    <!-- 侧边导航 - 仅在触摸屏模式下显示 -->
    <nav class="side-navigation" id="touchscreen-nav" style="display: none;">
        <div class="nav-buttons">
            <a href="/touchscreen" class="nav-button">
                <i class="fas fa-file-alt"></i>
                <span>诉状转换</span>
            </a>
            <a href="/calculator" class="nav-button">
                <i class="fas fa-calculator"></i>
                <span>司法计算器</span>
            </a>
            <a href="/mediator" class="nav-button">
                <i class="fas fa-handshake"></i>
                <span>多元调解</span>
            </a>
        </div>
    </nav>

    <div class="container">
        <!-- 页面标题 -->
        <h1 class="page-title">{{ template_name }} - 在线编辑</h1>

        <!-- 实时保存指示器 -->
        <div id="realTimeIndicator" class="real-time-indicator">
            ✅ 已保存
        </div>
        
        <!-- 加载状态 -->
        <div id="loading" class="loading">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">处理中...</span>
            </div>
            <p class="mt-3">正在处理，请稍候...</p>
        </div>
        
        <!-- 错误提示 -->
        <div id="errorAlert" class="alert alert-danger" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorMessage"></span>
        </div>
        
        <!-- 成功提示 -->
        <div id="successAlert" class="alert alert-success" style="display: none;">
            <i class="fas fa-check-circle"></i>
            <span id="successMessage"></span>
        </div>
        
        <!-- 表单内容 -->
        <div id="formContainer">
            {{ form_html|safe }}
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 全局变量
        const previewId = '{{ preview_id }}';
        const originalFormData = {{ form_data|safe }};
        let currentFormData = { ...originalFormData };
        let autoSaveTimer = null;

        // 检测是否来自触摸屏
        const isTouchscreen = sessionStorage.getItem('source') === 'touchscreen' ||
                             sessionStorage.getItem('touchscreen_mode') === 'true' ||
                             window.location.search.includes('touchscreen=true') ||
                             document.referrer.includes('touchscreen');

        console.log('编辑页面来源检测:', {
            sessionSource: sessionStorage.getItem('source'),
            touchscreenMode: sessionStorage.getItem('touchscreen_mode'),
            referrer: document.referrer,
            isTouchscreen: isTouchscreen
        });
        
        // DOM元素
        const loading = document.getElementById('loading');
        const errorAlert = document.getElementById('errorAlert');
        const successAlert = document.getElementById('successAlert');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        const realTimeIndicator = document.getElementById('realTimeIndicator');
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 如果来自触摸屏，加载触摸屏功能
            if (isTouchscreen) {
                loadTouchscreenFeatures();
            }

            initializeForm();
            bindEvents();
            startAutoSave();
        });
        
        function initializeForm() {
            // 自动调整文本域高度
            const textareas = document.querySelectorAll('.auto-resize');
            textareas.forEach(textarea => {
                autoResize(textarea);
                textarea.addEventListener('input', () => {
                    autoResize(textarea);
                    scheduleAutoSave();
                });
            });
            
            // 监听所有表单字段变化
            const formFields = document.querySelectorAll('.form-control');
            formFields.forEach(field => {
                field.addEventListener('input', scheduleAutoSave);
                field.addEventListener('change', scheduleAutoSave);
            });
        }
        
        function bindEvents() {
            // 保存并生成按钮
            const generateBtn = document.getElementById('generateBtn');
            if (generateBtn) {
                generateBtn.addEventListener('click', generateDocument);
            }

            // 重置识别结果按钮
            const resetBtn = document.getElementById('resetBtn');
            if (resetBtn) {
                resetBtn.addEventListener('click', resetToOriginal);
            }
        }
        
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            // 计算最小高度：触摸屏模式下使用较小的最小高度
            const minHeight = isTouchscreen ? 32 : 24; // 约1行文字的高度
            textarea.style.height = Math.max(textarea.scrollHeight, minHeight) + 'px';
        }
        
        function scheduleAutoSave() {
            if (autoSaveTimer) {
                clearTimeout(autoSaveTimer);
            }
            
            showRealTimeIndicator('saving');
            
            autoSaveTimer = setTimeout(() => {
                saveFormData();
            }, 1000); // 1秒后自动保存
        }
        
        function startAutoSave() {
            // 每30秒自动保存一次
            setInterval(() => {
                saveFormData();
            }, 30000);
        }
        
        function collectFormData() {
            const form = document.getElementById('documentForm');
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // 处理复选框
            const checkboxes = form.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                data[checkbox.name] = checkbox.checked;
            });
            
            return data;
        }
        
        function saveFormData() {
            currentFormData = collectFormData();
            showRealTimeIndicator('saved');
        }
        
        function showRealTimeIndicator(type) {
            realTimeIndicator.className = 'real-time-indicator';

            if (type === 'saving') {
                realTimeIndicator.classList.add('saving');
                realTimeIndicator.innerHTML = '💾 保存中...';
            } else {
                realTimeIndicator.innerHTML = '✅ 已保存';
            }

            realTimeIndicator.style.display = 'block';

            setTimeout(() => {
                realTimeIndicator.style.display = 'none';
            }, 2000);
        }
        
        function showLoading() {
            loading.style.display = 'block';
            hideAlerts();
        }
        
        function hideLoading() {
            loading.style.display = 'none';
        }
        
        function showError(message) {
            errorMessage.textContent = message;
            errorAlert.style.display = 'block';
            successAlert.style.display = 'none';
            hideLoading();
        }
        
        function showSuccess(message) {
            successMessage.textContent = message;
            successAlert.style.display = 'block';
            errorAlert.style.display = 'none';
            hideLoading();
        }
        
        function hideAlerts() {
            errorAlert.style.display = 'none';
            successAlert.style.display = 'none';
        }

        // 触摸屏功能模块
        function loadTouchscreenFeatures() {
            console.log('加载触摸屏功能模块');

            // 添加触摸屏样式类
            document.body.classList.add('touchscreen-mode');

            // 禁用右键菜单
            disableContextMenu();

            // 添加触摸屏导航
            addTouchscreenNavigation();

            // 修改结果处理逻辑
            modifyResultHandling();

            // 添加触摸屏样式
            addTouchscreenStyles();
        }

        function disableContextMenu() {
            // 禁用右键菜单
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });

            // 禁用文本选择
            document.body.style.webkitUserSelect = 'none';
            document.body.style.mozUserSelect = 'none';
            document.body.style.msUserSelect = 'none';
            document.body.style.userSelect = 'none';

            // 移除触摸高亮效果
            document.body.style.webkitTapHighlightColor = 'transparent';
        }

        function addTouchscreenNavigation() {
            // 显示触摸屏导航栏
            const nav = document.getElementById('touchscreen-nav');
            if (nav) {
                nav.style.display = 'flex';
            }

            // 调整主容器的左边距
            const container = document.querySelector('.container');
            if (container) {
                container.style.marginLeft = '220px';
            }
        }

        function addTouchscreenStyles() {
            // 添加触摸屏专用样式
            const style = document.createElement('style');
            style.textContent = `
                /* 触摸屏模式样式 */
                .touchscreen-mode {
                    -webkit-touch-callout: none;
                    -webkit-user-select: none;
                    -khtml-user-select: none;
                    -moz-user-select: none;
                    -ms-user-select: none;
                    user-select: none;
                    -webkit-tap-highlight-color: transparent;
                }

                .touchscreen-mode .btn {
                    min-height: 60px;
                    font-size: 18px;
                    padding: 20px 40px;
                    margin: 10px 5px;
                    min-width: 180px;
                }

                .touchscreen-mode .form-actions {
                    position: fixed;
                    right: 20px;
                    top: 50%;
                    transform: translateY(-50%);
                    flex-direction: column;
                    gap: 20px;
                    z-index: 999;
                    border-top: none;
                    margin-top: 0;
                    padding-top: 0;
                }

                .touchscreen-mode .inline-textarea,
                .touchscreen-mode .cell-textarea {
                    min-height: 32px;
                    font-size: 16px;
                    padding: 6px 10px;
                    line-height: 1.4;
                }

                .touchscreen-mode .page-title {
                    font-size: 32px;
                    margin-bottom: 40px;
                }

                /* 侧边导航样式已移至共享文件 */
            `;
            document.head.appendChild(style);
        }

        function modifyResultHandling() {
            // 修改生成文档的处理逻辑，添加触摸屏特有的结果显示
            console.log('修改结果处理逻辑为触摸屏模式');
        }

        // 触摸屏专用的通知函数
        function showTouchscreenNotification(title, message, autoClose = true, autoCloseDelay = 3000, customButtons = null) {
            // 创建遮罩层
            const overlay = document.createElement('div');
            overlay.className = 'notification-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 10000;
                display: flex;
                justify-content: center;
                align-items: center;
            `;

            // 创建通知框
            const notification = document.createElement('div');
            notification.className = 'notification-box';
            notification.style.cssText = `
                background-color: white;
                border-radius: 15px;
                padding: 30px;
                max-width: 400px;
                width: 80%;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                animation: slideIn 0.3s ease-out;
            `;

            // 创建标题和消息
            const titleEl = document.createElement('h3');
            titleEl.style.cssText = 'margin: 0 0 15px 0; color: #333; font-size: 20px;';
            titleEl.textContent = title;

            const messageEl = document.createElement('p');
            messageEl.style.cssText = 'margin: 0 0 20px 0; color: #666; font-size: 16px; line-height: 1.5;';
            messageEl.textContent = message;

            notification.appendChild(titleEl);
            notification.appendChild(messageEl);

            // 创建按钮容器
            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = 'display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;';

            // 如果有自定义按钮，使用自定义按钮
            if (customButtons && Array.isArray(customButtons)) {
                customButtons.forEach(buttonConfig => {
                    const btn = document.createElement('button');
                    btn.innerHTML = buttonConfig.text;
                    btn.style.cssText = `
                        background-color: ${buttonConfig.className && buttonConfig.className.includes('download-btn') ? '#28a745' : '#4a90e2'};
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 16px;
                        cursor: pointer;
                        min-width: 100px;
                        margin: 0 5px;
                    `;
                    btn.addEventListener('click', function() {
                        if (buttonConfig.onClick) {
                            buttonConfig.onClick();
                        }
                        if (buttonConfig.closeOnClick !== false) {
                            overlay.remove();
                        }
                    });
                    buttonContainer.appendChild(btn);
                });
            } else {
                // 创建默认确认按钮
                const confirmBtn = document.createElement('button');
                confirmBtn.textContent = '确定';
                confirmBtn.style.cssText = `
                    background-color: #4a90e2;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    font-size: 16px;
                    cursor: pointer;
                    min-width: 100px;
                `;
                confirmBtn.addEventListener('click', function() {
                    overlay.remove();
                });
                buttonContainer.appendChild(confirmBtn);
            }

            notification.appendChild(buttonContainer);
            overlay.appendChild(notification);
            document.body.appendChild(overlay);

            // 自动关闭
            if (autoClose) {
                setTimeout(() => {
                    if (overlay.parentNode) {
                        overlay.remove();
                    }
                }, autoCloseDelay);
            }

            // 添加动画样式
            if (!document.querySelector('#notification-styles')) {
                const animationStyle = document.createElement('style');
                animationStyle.id = 'notification-styles';
                animationStyle.textContent = `
                    @keyframes slideIn {
                        from {
                            transform: translateY(-50px);
                            opacity: 0;
                        }
                        to {
                            transform: translateY(0);
                            opacity: 1;
                        }
                    }
                `;
                document.head.appendChild(animationStyle);
            }

            return () => overlay.remove();
        }
        
        async function generateDocument() {
            showLoading();

            try {
                const data = collectFormData();

                const response = await fetch('/api/generate-from-preview', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        preview_id: previewId,
                        form_data: data
                    })
                });

                const result = await response.json();

                if (result.success) {
                    hideLoading();

                    if (isTouchscreen) {
                        // 触摸屏模式：显示结果操作界面
                        showTouchscreenResult(result);
                    } else {
                        // 普通模式：直接下载
                        showSuccess('文档生成成功！正在下载...');
                        window.location.href = result.download_url;
                    }
                } else {
                    showError(result.error || '生成文档失败');
                }
            } catch (error) {
                showError('生成文档时发生错误: ' + error.message);
            }
        }

        function showTouchscreenResult(result) {
            // 隐藏表单内容
            const formContainer = document.getElementById('formContainer');
            if (formContainer) {
                formContainer.style.display = 'none';
            }

            // 创建结果界面
            const resultContainer = document.createElement('div');
            resultContainer.id = 'touchscreen-result';
            resultContainer.style.cssText = `
                text-align: center;
                padding: 40px;
                background: white;
                border-radius: 15px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                margin: 20px 0;
            `;

            resultContainer.innerHTML = `
                <div style="margin-bottom: 30px;">
                    <i class="fas fa-check-circle" style="font-size: 60px; color: #28a745; margin-bottom: 20px;"></i>
                    <h2 style="color: #333; margin-bottom: 10px;">文档生成成功！</h2>
                    <p style="color: #666; font-size: 16px;">请选择您需要的操作</p>
                </div>

                <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap;">
                    <button id="print-btn" class="btn btn-primary" style="min-width: 150px; background: #17a2b8;">
                        <i class="fas fa-print"></i> 打印文档
                    </button>
                    <button id="restart-btn" class="btn btn-outline" style="min-width: 150px;">
                        <i class="fas fa-redo"></i> 重新开始
                    </button>
                </div>

                <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                    <p style="margin: 0; color: #666; font-size: 14px;">
                        <i class="fas fa-clock"></i>
                        系统将在 <span id="countdown-timer" style="color: #dc3545; font-weight: bold;">300</span> 秒后自动清理数据并返回首页
                    </p>
                </div>
            `;

            // 插入结果界面
            const container = document.querySelector('.container');
            container.appendChild(resultContainer);

            // 绑定按钮事件
            bindTouchscreenResultEvents(result);

            // 启动倒计时
            startTouchscreenCountdown(300);
        }

        function bindTouchscreenResultEvents(result) {
            // 打印按钮
            const printBtn = document.getElementById('print-btn');
            if (printBtn) {
                printBtn.addEventListener('click', function() {
                    printDocument(result.download_url);
                });
            }

            // 重新开始按钮
            const restartBtn = document.getElementById('restart-btn');
            if (restartBtn) {
                restartBtn.addEventListener('click', function() {
                    restartTouchscreenSession();
                });
            }
        }

        function printDocument(downloadUrl) {
            const printBtn = document.getElementById('print-btn');
            if (!printBtn) return;

            // 显示打印中状态
            printBtn.disabled = true;
            printBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 打印中...';

            // 从下载URL中提取文件路径
            const filepath = downloadUrl.replace('/download/', '');

            // 调用打印API
            fetch(`/print-document/${filepath}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showTouchscreenNotification('打印成功', '文档已发送到打印机，请到打印机处取件');
                } else {
                    console.error('打印失败:', data);
                    if (data.message && data.message.includes('未找到默认打印机')) {
                        // 打印机不可用时，显示带下载按钮的通知
                        const customButtons = [
                            {
                                text: '<i class="fas fa-download"></i> 下载文档',
                                className: 'download-btn',
                                onClick: function() {
                                    // 直接下载文档
                                    window.open(downloadUrl, '_blank');
                                }
                            },
                            {
                                text: '确定',
                                className: 'confirm-btn',
                                onClick: null
                            }
                        ];
                        showTouchscreenNotification('打印失败', '系统未配置打印机，您可以下载文档到本地', false, 0, customButtons);
                    } else {
                        showTouchscreenNotification('打印失败', data.message || '打印失败，请重试', false);
                    }
                }
            })
            .catch(error => {
                console.error('打印请求出错:', error);
                showTouchscreenNotification('打印错误', '打印请求出错，请稍后重试', false);
            })
            .finally(() => {
                // 恢复按钮状态
                printBtn.disabled = false;
                printBtn.innerHTML = '<i class="fas fa-print"></i> 打印文档';
            });
        }

        function restartTouchscreenSession() {
            const restartBtn = document.getElementById('restart-btn');
            if (!restartBtn) return;

            // 显示清理中状态
            restartBtn.disabled = true;
            restartBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在清理...';

            // 清除倒计时
            clearTouchscreenCountdown();

            // 调用清理API
            fetch('/clean-temp-files', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('临时文件清理成功:', data.message);
                } else {
                    console.error('临时文件清理失败:', data.message);
                }
            })
            .catch(error => {
                console.error('清理请求出错:', error);
            })
            .finally(() => {
                // 清除会话存储
                sessionStorage.removeItem('source');
                sessionStorage.removeItem('touchscreen_mode');

                // 跳转回触摸屏首页
                window.location.href = '/touchscreen';
            });
        }

        let touchscreenCountdownTimer = null;

        function startTouchscreenCountdown(seconds) {
            // 清除可能存在的旧倒计时
            clearTouchscreenCountdown();

            // 获取倒计时显示元素
            const countdownElement = document.getElementById('countdown-timer');
            if (!countdownElement) return;

            // 设置初始时间
            let remainingTime = seconds;
            countdownElement.textContent = remainingTime;

            // 创建倒计时定时器
            touchscreenCountdownTimer = setInterval(function() {
                remainingTime--;

                // 更新显示
                if (countdownElement) {
                    countdownElement.textContent = remainingTime;
                }

                // 检查是否倒计时结束
                if (remainingTime <= 0) {
                    clearInterval(touchscreenCountdownTimer);

                    console.log('倒计时结束，自动清理并返回首页');

                    // 调用清理API
                    fetch('/clean-temp-files', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            console.log('临时文件清理成功:', data.message);
                        } else {
                            console.error('临时文件清理失败:', data.message);
                        }
                    })
                    .catch(error => {
                        console.error('清理请求出错:', error);
                    })
                    .finally(() => {
                        // 清除会话存储
                        sessionStorage.removeItem('source');
                        sessionStorage.removeItem('touchscreen_mode');

                        // 跳转回触摸屏首页
                        window.location.href = '/touchscreen';
                    });
                }
            }, 1000);
        }

        function clearTouchscreenCountdown() {
            if (touchscreenCountdownTimer) {
                clearInterval(touchscreenCountdownTimer);
                touchscreenCountdownTimer = null;
            }
        }
        

        
        function resetToOriginal() {
            if (!confirm('确定要重置所有修改，恢复到AI分析的原始结果吗？')) {
                return;
            }
            
            // 重置表单数据
            const form = document.getElementById('documentForm');
            
            for (const [key, value] of Object.entries(originalFormData)) {
                const field = form.querySelector(`[name="${key}"]`);
                if (field) {
                    if (field.type === 'checkbox') {
                        field.checked = Boolean(value);
                    } else {
                        field.value = value || '';
                        if (field.classList.contains('auto-resize')) {
                            autoResize(field);
                        }
                    }
                }
            }
            
            showSuccess('已重置为AI分析的原始结果');
        }
    </script>
</body>
</html>
