@socketio.on('upload_response')
def handle_upload_response(data):
    """处理上传响应"""
    session_id = data.get('session_id')
    
    if session_id:
        # 将响应转发给移动端
        emit('upload_response', {
            'success': data.get('success', False),
            'message': data.get('message', '')
        }, room=session_id, skip_sid=request.sid)

# 不再使用这个函数处理移动端上传的响应
@socketio.on('pc_received_file')
