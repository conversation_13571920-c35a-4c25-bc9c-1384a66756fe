"""
预览数据存储器
用于临时存储LLM处理结果，支持预览编辑功能
"""

import os
import json
import time
import uuid
import logging
import threading
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class PreviewDataStorage:
    """
    预览数据存储器
    
    功能：
    1. 临时存储LLM处理结果
    2. 支持数据的保存、获取和清理
    3. 支持文件系统和内存存储
    4. 自动清理过期数据
    """
    
    def __init__(self, storage_type: str = 'file', storage_dir: str = './temp/preview', ttl: int = 3600):
        """
        初始化存储器
        
        Args:
            storage_type (str): 存储类型，'file' 或 'memory'
            storage_dir (str): 文件存储目录（仅文件存储模式）
            ttl (int): 数据过期时间（秒），默认1小时
        """
        self.storage_type = storage_type
        self.storage_dir = storage_dir
        self.ttl = ttl
        self.logger = logging.getLogger('utils.preview_storage')
        
        # 内存存储（用于memory模式）
        self._memory_storage = {}
        self._memory_timestamps = {}
        self._lock = threading.Lock()
        
        # 初始化文件存储目录
        if self.storage_type == 'file':
            os.makedirs(self.storage_dir, exist_ok=True)
            self.logger.info(f"预览数据存储初始化完成，存储目录: {self.storage_dir}")
        else:
            self.logger.info("预览数据存储初始化完成，使用内存存储")
    
    def save_preview_data(self, formatted_text: Dict[str, Any], template_path: str, file_id: str) -> str:
        """
        保存预览数据
        
        Args:
            formatted_text (dict): LLM处理结果
            template_path (str): 模板文件路径
            file_id (str): 原始文件ID
            
        Returns:
            str: 预览数据ID
        """
        # 生成唯一的预览ID
        preview_id = str(uuid.uuid4())
        
        # 构建预览数据
        preview_data = {
            'preview_id': preview_id,
            'file_id': file_id,
            'template_path': template_path,
            'formatted_text': formatted_text,
            'created_at': datetime.now().isoformat(),
            'expires_at': (datetime.now() + timedelta(seconds=self.ttl)).isoformat()
        }
        
        try:
            if self.storage_type == 'file':
                self._save_to_file(preview_id, preview_data)
            else:
                self._save_to_memory(preview_id, preview_data)
            
            self.logger.info(f"预览数据保存成功，ID: {preview_id}")
            return preview_id
            
        except Exception as e:
            self.logger.error(f"保存预览数据失败: {str(e)}")
            raise
    
    def get_preview_data(self, preview_id: str) -> Optional[Dict[str, Any]]:
        """
        获取预览数据
        
        Args:
            preview_id (str): 预览数据ID
            
        Returns:
            dict: 预览数据，如果不存在或已过期返回None
        """
        try:
            if self.storage_type == 'file':
                data = self._get_from_file(preview_id)
            else:
                data = self._get_from_memory(preview_id)
            
            if data is None:
                self.logger.warning(f"预览数据不存在: {preview_id}")
                return None
            
            # 检查是否过期
            expires_at = datetime.fromisoformat(data['expires_at'])
            if datetime.now() > expires_at:
                self.logger.warning(f"预览数据已过期: {preview_id}")
                self.clear_preview_data(preview_id)
                return None
            
            self.logger.info(f"预览数据获取成功: {preview_id}")
            return data
            
        except Exception as e:
            self.logger.error(f"获取预览数据失败: {str(e)}")
            return None
    
    def clear_preview_data(self, preview_id: str) -> bool:
        """
        清理预览数据
        
        Args:
            preview_id (str): 预览数据ID
            
        Returns:
            bool: 清理是否成功
        """
        try:
            if self.storage_type == 'file':
                success = self._clear_from_file(preview_id)
            else:
                success = self._clear_from_memory(preview_id)
            
            if success:
                self.logger.info(f"预览数据清理成功: {preview_id}")
            else:
                self.logger.warning(f"预览数据清理失败，数据不存在: {preview_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"清理预览数据失败: {str(e)}")
            return False
    
    def cleanup_expired_data(self) -> int:
        """
        清理所有过期数据
        
        Returns:
            int: 清理的数据数量
        """
        cleaned_count = 0
        
        try:
            if self.storage_type == 'file':
                cleaned_count = self._cleanup_expired_files()
            else:
                cleaned_count = self._cleanup_expired_memory()
            
            if cleaned_count > 0:
                self.logger.info(f"清理过期预览数据完成，清理数量: {cleaned_count}")
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"清理过期数据失败: {str(e)}")
            return 0
    
    def _save_to_file(self, preview_id: str, data: Dict[str, Any]):
        """保存数据到文件"""
        file_path = os.path.join(self.storage_dir, f"{preview_id}.json")
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def _get_from_file(self, preview_id: str) -> Optional[Dict[str, Any]]:
        """从文件获取数据"""
        file_path = os.path.join(self.storage_dir, f"{preview_id}.json")
        if not os.path.exists(file_path):
            return None
        
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _clear_from_file(self, preview_id: str) -> bool:
        """从文件清理数据"""
        file_path = os.path.join(self.storage_dir, f"{preview_id}.json")
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    
    def _cleanup_expired_files(self) -> int:
        """清理过期文件"""
        cleaned_count = 0
        current_time = datetime.now()
        
        for filename in os.listdir(self.storage_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(self.storage_dir, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    expires_at = datetime.fromisoformat(data['expires_at'])
                    if current_time > expires_at:
                        os.remove(file_path)
                        cleaned_count += 1
                        
                except Exception as e:
                    self.logger.warning(f"清理文件时出错 {filename}: {str(e)}")
        
        return cleaned_count
    
    def _save_to_memory(self, preview_id: str, data: Dict[str, Any]):
        """保存数据到内存"""
        with self._lock:
            self._memory_storage[preview_id] = data
            self._memory_timestamps[preview_id] = time.time()
    
    def _get_from_memory(self, preview_id: str) -> Optional[Dict[str, Any]]:
        """从内存获取数据"""
        with self._lock:
            return self._memory_storage.get(preview_id)
    
    def _clear_from_memory(self, preview_id: str) -> bool:
        """从内存清理数据"""
        with self._lock:
            if preview_id in self._memory_storage:
                del self._memory_storage[preview_id]
                if preview_id in self._memory_timestamps:
                    del self._memory_timestamps[preview_id]
                return True
            return False
    
    def _cleanup_expired_memory(self) -> int:
        """清理过期内存数据"""
        cleaned_count = 0
        current_time = datetime.now()
        
        with self._lock:
            expired_ids = []
            for preview_id, data in self._memory_storage.items():
                try:
                    expires_at = datetime.fromisoformat(data['expires_at'])
                    if current_time > expires_at:
                        expired_ids.append(preview_id)
                except Exception as e:
                    self.logger.warning(f"检查过期时间时出错 {preview_id}: {str(e)}")
                    expired_ids.append(preview_id)  # 出错的数据也清理掉
            
            for preview_id in expired_ids:
                if preview_id in self._memory_storage:
                    del self._memory_storage[preview_id]
                if preview_id in self._memory_timestamps:
                    del self._memory_timestamps[preview_id]
                cleaned_count += 1
        
        return cleaned_count
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        获取存储统计信息
        
        Returns:
            dict: 存储统计信息
        """
        stats = {
            'storage_type': self.storage_type,
            'ttl': self.ttl,
            'total_count': 0,
            'expired_count': 0
        }
        
        try:
            current_time = datetime.now()
            
            if self.storage_type == 'file':
                if os.path.exists(self.storage_dir):
                    files = [f for f in os.listdir(self.storage_dir) if f.endswith('.json')]
                    stats['total_count'] = len(files)
                    
                    for filename in files:
                        file_path = os.path.join(self.storage_dir, filename)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            expires_at = datetime.fromisoformat(data['expires_at'])
                            if current_time > expires_at:
                                stats['expired_count'] += 1
                        except:
                            stats['expired_count'] += 1
            else:
                with self._lock:
                    stats['total_count'] = len(self._memory_storage)
                    for data in self._memory_storage.values():
                        try:
                            expires_at = datetime.fromisoformat(data['expires_at'])
                            if current_time > expires_at:
                                stats['expired_count'] += 1
                        except:
                            stats['expired_count'] += 1
            
        except Exception as e:
            self.logger.error(f"获取存储统计信息失败: {str(e)}")
        
        return stats


# 全局存储实例
_preview_storage = None


def get_preview_storage() -> PreviewDataStorage:
    """
    获取全局预览存储实例
    
    Returns:
        PreviewDataStorage: 预览存储实例
    """
    global _preview_storage
    
    if _preview_storage is None:
        # 从环境变量获取配置
        storage_type = os.environ.get('PREVIEW_STORAGE_TYPE', 'file')
        storage_dir = os.environ.get('PREVIEW_STORAGE_DIR', './temp/preview')
        ttl = int(os.environ.get('PREVIEW_DATA_TTL', '3600'))
        
        _preview_storage = PreviewDataStorage(
            storage_type=storage_type,
            storage_dir=storage_dir,
            ttl=ttl
        )
    
    return _preview_storage
