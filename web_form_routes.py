"""
Web表单相关的Flask路由
提供在线表单编辑和文档生成的API接口
"""

import os
import json
import logging
from flask import Flask, request, jsonify, render_template, send_from_directory
from document.web_form_processor import WebFormProcessor
from document.web_form_analyzer import Template<PERSON><PERSON>yzer
from document.web_form_generator import WebFormGenerator

logger = logging.getLogger(__name__)

def register_web_form_routes(app: Flask):
    """注册Web表单相关的路由"""
    
    @app.route('/web-form/<template_name>')
    def show_web_form(template_name):
        """显示Web表单页面"""
        try:
            # 构建模板路径
            template_path = os.path.join(app.static_folder, 'templates', template_name)
            
            if not os.path.exists(template_path):
                return f"模板文件不存在: {template_name}", 404
            
            # 检查是否已有生成的表单页面
            form_page_name = template_name.replace('.docx', '_form.html')
            form_page_path = os.path.join(app.static_folder, 'form_pages', form_page_name)
            
            if not os.path.exists(form_page_path):
                # 动态生成表单页面
                generator = WebFormGenerator(template_path)
                os.makedirs(os.path.dirname(form_page_path), exist_ok=True)
                generator.save_form_page(form_page_path, f"{template_name} - 在线编辑")
            
            # 返回表单页面
            return send_from_directory(
                directory=os.path.join(app.static_folder, 'form_pages'),
                path=form_page_name
            )
            
        except Exception as e:
            logger.error(f"显示Web表单时出错: {str(e)}")
            return f"加载表单失败: {str(e)}", 500
    
    @app.route('/api/analyze-template', methods=['POST'])
    def analyze_template():
        """分析模板API"""
        try:
            data = request.get_json()
            template_name = data.get('template_name')
            
            if not template_name:
                return jsonify({'error': '缺少模板名称'}), 400
            
            template_path = os.path.join(app.static_folder, 'templates', template_name)
            
            if not os.path.exists(template_path):
                return jsonify({'error': f'模板文件不存在: {template_name}'}), 404
            
            # 分析模板
            analyzer = TemplateAnalyzer(template_path)
            config = analyzer.generate_form_config()
            
            return jsonify({
                'success': True,
                'config': config,
                'statistics': {
                    'placeholders': len(analyzer.placeholders),
                    'checkboxes': len(analyzer.checkboxes),
                    'sections': len(config['form_sections'])
                }
            })
            
        except Exception as e:
            logger.error(f"分析模板时出错: {str(e)}")
            return jsonify({'error': f'分析失败: {str(e)}'}), 500
    
    @app.route('/api/preview-document', methods=['POST'])
    def preview_document():
        """预览文档API"""
        try:
            data = request.get_json()
            template_path = data.get('template_path')
            form_data = data.get('form_data', {})
            
            if not template_path:
                return jsonify({'error': '缺少模板路径'}), 400
            
            if not os.path.exists(template_path):
                return jsonify({'error': '模板文件不存在'}), 404
            
            # 创建表单处理器
            processor = WebFormProcessor(template_path)
            
            # 验证表单数据
            validation_result = processor.validate_form_data(form_data)
            if not validation_result['valid']:
                return jsonify({
                    'error': '表单数据验证失败',
                    'validation_errors': validation_result['errors']
                }), 400
            
            # 生成预览HTML
            preview_html = processor.generate_preview_html(form_data)
            
            # 获取统计信息
            statistics = processor.get_form_statistics(form_data)
            
            return jsonify({
                'success': True,
                'preview_html': preview_html,
                'statistics': statistics
            })
            
        except Exception as e:
            logger.error(f"预览文档时出错: {str(e)}")
            return jsonify({'error': f'预览失败: {str(e)}'}), 500
    
    @app.route('/api/generate-document', methods=['POST'])
    def generate_document():
        """生成文档API"""
        try:
            data = request.get_json()
            template_path = data.get('template_path')
            form_data = data.get('form_data', {})
            
            if not template_path:
                return jsonify({'error': '缺少模板路径'}), 400
            
            if not os.path.exists(template_path):
                return jsonify({'error': '模板文件不存在'}), 404
            
            # 创建表单处理器
            processor = WebFormProcessor(template_path)
            
            # 验证表单数据
            validation_result = processor.validate_form_data(form_data)
            if not validation_result['valid']:
                return jsonify({
                    'error': '表单数据验证失败',
                    'validation_errors': validation_result['errors']
                }), 400
            
            # 生成文档
            download_dir = os.path.join(app.static_folder, 'downloads')
            os.makedirs(download_dir, exist_ok=True)
            
            output_path = processor.generate_document(form_data, download_dir)
            
            if output_path:
                # 生成下载URL
                filename = os.path.basename(output_path)
                download_url = f"/download/{filename}"
                
                # 获取统计信息
                statistics = processor.get_form_statistics(form_data)
                
                return jsonify({
                    'success': True,
                    'download_url': download_url,
                    'filename': filename,
                    'statistics': statistics
                })
            else:
                return jsonify({'error': '文档生成失败'}), 500
                
        except Exception as e:
            logger.error(f"生成文档时出错: {str(e)}")
            return jsonify({'error': f'生成失败: {str(e)}'}), 500
    
    @app.route('/api/validate-form', methods=['POST'])
    def validate_form():
        """验证表单数据API"""
        try:
            data = request.get_json()
            template_path = data.get('template_path')
            form_data = data.get('form_data', {})
            
            if not template_path:
                return jsonify({'error': '缺少模板路径'}), 400
            
            if not os.path.exists(template_path):
                return jsonify({'error': '模板文件不存在'}), 404
            
            # 创建表单处理器
            processor = WebFormProcessor(template_path)
            
            # 验证表单数据
            validation_result = processor.validate_form_data(form_data)
            
            # 获取统计信息
            statistics = processor.get_form_statistics(form_data)
            
            return jsonify({
                'success': True,
                'validation': validation_result,
                'statistics': statistics
            })
            
        except Exception as e:
            logger.error(f"验证表单时出错: {str(e)}")
            return jsonify({'error': f'验证失败: {str(e)}'}), 500
    
    @app.route('/api/list-templates')
    def list_templates():
        """获取可用模板列表API"""
        try:
            templates_dir = os.path.join(app.static_folder, 'templates')
            
            if not os.path.exists(templates_dir):
                return jsonify({'templates': []})
            
            templates = []
            for filename in os.listdir(templates_dir):
                if filename.endswith('.docx') and not filename.startswith('~'):
                    template_path = os.path.join(templates_dir, filename)
                    
                    # 获取模板基本信息
                    try:
                        analyzer = TemplateAnalyzer(template_path)
                        templates.append({
                            'name': filename,
                            'display_name': filename.replace('.docx', ''),
                            'placeholders_count': len(analyzer.placeholders),
                            'checkboxes_count': len(analyzer.checkboxes),
                            'web_form_url': f'/web-form/{filename}'
                        })
                    except Exception as e:
                        logger.warning(f"分析模板 {filename} 时出错: {str(e)}")
                        templates.append({
                            'name': filename,
                            'display_name': filename.replace('.docx', ''),
                            'placeholders_count': 0,
                            'checkboxes_count': 0,
                            'web_form_url': f'/web-form/{filename}',
                            'error': str(e)
                        })
            
            return jsonify({
                'success': True,
                'templates': templates,
                'total': len(templates)
            })
            
        except Exception as e:
            logger.error(f"获取模板列表时出错: {str(e)}")
            return jsonify({'error': f'获取模板列表失败: {str(e)}'}), 500
    
    @app.route('/web-forms')
    def web_forms_index():
        """Web表单首页"""
        try:
            # 获取模板列表
            templates_dir = os.path.join(app.static_folder, 'templates')
            templates = []
            
            if os.path.exists(templates_dir):
                for filename in os.listdir(templates_dir):
                    if filename.endswith('.docx') and not filename.startswith('~'):
                        templates.append({
                            'name': filename,
                            'display_name': filename.replace('.docx', '').replace('-', ' - '),
                            'url': f'/web-form/{filename}'
                        })
            
            # 渲染模板选择页面
            html = f'''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线表单编辑 - 起诉状生成系统</title>
    <style>
        body {{
            font-family: "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
        }}
        .templates-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }}
        .template-card {{
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }}
        .template-card:hover {{
            transform: translateY(-2px);
        }}
        .template-title {{
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }}
        .template-link {{
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.3s;
        }}
        .template-link:hover {{
            background: #0056b3;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>在线表单编辑</h1>
            <p>选择模板开始在线编辑起诉状</p>
        </div>
        
        <div class="templates-grid">
'''
            
            for template in templates:
                html += f'''
            <div class="template-card">
                <div class="template-title">{template['display_name']}</div>
                <a href="{template['url']}" class="template-link">开始编辑</a>
            </div>
'''
            
            html += '''
        </div>
    </div>
</body>
</html>'''
            
            return html
            
        except Exception as e:
            logger.error(f"显示Web表单首页时出错: {str(e)}")
            return f"加载失败: {str(e)}", 500
